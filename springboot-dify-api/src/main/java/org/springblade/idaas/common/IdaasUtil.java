package org.springblade.idaas.common;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.utils.HttpUtils;
import me.zhyd.oauth.utils.UrlBuilder;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * IDaaS工具类，用于处理IDaaS认证流程和获取用户信息
 */
@Slf4j
public class IdaasUtil {

    private static Map<String, JSONObject> tokens;

    /**
     * 获取访问令牌
     * @param clientId 客户端ID
     * @param clientSecret 客户端密钥
     * @param code 授权码
     * @param redirectUri 重定向URI
     * @return 访问令牌信息
     */
    public static JSONObject getAccessToken(String iDaasServer, String clientId, String clientSecret, String code, String redirectUri) {
        if (StrUtil.isBlank(code)) {
            return null;
        }
        
        // 根据不同的IDaaS提供商构建不同的请求
        String tokenUrl;

        tokenUrl = iDaasServer + "/oauth/token";

        String url = UrlBuilder.fromBaseUrl(tokenUrl)
            .queryParam("grant_type", "authorization_code")
            .queryParam("code", code)
            .queryParam("redirect_uri", URLEncoder.encode(redirectUri))
            .queryParam("client_id", clientId)
            //文档p5   随机值，若为门户发起，可自定义随机一个字符串，若为IDP发起，也是随机一个值，但会带上_idp后缀
            .queryParam("client_secret", clientSecret).build();
        System.out.println("获取token url" + url);

        String response = new HttpUtils().post(url).getBody();
        System.out.println("获取token" + response);
        JSONObject jsonObject = JSONObject.parseObject(response);
        IdaasUtil.checkResponse(jsonObject);
        return jsonObject;
    }
    
    /**
     * 获取用户信息
     * @param accessToken 访问令牌
     * @return 用户信息
     */
    public static JSONObject getUserInfo(String iDaasServer, String accessToken) {
        if (StrUtil.isBlank(accessToken)) {
            return null;
        }
        
        String userInfoUrl;
        userInfoUrl = iDaasServer + "/api/bff/v1.2/oauth2/userinfo";

        String url = UrlBuilder.fromBaseUrl(userInfoUrl).queryParam("access_token", accessToken).build();
        String userInfo = new HttpUtils().get(url).getBody();
        System.out.println("获取userInfo" + userInfo);

        JSONObject object = JSONObject.parseObject(userInfo);
        IdaasUtil.checkResponse(object);

        return object;
    }

    /**
     * 检查响应内容是否正确
     *
     * @param object 请求响应内容
     */
    public static void checkResponse(JSONObject object) {
        if (object.containsKey("error")) {
            throw new AuthException(object.getString("error_description"));
        }
    }
}
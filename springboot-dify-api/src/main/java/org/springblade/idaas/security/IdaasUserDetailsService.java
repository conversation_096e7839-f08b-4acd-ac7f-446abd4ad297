package org.springblade.idaas.security;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.gzyc.service.GzycUserService;
import org.springblade.gzyc.vo.GzycUserVO;
import org.springblade.idaas.common.IdaasUtil;
import org.springblade.modules.entity.Account;
import org.springblade.modules.entity.AccountIntegrates;
import org.springblade.modules.repository.AccountIntegratesRepository;
import org.springblade.modules.repository.AccountRepository;
import org.springblade.modules.service.AccountIntegratesService;
import org.springblade.modules.service.AccountService;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;

/**
 * IDaaS用户详情服务
 * 用于处理IDaaS用户的认证和用户信息获取
 */
@Slf4j
@Component
public class IdaasUserDetailsService implements UserDetailsService {

    @Value("${idaas.server-host}")
    String iDaasServer;

    @Value("${idaas.client-id:}")
    String clientId;
    
    @Value("${idaas.client-secret:}")
    String clientSecret;
    
    @Value("${idaas.redirect-uri:}")
    String redirectUri;

    @Resource
    private AccountIntegratesService accountIntegratesService;

    @Resource
    private AccountService accountService;

    @Resource
    private GzycUserService gzycUserService;

    @Override
    public UserDetails loadUserByUsername(String principal) throws UsernameNotFoundException {
        log.info(principal);
        JSONObject obj = JSON.parseObject(principal);
        String code = obj.getString("code");

        // 获取访问令牌
        JSONObject tokenInfo = IdaasUtil.getAccessToken(iDaasServer, clientId, clientSecret, code, redirectUri);
        if (tokenInfo == null) {
            throw new AuthenticationCredentialsNotFoundException("获取访问令牌失败");
        }
        
        // 根据不同提供商获取访问令牌
        String accessToken = tokenInfo.getString("access_token");

        // 获取用户信息
        JSONObject userInfo = IdaasUtil.getUserInfo(iDaasServer, accessToken);
        if (userInfo == null) {
            throw new AuthenticationCredentialsNotFoundException("获取用户信息失败");
        }

        // 创建用户详情
        CustomUserDetails data = getAuthentication(accessToken, userInfo.getJSONObject("data"));
        data.setPassword(new BCryptPasswordEncoder().encode(code));

        System.out.println("用户详情" + JSONObject.toJSONString(data));
        return data;
    }

    /**
     * 创建用户认证信息
     * @param user 用户信息
     * @return 用户详情
     */
    private CustomUserDetails getAuthentication(Account user) {
        Set<GrantedAuthority> authorities = new LinkedHashSet<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        return new CustomUserDetails(user, authorities);
    }

    /**
     * 创建用户认证信息
     * @param accessToken 访问令牌
     * @param userInfo 用户信息
     * @return 用户详情
     */
    private CustomUserDetails getAuthentication(String accessToken, JSONObject userInfo) {
        // 根据不同提供商获取用户ID和邮箱
        String id = userInfo.getString("sub");
        String nickName = userInfo.getString("nickname");
        String email = userInfo.getString("username") + "@difymail.com";
        GzycUserVO gzycUserVO = gzycUserService.getUserInfo(userInfo.getString("username"));


        // 查找数据库有没有对应账号，如果没有就创建
        Account account = null;
        AccountIntegrates accountIntegrates = accountIntegratesService.findByOpenId(id);
        if (accountIntegrates != null) {
            account = accountService.findById(accountIntegrates.getAccountId().toString());
        } else {
            account = accountService.findByEmail(email);
        }

        if (account == null) {
            account = Account.builder()
                    .name(nickName)
                    .email(email)
                    .interfaceTheme("light")
                .interfaceLanguage("zh-Hans")
                    .timezone("Asia/Shanghai")
                    .status("active")
                    .build();

            account = accountService.save(account);
        }

        log.info("account: {}", account);

        // 更新关联表
        accountIntegrates = accountIntegratesService.findByAccountIdAndProvider(account.getId(), "gzyc");
        if (accountIntegrates == null) {
            accountIntegrates = AccountIntegrates.builder()
                    .accountId(account.getId())
                    .provider("gzyc")
                    .openId(id)
                    .encryptedToken(accessToken)
                    .build();
        } else {
            accountIntegrates.setOpenId(id);
            accountIntegrates.setEncryptedToken(accessToken);
        }

        accountIntegratesService.save(accountIntegrates);

        account.setWorkNo(gzycUserVO.getWorkNo());

        CustomUserDetails authentication = getAuthentication(account);
        authentication.setIntegrate("idaas");

        return authentication;
    }
}
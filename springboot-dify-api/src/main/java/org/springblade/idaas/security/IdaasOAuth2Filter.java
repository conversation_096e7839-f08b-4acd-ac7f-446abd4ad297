package org.springblade.idaas.security;

import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.security.jwt.JwtTokenProvider;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;

import java.io.IOException;

/**
 * IDaaS OAuth2认证过滤器
 * 用于处理IDaaS认证请求并创建认证令牌
 */
@Slf4j
public class IdaasOAuth2Filter extends UsernamePasswordAuthenticationFilter {

    private JwtTokenProvider jwtTokenProvider;

    private String baseUrl;

    public void setJwtTokenProvider(JwtTokenProvider jwtTokenProvider) {
        this.jwtTokenProvider = jwtTokenProvider;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) {
        String code = request.getParameter("code");
        String state = request.getParameter("state");
        String provider = request.getParameter("provider");

        // 如果没有指定提供商，默认使用通用OAuth2流程
        if (provider == null) {
            provider = "default";
        }

        System.out.println("code:" + code + ", state:" + state + ", provider:" + provider);

        JSONObject principal = new JSONObject();
        principal.put("code", code);
        principal.put("state", state);
        principal.put("provider", provider);

        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(principal.toJSONString(), code);
        setDetails(request, authenticationToken);

        // 如果使用JWT，则不需要设置会话安全上下文存储库
        if (jwtTokenProvider == null) {
            this.setSecurityContextRepository(new HttpSessionSecurityContextRepository());
        }

        return this.getAuthenticationManager().authenticate(authenticationToken);
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response,
                                           FilterChain chain, Authentication authResult) throws IOException, ServletException {
        // 如果配置了JWT提供者，则生成JWT令牌
        if (jwtTokenProvider != null) {
            String token = jwtTokenProvider.createToken(authResult);

            // 获取用户信息
            CustomUserDetails userDetails = (CustomUserDetails) authResult.getPrincipal();

            // 获取当前服务的域名和端口
            String serverDomain = request.getScheme() + "://" + request.getServerName();
            if (request.getServerPort() != 80 && request.getServerPort() != 443) {
                serverDomain += ":" + request.getServerPort();
            }

            // 如果没有重定向URL，则使用baseUrl或当前服务域名构建重定向URL
            String finalRedirectUrl;
            if (baseUrl != null && !baseUrl.isEmpty()) {
                finalRedirectUrl = baseUrl;
            } else {
                // 使用当前服务域名作为基础URL
                finalRedirectUrl = serverDomain + "/index.html#/idaasLogin";
            }

            // 添加token参数
            if (finalRedirectUrl.contains("?")) {
                finalRedirectUrl += "&token=" + token;
            } else {
                finalRedirectUrl += "?token=" + token;
            }
            finalRedirectUrl += "&uid=" + userDetails.getAttributes().getId();

            log.info("重定向到: {}", finalRedirectUrl);
            response.sendRedirect(finalRedirectUrl);
        } else {
            // 如果没有配置JWT，则使用默认的会话行为
            super.successfulAuthentication(request, response, chain, authResult);
        }
    }
}
package org.springblade.idaas.controller;

import cn.hutool.core.lang.UUID;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.view.RedirectView;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * IDaaS登录控制器
 * 用于处理IDaaS登录请求和重定向
 */
@Controller
@Slf4j
@RequestMapping("/enclosure/idaas")
public class IdaasLoginController {

    @Resource
    HttpServletRequest request;

    @Resource
    HttpServletResponse response;

    @Value("${idaas.client-id:}")
    String clientId;

    @Value("${idaas.server-host}")
    String iDaasServer;

    @Value("${idaas.redirect-uri:}")
    String redirectUri;

    @Value("${idaas.login-url:/idaas/login/}")
    String loginUrl;

    @Value("${idaas.web-url:}")
    String baseUrl;

    /**
     * 钉钉登录入口
     */
    @GetMapping(value = "/login/dingtalk")
    public RedirectView loginDingtalk() {
        String url = UriComponentsBuilder.fromHttpUrl(iDaasServer + "/oauth/authorize")
                .queryParam("client_id", clientId)
                .queryParam("redirect_uri", redirectUri)
                .queryParam("response_type", "code")
                .queryParam("scope", "read")
                .queryParam("state", UUID.fastUUID())
                .encode() // 自动处理编码
                .build()
                .toUriString();
        log.info("钉钉登录重定向: {}", url);
        return new RedirectView(url);
    }

    @GetMapping(value = "/login/test")
    public RedirectView test() {
        return new RedirectView("/index.html#/home");
    }

}
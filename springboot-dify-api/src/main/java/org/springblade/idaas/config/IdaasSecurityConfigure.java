package org.springblade.idaas.config;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.idaas.security.IdaasOAuth2Filter;
import org.springblade.idaas.security.IdaasUserDetailsService;
import org.springblade.modules.security.jwt.JwtTokenProvider;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * IDaaS安全配置类
 * 用于配置IDaaS认证相关的安全设置
 */
@Configuration
@Slf4j
public class IdaasSecurityConfigure {

    @Resource
    IdaasUserDetailsService idaasUserDetailsService;

    @Resource
    JwtTokenProvider jwtTokenProvider;

    @Value("${idaas.web-url:}")
    String baseUri;

    @Value("${idaas.login-url:}")
    String loginUrl;

    @Value("${idaas.client-id:}")
    String clientId;

    @Value("${idaas.client-secret:}")
    String clientSecret;

    @Value("${idaas.redirect-uri:}")
    String redirectUri;

    /**
     * 创建IDaaS OAuth2过滤器
     */
    @Bean
    public IdaasOAuth2Filter idaasFilter() {
        IdaasOAuth2Filter idaasOAuth2Filter = new IdaasOAuth2Filter();
        idaasOAuth2Filter.setAuthenticationManager(idaasAuthenticationManager());
        idaasOAuth2Filter.setFilterProcessesUrl(loginUrl);
        idaasOAuth2Filter.setJwtTokenProvider(jwtTokenProvider); // 设置JWT提供者
        idaasOAuth2Filter.setBaseUrl(baseUri);
        idaasOAuth2Filter.setAuthenticationFailureHandler((request, response, exception) -> {
            log.info("IDaaS登录失败", exception);
            response.sendRedirect(baseUri + "?error=true");
        });
        idaasOAuth2Filter.setAuthenticationSuccessHandler((request, response, authentication) -> {
            log.info("IDaaS登录成功");
            // 注意：成功处理器已经在filter中实现，这里只是为了兼容性
            // JWT令牌已经在IdaasOAuth2Filter的successfulAuthentication方法中生成
        });
        return idaasOAuth2Filter;
    }

    /**
     * 创建IDaaS认证管理器
     */
    @Bean
    @Primary
    AuthenticationManager idaasAuthenticationManager() {
        DaoAuthenticationProvider authenticationProvider = new DaoAuthenticationProvider();
        authenticationProvider.setUserDetailsService(idaasUserDetailsService);
        authenticationProvider.setPasswordEncoder(new BCryptPasswordEncoder());
        return new ProviderManager(authenticationProvider);
    }

}
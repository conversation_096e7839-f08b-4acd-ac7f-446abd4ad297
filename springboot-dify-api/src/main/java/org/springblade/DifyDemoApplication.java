package org.springblade;

import com.feiniaojin.gracefulresponse.EnableGracefulResponse;
import org.springblade.modules.vo.MobileAgentParamVO;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;

@SpringBootApplication
@EnableGracefulResponse
public class DifyDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(DifyDemoApplication.class, args);
    }

    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
//        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
//        messageConverters.add(new FormHttpMessageConverter());
//        restTemplate.setMessageConverters(messageConverters);
        return restTemplate;
    }

    @Bean
    public WebClient webClient() {
        return WebClient.create();
    }

}

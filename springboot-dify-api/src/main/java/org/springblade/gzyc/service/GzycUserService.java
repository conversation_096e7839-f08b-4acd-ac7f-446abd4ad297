package org.springblade.gzyc.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.gzyc.util.AuthUtil;
import org.springblade.gzyc.vo.GzycUserVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class GzycUserService {

    @Value("${gzyc.username}")
    private String username;

    @Value("${gzyc.ak}")
    private String ak;

    @Value("${gzyc.sk}")
    private String sk;

    @Value("${gzyc.userInfoUrl}")
    private String userInfoUrl;

    public GzycUserVO getUserInfo(String userId) {
        if (StrUtil.isBlank(userId)) {
            return null;
        }

        String secret = secret(username, ak, sk);

        System.out.println(secret);

        JSONObject body = new JSONObject();
        body.put("userId", userId);
        body.put("pageNo", 1);
        body.put("pageSize", 10);

        String result = HttpRequest.post(userInfoUrl)
            .body(body.toJSONString())
            .bearerAuth(secret)
            .execute()
            .body();

        JSONObject tokenJson = JSON.parseObject(result);
        Object errcode = tokenJson.get("errcode");
        if (errcode instanceof Integer && ((int)errcode != 0)){
            log.error(result);
            return null;
        }

        JSONArray jsonArray = tokenJson.getJSONArray("data");
        if (jsonArray.isEmpty()) {
            return null;
        }

        JSONObject data = jsonArray.getJSONObject(0);
        if (data == null) {
            return null;
        }

        GzycUserVO userVO = new GzycUserVO();
        userVO.setWorkNo(data.getString("work_no"));
        userVO.setUserName(data.getString("user_name"));
        userVO.setDingtalkId(data.getString("dingtalk_id"));
        userVO.setWechatId(data.getString("wechat_id"));
        userVO.setCellPhone(data.getString("cell_phone"));
        userVO.setOrgName(data.getString("org_name"));

        return userVO;
    }

    public String secret(String username, String ak, String sk) {
        return AuthUtil.secret(username, ak, sk);

    }

}

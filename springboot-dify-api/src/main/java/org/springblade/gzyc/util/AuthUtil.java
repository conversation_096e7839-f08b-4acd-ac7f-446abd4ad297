package org.springblade.gzyc.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;

/**
 * 构建用于请求 gzyc 内部服务接口的校验
 */
public class AuthUtil {

    public static String secret(String username, String ak, String sk) {
        long timestamp = System.currentTimeMillis();
        String md5code = md5code(timestamp, sk);
        String json = "{" +
            "\"UserAccount\": \"" + username + "\", " +
            "\"AK\": \"" + ak + "\", " +
            "\"Timestamp\":" + timestamp + ", " +
            "\"LoginCode\":\"" + md5code + "\"}";
        return Base64.encode(json);
    }

    private static String md5code(long timestamp, String secret) {
        String linkstr = timestamp + secret;
        //DigestUtils.md5Hex(linkstr);
        return SecureUtil.md5(linkstr);
    }


}

package org.springblade.qyweixin.controller;

import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.view.RedirectView;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-03-15 22:34
 */
@Controller
@Slf4j
@RequestMapping("/enclosure/qywx")
public class LoginController {


    @Resource
    HttpServletRequest request;
    @Resource
    HttpServletResponse response;
    @Value("${wechat.agent-id}")
    String agentId;
    @Value("${wechat.corp-id}")
    String corpId;
    @Value("${qywx.redirect-uri}")
    String redirectUri;
    @Value("${qywx.login-url}")
    String loginUrl;


    @GetMapping(value = "/login")
    public RedirectView login() {
        String url = "https://open.weixin.qq.com/connect/oauth2/authorize" +
                "?" + "appid=" + corpId +
                "&" + "redirect_uri=" + redirectUri +
                "&" + "response_type=" + "code" +
                "&" + "scope=" + "SCOPE" +
                "&" + "agentid=" + agentId +
                "&" + "state=STATE" +
                "#wechat_redirect";
        System.out.println(url);
        // 线程停止 20秒
//        try {
//            Thread.sleep(20000);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
        return new RedirectView(url);
    }

}

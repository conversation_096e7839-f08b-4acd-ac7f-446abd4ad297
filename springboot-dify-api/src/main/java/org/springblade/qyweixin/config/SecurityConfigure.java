package org.springblade.qyweixin.config;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.idaas.config.IdaasSecurityConfigure;
import org.springblade.idaas.security.IdaasOAuth2Filter;
import org.springblade.modules.security.UsernamePasswordOAuth2Filter;
import org.springblade.modules.security.UsernamePasswordSecurityConfig;
import org.springblade.modules.security.jwt.JwtTokenFilter;
import org.springblade.modules.security.jwt.JwtTokenProvider;
import org.springblade.qyweixin.security.CustomLogoutSuccessHandler;
import org.springblade.qyweixin.security.CustomOAuth2Filter;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springblade.qyweixin.security.CustomUserDetailsService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@EnableMethodSecurity()
@Slf4j
public class SecurityConfigure {

    @Resource
    CustomUserDetailsService customUserDetailsService;

    @Resource
    IdaasSecurityConfigure idaasSecurityConfigure;

    @Resource
    UsernamePasswordSecurityConfig usernamePasswordSecurityConfig;

    @Resource
    CustomLogoutSuccessHandler customLogoutSuccessHandler; // 引入自定义的 LogoutSuccessHandler

    @Resource
    JwtTokenProvider jwtTokenProvider;

    @Value("${qywx.url}")
    String baseUri;
    @Value("${qywx.login-url}")
    String loginUrl;

    @Bean
    SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.authorizeHttpRequests(authz -> {
            authz
                    .requestMatchers("/enclosure/login/**",
                            "/error",
                            "/enclosure/login",
                            "/userLogin",
                            "/enclosure/idaas/**",
                            "/enclosure/qywx/**",
                            "/enclosure/auth/**",
                            "/enclosure/dify/stream",
                            "/enclosure/dify/stream/**",
                            "/enclosure/config/**",
                            "/enclosure/suggestion/export",
                            "/enclosure/public/**",
                            "/enclosure/dify/dataset/retrieve"
                    )
                    .permitAll()
                    .anyRequest()
                    .authenticated()
            ;
        });
        http.httpBasic(Customizer.withDefaults());
        http.headers(headerConfigurer -> {
            headerConfigurer.contentTypeOptions(HeadersConfigurer.ContentTypeOptionsConfig::disable);
            headerConfigurer.frameOptions(HeadersConfigurer.FrameOptionsConfig::disable);
        });
        http.csrf(AbstractHttpConfigurer::disable);

        // 设置会话管理为无状态（JWT不需要会话）
        http.sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));

        // 添加JWT过滤器
        http.addFilterBefore(new JwtTokenFilter(jwtTokenProvider), UsernamePasswordAuthenticationFilter.class);

        // 保留表单登录以兼容现有功能
        http.formLogin(login -> login
                .failureHandler((request, response, exception) -> {
                    log.warn(exception.getMessage());
                    response.setContentType("text/json;charset=UTF-8");
                    response.setStatus(403);
                    response.getWriter().write(exception.getMessage());
                })
        );

        // 添加企业微信认证过滤器
        http.addFilterBefore(qywxFilter(), UsernamePasswordAuthenticationFilter.class);
        // 添加IDaaS认证过滤器
        http.addFilterBefore(idaasSecurityConfigure.idaasFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(usernamePasswordSecurityConfig.usernamePasswordAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);

        http.exceptionHandling(exception -> exception
                .accessDeniedHandler((request, response, accessDeniedException) -> {
                    log.warn(accessDeniedException.getMessage());
                    log.warn(request.getRequestURI());
                    response.setContentType("text/json;charset=UTF-8");
                    response.setStatus(403);
                    response.getWriter().write(accessDeniedException.getMessage());
                })
                .authenticationEntryPoint((request, response, authException) -> {
                    log.warn(authException.getMessage());
                    log.warn(request.getRequestURI());
                    response.setContentType("text/json;charset=UTF-8");
                    response.setStatus(401);
                    response.getWriter().write("{\"code\": 401, \"message\": \"" + authException.getMessage() + "\"}");
                }));
        // 配置自定义的 LogoutSuccessHandler
        http.logout(logout -> logout
                .logoutUrl("/enclosure/logout")
                .permitAll()
                .logoutSuccessHandler(customLogoutSuccessHandler)); // 使用自定义的 LogoutSuccessHandler

//        http.logout(logout -> logout
//                .logoutUrl("/api/logout")
//                .logoutSuccessUrl("/index.html#/home"));
        return http.build();
    }

    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        return (web -> web.ignoring().requestMatchers("favicon.ico", "index.html", "fsyc.svg", "vite.svg", "/assets/**"));
    }

    @Bean
    CustomOAuth2Filter qywxFilter() {
        CustomOAuth2Filter customOAuth2Filter = new CustomOAuth2Filter();
        customOAuth2Filter.setAuthenticationManager(qywxAuthenticationManager());
        customOAuth2Filter.setFilterProcessesUrl(loginUrl);
        customOAuth2Filter.setJwtTokenProvider(jwtTokenProvider); // 设置JWT提供者
        customOAuth2Filter.setBaseUrl(baseUri);
        customOAuth2Filter.setAuthenticationFailureHandler((request, response, exception) -> {
            log.info("企业微信登录失败", exception);
            response.sendRedirect(baseUri + "?error=true");
        });
        customOAuth2Filter.setAuthenticationSuccessHandler((request, response, authentication) -> {
            log.info("企业微信登录成功");
            // 注意：成功处理器已经在filter中实现，这里只是为了兼容性
            // JWT令牌已经在CustomOAuth2Filter的successfulAuthentication方法中生成
        });
        return customOAuth2Filter;
    }


    @Bean
    AuthenticationManager qywxAuthenticationManager() {
        DaoAuthenticationProvider authenticationProvider = new DaoAuthenticationProvider();
        authenticationProvider.setUserDetailsService(customUserDetailsService);
        authenticationProvider.setPasswordEncoder(new BCryptPasswordEncoder());
        return new ProviderManager(authenticationProvider);
    }
}

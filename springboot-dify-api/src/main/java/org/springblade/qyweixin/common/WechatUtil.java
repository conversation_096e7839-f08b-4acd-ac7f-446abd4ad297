package org.springblade.qyweixin.common;

import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.Digester;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.json.XML;
import org.springblade.modules.util.SSLUtils;
import org.springblade.qyweixin.aes.AesException;
import org.springblade.qyweixin.aes.WXBizMsgCrypt;

import java.io.File;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.11.0
 * 微信企业号调用方法
 * 
 * <b>注意：</b>Hutool的版本需要是5.4.1以后的；
 ***/
@Slf4j
public class WechatUtil {

	private static Map<String, JSONObject> tokens;
	private static Map<String, JSONObject> tickets;

	private static final String WECHAT_URI= "https://qyapi.weixin.qq.com/cgi-bin/";
	private static final String ACCESS_TOKEN_STR = "access_token";
	
	private static final String[] IMAGE_TYPE = new String[] {"jpg", "png", "gif", "tif", "bmp"};
	private static final String[] VOICE_TYPE = new String[] {"wav", "ram", "mid"};
	private static final String[] VIDEO_TYPE = new String[] {"avi", "rm", "mpg", "mov", "asf"};
	
	/**
	 * 判断上次获取access_token的时间，如果未超过7000秒，则继续使用之前的值，否则重新获取
	 * 由于获取access_token的api调用次数非常有限，
	 * 因此应用存储与更新access_token，
	 * 频繁刷新access_token会导致api调用受限，影响自身业务
	 **/
	public static synchronized String accessToken(String corpId, String secret){
		if (tokens == null) {
			tokens = new HashMap<>();
		}
		
		String key = corpId + "-" + secret;
		
		JSONObject token = tokens.get(key);
		if (token == null) {
			token = new JSONObject();
		}
		String accessToken	= token.getString("accessToken");
		long accessTime		= token.getLongValue("accessTime");
		
		if (accessToken == null || System.currentTimeMillis() - accessTime > 7000 * 1000) {
			accessToken = getAccessToken(corpId, secret);
			accessTime = System.currentTimeMillis();
			token.put("accessToken", accessToken);
			token.put("accessTime", accessTime);
			tokens.put(key, token);
		}
		return accessToken;
	}
	
	/**
	 * 判断上次获取jsapi_ticket的时间，如果未超过7000秒，则继续使用之前的值，否则重新获取
	 * 由于获取jsapi_ticket的api调用次数非常有限，
	 * 因此应用存储与更新jsapi_ticket，
	 * 频繁刷新jsapi_ticket会导致api调用受限，影响自身业务
	 **/
	public static synchronized String jsapiTicket(String accessToken){
		if (tickets == null) {
			tickets = new HashMap<>();
		}
		
		JSONObject ticket = tickets.get(accessToken);
		if (ticket == null) {
			ticket = new JSONObject();
		}
		String jsapiTicket	= ticket.getString("jsapiTicket");
		long jsapiTime		= ticket.getLongValue("jsapiTime");
		
		if (jsapiTicket == null || System.currentTimeMillis() - jsapiTime > 7000 * 1000) {
			jsapiTicket = getJsapiTicket(accessToken);
			jsapiTime = System.currentTimeMillis();
			ticket.put("jsapiTicket", jsapiTicket);
			ticket.put("jsapiTime", jsapiTime);
			tickets.put(accessToken, ticket);
		}
		return jsapiTicket;
	}
	
	public static String getJsapiSignature(String accessToken, String noncestr, String timestamp, String url) {
		String ticket = jsapiTicket(accessToken);
		String str = "jsapi_ticket=" + ticket 
				+ "&" + "noncestr=" + noncestr 
				+ "&" + "timestamp=" + timestamp 
				+ "&" + "url=" + url;
		log.info(str);
		Digester sha1 = new Digester(DigestAlgorithm.SHA1);
		return sha1.digestHex(str);
	}
	
	/**
	 * 获取AccessToken
	 * AccessToken是企业号的全局唯一票据，调用接口时需携带AccessToken。
	 * AccessToken需要用CorpID和Secret来换取，不同的Secret会返回不同的AccessToken。
	 * 正常情况下AccessToken有效期为7200秒，有效期内重复获取返回相同结果。
	 * access_token至少保留512字节的存储空间。
	 **/
	private static String getAccessToken(String corpId, String secret){
		String path = "gettoken";
		Map<String, Object> querys = new HashMap<>();
		querys.put("corpid", corpId);
		querys.put("corpsecret", secret);
		
		String uri = WECHAT_URI + path;
		log.info("getAccessToken uri: {}", uri);
		String result = HttpRequest.get(uri)
				.form(querys)
				.setSSLSocketFactory(SSLUtils.getSSLSocketFactory())
				.execute()
				.body();
		
		JSONObject tokenJson = validate(result);
		if (tokenJson != null){
			return tokenJson.getString(ACCESS_TOKEN_STR);
		}
		return null;
	}
	
	private static String getJsapiTicket(String accessToken) {
		String path = "get_jsapi_ticket";
		Map<String, Object> querys = new HashMap<>();
		querys.put(ACCESS_TOKEN_STR, accessToken);

		String uri = WECHAT_URI + path;
		log.info("getJsapiTicket uri: {}", uri);
		String result = HttpRequest.get(uri)
				.form(querys)
				.setSSLSocketFactory(SSLUtils.getSSLSocketFactory())
				.execute()
				.body();
		
		JSONObject ticketJson = validate(result);
		if (ticketJson != null) {
			return ticketJson.getString("ticket");
		}
		return null;
	}
	
	/**
	 * 根据code获取成员信息
	 * @param accessToken	调用接口凭证
	 * @param code	通过成员授权获取到的code，每次成员授权带上的code将不一样，code只能使用一次，10分钟未被使用自动过期
	 * @return 
	 * [errcode]	返回码
	 * [errmsg]		对返回码的文本描述内容
	 * [OpenId]		非企业成员的标识，对当前企业唯一。不超过64字节
	 * [DeviceId]	手机设备号(由企业微信在安装时随机生成，删除重装会改变，升级不受影响)
	 * [external_userid]	外部联系人id，当且仅当用户是企业的客户，且跟进人在应用的可见范围内时返回。如果是第三方应用调用，针对同一个客户，同一个服务商不同应用获取到的id相同
	 **/
	public static JSONObject userInfo(String accessToken, String code){
		String path = "auth/getuserinfo";
		Map<String, Object> querys = new HashMap<>();
		querys.put(ACCESS_TOKEN_STR, accessToken);
		querys.put("code", code);
		
		String uri = WECHAT_URI + path;
		log.info("userInfo uri: {}", uri);
		String result = HttpRequest.get(uri)
				.form(querys)
				.setSSLSocketFactory(SSLUtils.getSSLSocketFactory())
				.execute()
				.body();
		
		return validate(result);
	}
	
	/**
	 * 读取该应用设置的可见范围内的成员信息
	 * @param accessToken	调用接口凭证
	 * @param userId	成员ID
	 * @return 
	 * [name]	成员名称；第三方不可获取，调用时返回userid以代替name；对于非第三方创建的成员，第三方通讯录应用也不可获取；第三方页面需要通过通讯录展示组件来展示名字
	 * [mobile]	手机号码，第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取
	 * [department]	成员所属部门id列表，仅返回该应用有查看权限的部门id
	 * ......
	 **/
	public static JSONObject userDetail(String accessToken, String userId){
		if (StrUtil.isBlank(userId)) {
			return null;
		}
		
		String path = "user/get";
		Map<String, Object> querys = new HashMap<>();
		querys.put(ACCESS_TOKEN_STR, accessToken);
		querys.put("userid", userId);
		
		String uri = WECHAT_URI + path;
		log.info("userDetail uri: {}", uri);
		String result = HttpRequest.get(uri)
				.form(querys)
				.setSSLSocketFactory(SSLUtils.getSSLSocketFactory())
				.execute()
				.body();
		
		return validate(result);
	}

	public static JSONObject ticket(String accessToken, String ticket){
		if (StrUtil.isBlank(ticket)) {
			return null;
		}

		String path = "auth/getuserdetail?" + ACCESS_TOKEN_STR + "=" + accessToken;

		JSONObject body = new JSONObject();
		body.put("user_ticket", ticket);

		String uri = WECHAT_URI + path;
		log.info("ticket uri: {}", uri);
		String result = HttpRequest.post(uri)
				.body(body.toJSONString())
				.setSSLSocketFactory(SSLUtils.getSSLSocketFactory())
				.execute()
				.body();

		return validate(result);
	}
	
	/**
	 * 发送应用消息 应用支持推送文本、图片、视频、文件、图文等类型。
	 * @param accessToken	调用接口凭证
	 * @param message	详见<a href="https://work.weixin.qq.com/api/doc/90000/90135/90236">企业微信说明文档</a>
	 * @return 
	 * [errcode]	返回码</br>
	 * [errmsg]		对返回码的文本描述内容</br>
	 * 如果部分接收人无权限或不存在，发送仍然执行，但会返回无效的部分（即invaliduser或invalidparty或invalidtag），常见的原因是接收人不在应用的可见范围内。</br>
	 * 如果全部接收人无权限或不存在，则本次调用返回失败，errcode为81013。</br>
	 * 返回包中的userid，不区分大小写，统一转为小写</br>
	 * [invaliduser]	无效用户</br>
	 * [invalidparty]	无效组织</br>
	 * [invalidtag]		无效标签</br>
	 **/
	public static JSONObject send(String accessToken, String message){
		String path = "message/send";
		
		String uri = WECHAT_URI + path 
				+ "?" + ACCESS_TOKEN_STR + "=" + accessToken;
		log.info("send uri: {}", uri);
		String result = HttpRequest.post(uri)
				.body(message)
				.setSSLSocketFactory(SSLUtils.getSSLSocketFactory())
				.execute()
				.body();
		
		return validate(result);
	}
	
	/**
	 * 取得文件所属的类型
	 * @param file	要判断的文件
	 * @return 文件类型
	 **/
	public static String getFileType(File file) {
		String fileType = FileTypeUtil.getType(file);
		String type = "file";
		if (Arrays.asList(IMAGE_TYPE).contains(fileType)) {
			type = "image";
		}
		if (Arrays.asList(VOICE_TYPE).contains(fileType)) {
			type = "voice";
		}
		if (Arrays.asList(VIDEO_TYPE).contains(fileType)) {
			type = "video";
		}
		return type;
	}
	
	/**
	 * 上传临时素材 素材上传得到media_id，该media_id仅三天内有效;media_id在同一企业内应用之间可以共享
	 * @param accessToken	调用接口凭证
	 * @param file	上传的文件
	 * @return 
	 * [errcode]	返回码</br>
	 * [errmsg]		对返回码的文本描述内容</br>
	 * [type]		媒体文件类型，分别有图片（image）、语音（voice）、视频（video），普通文件(file)</br>
	 * [media_id]	媒体文件上传后获取的唯一标识，3天内有效</br>
	 * [created_at]	媒体文件上传时间戳</br>
	 **/
	public static JSONObject upload(String accessToken, File file){
		String type = getFileType(file);
		
		String path = "media/upload";
		
		Map<String, Object> querys = new HashMap<>();
		querys.put("file",			file);
		
		String uri = WECHAT_URI + path 
				+ "?" + ACCESS_TOKEN_STR + "=" + accessToken 
				+ "&" + "type=" + type;
		String result = HttpUtil.post(uri, querys);
		
		return validate(result);
	}
	
	/**
	 * 获取临时素材  media_id在同一企业内所有应用之间可以共享。
	 * @param accessToken	调用接口凭证
	 * @param mediaId	上传的文件
	 * @param basePath	存放目录
	 * @return 
	 * [errcode]	返回码</br>
	 * [errmsg]		对返回码的文本描述内容</br>
	 * [size]		文件大小</br>
	 * [path]		存放路径</br>
	 * [name]		文件名</br>
	 **/
	public static JSONObject download(String accessToken, 
			String mediaId, String basePath) {
		String path = "media/get";
		
		String filePath = basePath + 
				File.separator + 
				System.currentTimeMillis() + 
				File.separator;
		File dir = new File(filePath);
		if (!dir.exists()) {
			dir.mkdir();
		}
		
		String uri = WECHAT_URI + path
				+ "?" + ACCESS_TOKEN_STR + "=" + accessToken 
				+ "&" + "media_id=" + mediaId;
		File file = HttpUtil.downloadFileFromUrl(uri, filePath);
		
		String fileName = file.getName();
		
		JSONObject ret = new JSONObject();
		ret.put("errcode", 0);
		ret.put("errmsg", "ok");
		ret.put("size", file.length());
		ret.put("path", filePath);
		ret.put("name", fileName);
		return ret;
	}
	
	/**
	 * 判断返回结果是否正常，如果正常则返回JSONObject，否则返回null
	 **/
	private static JSONObject validate(String result){
		if (result == null) {
			return null;
		}
		JSONObject tokenJson = JSON.parseObject(result);
		Object errcode = tokenJson.get("errcode");
		if (errcode instanceof Integer 
				&& ((int)errcode != 0)){
			log.error(result);
		}
		return tokenJson;
	}

	/**
	 * 事件消息接收的校验串
	 *
	 * @param corpId 企业ID
	 * @param token 回调Token
	 * @param aesKey 回调的AESKey
	 * @param sign 企业微信加密签名，msg_signature结合了企业填写的token、请求中的timestamp、nonce参数、加密的消息体
	 * @param timestamp 时间戳
	 * @param nonce 随机数
	 * @param echoStr 加密的字符串
	 * @return 校验结果
	 */
	public static String verifyStr(String corpId, String token, String aesKey,
								   String sign, String timestamp, String nonce, String echoStr) throws AesException {
		WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, aesKey, corpId);
		return wxcpt.VerifyURL(sign, timestamp, nonce, echoStr);
	}

	/**
	 * 事件消息接收
	 *
	 * @param corpId 企业ID
	 * @param token 回调Token
	 * @param aesKey 回调的AESKey
	 * @param sign 企业微信加密签名，msg_signature结合了企业填写的token、请求中的timestamp、nonce参数、加密的消息体
	 * @param timestamp 时间戳
	 * @param nonce 随机数
	 * @param context 接收到的内容
	 * @return 接收到的事件的JSON
	 */
	public static JSONObject reqMsg(String corpId, String token, String aesKey,
									String sign, String timestamp, String nonce, String context) throws AesException {
		WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, aesKey, corpId);
		String xml = wxcpt.DecryptMsg(sign, timestamp, nonce, context);
		org.json.JSONObject json = XML.toJSONObject(xml);
		return JSON.parseObject(json.toString()).getJSONObject("xml");
	}

	/**
	 * 被动消息回复
	 *
	 * @param corpId 企业ID
	 * @param token 回调Token
	 * @param aesKey 回调的AESKey
	 * @param timestamp 时间戳
	 * @param nonce 随机数
	 * @param context 回复的内容
	 * @return 接收到的事件的JSON
	 */
	public static String repMsg(String corpId, String token, String aesKey,
								String timestamp, String nonce, String context) throws AesException {
		WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, aesKey, corpId);
		return wxcpt.EncryptMsg(context, timestamp, nonce);
	}

	public static String repTxt(String corpId, String token, String aesKey,
								String timestamp, String nonce,
								String username, String context) throws AesException {
		String format = "<xml>" +
				"<ToUserName><![CDATA[%1$s]]></ToUserName>" +
				"<FromUserName><![CDATA[%2$s]]></FromUserName>" +
				"<CreateTime>%3$s</CreateTime>" +
				"<MsgType><![CDATA[%4$s]]></MsgType>" +
				"<Content><![CDATA[%5$s]]></Content>" +
				"</xml>";
		String xml = String.format(format,
				username,
				corpId,
				System.currentTimeMillis(),
				"text",
				context);
		//System.out.println(xml);
		return repMsg(corpId, token, aesKey, timestamp, nonce, xml);
	}

	public static String repImage(String corpId, String token, String aesKey,
								  String timestamp, String nonce,
								  String username, String mediaId) throws AesException {
		String format =
				"<xml>" +
						"<ToUserName><![CDATA[%1$s]]></ToUserName>" +
						"<FromUserName><![CDATA[%2$s]]></FromUserName>" +
						"<CreateTime>%3$s</CreateTime>" +
						"<MsgType><![CDATA[%4$s]]></MsgType>" +
						"<Image>"
						+ "<MediaId><![CDATA[%5$s]]></MediaId>" +
						"</Image>" +
						"</xml>";
		String xml = String.format(format,
				username,
				corpId,
				System.currentTimeMillis(),
				"image",
				mediaId);
		return repMsg(corpId, token, aesKey, timestamp, nonce, xml);
	}

	public static String repVoice(String corpId, String token, String aesKey,
								  String timestamp, String nonce,
								  String username, String mediaId) throws AesException {
		String format =
				"<xml>" +
						"<ToUserName><![CDATA[%1$s]]></ToUserName>" +
						"<FromUserName><![CDATA[%2$s]]></FromUserName>" +
						"<CreateTime>%3$s</CreateTime>" +
						"<MsgType><![CDATA[%4$s]]></MsgType>" +
						"<Voice>"
						+ "<MediaId><![CDATA[%5$s]]></MediaId>" +
						"</Voice>" +
						"</xml>";
		String xml = String.format(format,
				username,
				corpId,
				System.currentTimeMillis(),
				"voice",
				mediaId);
		return repMsg(corpId, token, aesKey, timestamp, nonce, xml);
	}

	public static String repVideo(String corpId, String token, String aesKey,
								  String timestamp, String nonce, String username,
								  String mediaId, String title, String desc) throws AesException {
		String format =
				"<xml>" +
						"<ToUserName><![CDATA[%1$s]]></ToUserName>" +
						"<FromUserName><![CDATA[%2$s]]></FromUserName>" +
						"<CreateTime>%3$s</CreateTime>" +
						"<MsgType><![CDATA[%4$s]]></MsgType>" +
						"<Video>"+
						"<MediaId><![CDATA[%5$s]]></MediaId>" +
						"<Title><![CDATA[%6$s]]></Title>" +
						"<Description><![CDATA[%7$s]]></Description>" +
						"</Video>" +
						"</xml>";
		String xml = String.format(format,
				username,
				corpId,
				System.currentTimeMillis(),
				"video",
				mediaId,
				title,
				desc);
		return repMsg(corpId, token, aesKey, timestamp, nonce, xml);
	}

	/**
	 * 回复图文信息
	 * @param corpId 企业ID
	 * @param token 回调Token
	 * @param aesKey 回调的AESKey
	 * @param timestamp 时间戳
	 * @param nonce 随机数
	 * @param username 用户名
	 * @param list 内容包括：title, description, picUrl, url
	 * @return
	 * @throws AesException
	 */
	public static String repNews(String corpId, String token, String aesKey,
								 String timestamp, String nonce, String username,
								 JSONArray list) throws AesException {
		String fmt0 =
				"<item>" +
						"<Title><![CDATA[%1$s]]></Title>" +
						"<Description><![CDATA[%2$s]]></Description>" +
						"<PicUrl><![CDATA[%3$s]]></PicUrl>" +
						"<Url><![CDATA[%4$s]]></Url>"+
						"</item>";
		StringBuilder sb = new StringBuilder();
		for (int i=0; i<list.size(); i++) {
			JSONObject art = list.getJSONObject(i);
			sb.append(String.format(fmt0,
					art.getString("title"),
					art.getString("description"),
					art.getString("picUrl"),
					art.getString("url")));
		}

		String format =
				"<xml>" +
						"<ToUserName><![CDATA[%1$s]]></ToUserName>" +
						"<FromUserName><![CDATA[%2$s]]></FromUserName>" +
						"<CreateTime>%3$s</CreateTime>" +
						"<MsgType><![CDATA[news]]></MsgType>" +
						"<ArticleCount><![CDATA[%4$s]]></ArticleCount>" +
						"<Articles>"+
						sb.toString() +
						"</Articles>" +
						"</xml>";
		String xml = String.format(format,
				username,
				corpId,
				System.currentTimeMillis(),
				list.size());
		return repMsg(corpId, token, aesKey, timestamp, nonce, xml);
	}

	/**
	 * 回复选择卡片
	 * @param corpId 企业ID
	 * @param token 回调Token
	 * @param aesKey 回调的AESKey
	 * @param timestamp 时间戳
	 * @param nonce 随机数
	 * @param username 用户名
	 * @param title 选择标题
	 * @param list 选项列表，包括text(显示内容), key(反回值)
	 * @return
	 * @throws AesException
	 */
	public static String repAction(String corpId, String token, String aesKey,
								   String timestamp, String nonce, String username,
								   String title, JSONArray list) throws AesException {
		String fmt0 =
				"<ActionList>" +
						"<Text><![CDATA[%1$s]]></Text>" +
						"<Key><![CDATA[%2$s]]></Key>" +
						"</ActionList>";
		StringBuilder sb = new StringBuilder();
		for (int i=0; i<list.size(); i++) {
			JSONObject art = list.getJSONObject(i);
			sb.append(String.format(fmt0,
					art.getString("text"),
					art.getString("key")));
		}

		String fmt1 =
				"<xml>" +
						"<ToUserName><![CDATA[%1$s]]></ToUserName>" +
						"<FromUserName><![CDATA[%2$s]]></FromUserName>" +
						"<CreateTime>%3$s</CreateTime>" +
						"<MsgType><![CDATA[update_template_card]]></MsgType>" +
						"<TemplateCard>" +
						"<CardType><![CDATA[text_notice]]></CardType>" +
						"<ActionMenu>" +
						"<Desc><![CDATA[%4$s]]></Desc>";
		String xml = String.format(fmt1,
				username,
				corpId,
				System.currentTimeMillis(),
				title);
		xml += sb.toString() +
				"</ActionMenu>" +
				"</TemplateCard>" +
				"</xml>";
		System.out.println(xml);
		return repMsg(corpId, token, aesKey, timestamp, nonce, xml);
	}
}

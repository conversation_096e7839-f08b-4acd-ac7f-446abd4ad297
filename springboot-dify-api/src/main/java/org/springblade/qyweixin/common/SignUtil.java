package org.springblade.qyweixin.common;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;

public class SignUtil {

    public static String sign(String userName, String ak, String sk) {
        long timestamp = System.currentTimeMillis() - 1000;
        String md5code = md5code(timestamp, sk);
        String json = "{" +
                "\"UserAccount\":\"" + userName + "\"," +
                "\"AK\":\"" + ak + "\"," +
                "\"Timestamp\":" + timestamp + "," +
                "\"LoginCode\":\"" + md5code + "\"}";
        return Base64.encode(json);
    }

    private static String md5code(long timestamp, String secret) {
        String str = timestamp + secret;
        return SecureUtil.md5(str);
    }
}

package org.springblade.qyweixin.security;

import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.security.jwt.JwtTokenProvider;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;

import java.io.IOException;


@Slf4j
public class CustomOAuth2Filter extends UsernamePasswordAuthenticationFilter {

    private JwtTokenProvider jwtTokenProvider;

    private String baseUrl;

    public void setJwtTokenProvider(JwtTokenProvider jwtTokenProvider) {
        this.jwtTokenProvider = jwtTokenProvider;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) {
        String code = request.getParameter("code");
        String state = request.getParameter("state");
        JSONObject principal = new JSONObject();
        principal.put("code", code);
        principal.put("state", state);
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(principal.toJSONString(), code);
        setDetails(request, authenticationToken);

        // 如果使用JWT，则不需要设置会话安全上下文存储库
        if (jwtTokenProvider == null) {
            this.setSecurityContextRepository(new HttpSessionSecurityContextRepository());
        }

        return this.getAuthenticationManager().authenticate(authenticationToken);
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response,
                                           FilterChain chain, Authentication authResult) throws IOException, ServletException {
        // 如果配置了JWT提供者，则生成JWT令牌
        if (jwtTokenProvider != null) {
            String token = jwtTokenProvider.createToken(authResult);

            // 获取用户信息
            CustomUserDetails userDetails = (CustomUserDetails) authResult.getPrincipal();
            String redirectUrl = baseUrl;

            // 添加token参数
            if (redirectUrl.contains("?")) {
                redirectUrl += "&token=" + token;
            } else {
                redirectUrl += "?token=" + token;
            }
            redirectUrl += "&uid=" + userDetails.getAttributes().getId();
            log.info("重定向到: {}", redirectUrl);
            response.sendRedirect(redirectUrl);
        } else {
            // 如果没有配置JWT，则使用默认的会话行为
            super.successfulAuthentication(request, response, chain, authResult);
        }
    }
}

package org.springblade.qyweixin.security;

import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.service.AccountIntegratesService;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
public class CustomLogoutSuccessHandler implements LogoutSuccessHandler {


    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        // 记录登出日志
        if (authentication != null && authentication.getName() != null) {
            log.info("用户 {} 成功登出", authentication.getName());
        } else {
            log.info("匿名用户成功登出");
        }

        String integrate = "username";

        // 获取用户的来源
        if (authentication != null) {
            CustomUserDetails principal = (CustomUserDetails) authentication.getPrincipal();
            if (StrUtil.isNotBlank(principal.getIntegrate())) {
                integrate = principal.getIntegrate();
            }
        }

        // 设置响应内容
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_OK);
        response.getWriter().write("{\"code\": 200, \"message\": \"登出成功\", \"integrate\": \"" + integrate + "\"}");

    }
}
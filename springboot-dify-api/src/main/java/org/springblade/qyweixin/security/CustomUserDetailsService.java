package org.springblade.qyweixin.security;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.gzyc.service.GzycUserService;
import org.springblade.gzyc.vo.GzycUserVO;
import org.springblade.modules.entity.Account;
import org.springblade.modules.entity.AccountIntegrates;
import org.springblade.modules.repository.AccountIntegratesRepository;
import org.springblade.modules.repository.AccountRepository;
import org.springblade.modules.service.AccountIntegratesService;
import org.springblade.modules.service.AccountService;
import org.springblade.qyweixin.common.WechatUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;

@Slf4j
@Component
public class CustomUserDetailsService implements UserDetailsService {

    @Value("${wechat.corp-id}")
    String corpId;
    @Value("${wechat.secret}")
    String secret;

    @Resource
    private AccountIntegratesService accountIntegratesService;

    @Resource
    private AccountService accountService;

    @Resource
    private GzycUserService gzycUserService;

    @Override
    public UserDetails loadUserByUsername(String principal) throws UsernameNotFoundException {
        log.info(principal);
        JSONObject obj = JSON.parseObject(principal);
        String code = obj.getString("code");
        String state = obj.getString("state");

        String accessToken = WechatUtil.accessToken(corpId, secret);
        JSONObject userInfo = getUserInfoByToken(accessToken, code);
        CustomUserDetails details = getAuthentication(accessToken, userInfo);

        details.setPassword(new BCryptPasswordEncoder().encode(code));

        log.info("details: {}", JSON.toJSONString(details));
        return details;
    }

    private CustomUserDetails getAuthentication(Account user) {
        Set<GrantedAuthority> authorities = new LinkedHashSet<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        return new CustomUserDetails(user, authorities);
    }

    private CustomUserDetails getAuthentication(String accessToken, JSONObject loginInfo) {
        String id = loginInfo.getString("userid");
        GzycUserVO userInfo = gzycUserService.getUserInfo(id);

        String nickName;
        String email;
        String workNo;
        if (userInfo != null) {
            nickName = userInfo.getUserName();
            email = userInfo.getWorkNo() + "@difymail.com";
            workNo = userInfo.getWorkNo();
            log.info("loginInfo: {}", loginInfo);
            log.info("userInfo: {}", userInfo);
        } else {
            JSONObject wechatUserInfo = WechatUtil.userDetail(accessToken, id);
            if (wechatUserInfo == null) {
                throw new AuthenticationCredentialsNotFoundException("获取用户信息失败");
            }
            nickName = wechatUserInfo.getString("name");
            workNo = id;
            email = id + "@difymail.com";
            log.info("wechatUserInfo: {}", wechatUserInfo);
        }


        // 查找数据库有没有对应账号，如果没有就创建
        Account account = null;
        AccountIntegrates accountIntegrates = accountIntegratesService.findByOpenId(id);
        if (accountIntegrates != null) {
            account = accountService.findById(accountIntegrates.getAccountId().toString());
        } else {
            account = accountService.findByEmail(email);
        }

        if (account == null) {
            account = Account.builder()
                    .name(nickName)
                    .email(email)
                    .password(new BCryptPasswordEncoder().encode("123456"))
                    .interfaceTheme("light")
                    .interfaceLanguage("zh-Hans")
                    .timezone("Asia/Shanghai")
                    .status("active")
                    .build();

            account = accountService.save(account);
        }

        // 更新关联表
        accountIntegrates = accountIntegratesService.findByAccountIdAndProvider(account.getId(), "qywx");
        if (accountIntegrates == null) {
            accountIntegrates = AccountIntegrates.builder()
                    .accountId(account.getId())
                    .provider("qywx")
                    .openId(id)
                    .encryptedToken(accessToken)
                    .build();
        } else {
            accountIntegrates.setOpenId(id);
            accountIntegrates.setEncryptedToken(accessToken);
        }

        accountIntegratesService.save(accountIntegrates);

        account.setWorkNo(workNo);

        CustomUserDetails authentication = getAuthentication(account);
        authentication.setIntegrate("qywx");

        return authentication;
    }

    private JSONObject getUserInfoByToken(String token, String code) {
        return WechatUtil.userInfo(token, code);
    }
}

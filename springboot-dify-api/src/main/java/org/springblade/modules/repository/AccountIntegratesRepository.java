package org.springblade.modules.repository;

import org.springblade.modules.entity.AccountIntegrates;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.UUID;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-03-17 00:10
 */
@Repository
public interface AccountIntegratesRepository extends JpaRepository<AccountIntegrates, String> {

    AccountIntegrates findByOpenId(String openId);

    AccountIntegrates findByAccountIdAndProvider(String accountId, String provider);

}

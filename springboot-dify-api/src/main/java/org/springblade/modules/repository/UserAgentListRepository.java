package org.springblade.modules.repository;

import org.springblade.modules.entity.UserAgentList;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-04-01 17:44
 */
@Repository
public interface UserAgentListRepository extends JpaRepository<UserAgentList, String> {

    UserAgentList findByAgentDictIdAndCreateBy(String agentDictId, String createBy);

    @Query(value =
            "SELECT * FROM portal_user_agent_list " +
                    "WHERE create_by = :userId " +
                    "ORDER BY is_top DESC, create_at DESC",
            nativeQuery = true
    )
    List<UserAgentList> findAllByCreateByOrderByCreateAt(String userId);

    void removeByAgentDictId(String agentDictId);

}

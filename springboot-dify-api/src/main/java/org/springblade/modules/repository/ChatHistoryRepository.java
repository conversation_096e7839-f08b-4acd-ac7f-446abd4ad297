package org.springblade.modules.repository;

import org.springblade.modules.entity.ChatHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-04-01 17:44
 */
@Repository
public interface ChatHistoryRepository extends JpaRepository<ChatHistory, String> {

    /**
     * 根据用户ID查询前20条聊天记录，并按order排序，过滤isDeleted为true的记录
     *
     * @param userId
     * @return
     */
    @Query(value =
            "SELECT * FROM portal_chat_history " +
            "WHERE create_by = :userId AND is_deleted = 0 " +
            "ORDER BY is_top DESC, create_at DESC",
            nativeQuery = true
    )
    List<ChatHistory> findChatHistoriesByCreateAt(@Param("userId") String userId);

    @Query(value =
            "SELECT * FROM portal_chat_history " +
                    "WHERE create_by = :userId AND agent_dict_id = :agentDictId AND is_deleted = 0 " +
                    "ORDER BY is_top DESC, create_at DESC",
            nativeQuery = true
    )
    List<ChatHistory> findChatHistoriesByCreateAtAndAgentDictId(@Param("userId") String userId, @Param("agentDictId") String agentDictId);

    @Modifying
    @Query("UPDATE ChatHistory a SET a.isDeleted = :isDeleted WHERE a.agentDictId = :agentDictId")
    void updateIsDeletedByAgentDictId(@Param("agentDictId") String agentDictId, @Param("isDeleted") Boolean isDeleted);


}

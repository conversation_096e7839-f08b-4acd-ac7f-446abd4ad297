package org.springblade.modules.repository;

import org.springblade.modules.entity.Account;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


@Repository
public interface AccountRepository extends JpaRepository<Account, String> {

    Account findByName(String name);

    Account findByEmail(String email);
}
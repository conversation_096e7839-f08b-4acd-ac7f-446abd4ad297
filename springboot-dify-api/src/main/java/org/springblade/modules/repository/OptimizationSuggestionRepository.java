package org.springblade.modules.repository;

import org.springblade.modules.entity.OptimizationSuggestion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 优化建议箱数据访问层
 */
@Repository
public interface OptimizationSuggestionRepository extends JpaRepository<OptimizationSuggestion, String> {

    /**
     * 根据用户ID查询优化建议列表
     * @param userId 用户ID
     * @return 优化建议列表
     */
    List<OptimizationSuggestion> findByUserIdOrderByCreateTimeDesc(String userId);

    /**
     * 查询所有优化建议列表，按创建时间降序排序
     * @return 优化建议列表
     */
    List<OptimizationSuggestion> findAllByOrderByCreateTimeDesc();
}

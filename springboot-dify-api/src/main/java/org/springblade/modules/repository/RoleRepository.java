package org.springblade.modules.repository;

import org.springblade.modules.entity.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户组数据访问层
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-27
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, String> {

    /**
     * 查询所有未删除的用户组
     */
    @Query("SELECT r FROM Role r WHERE r.isDeleted = false ORDER BY r.createdAt ASC")
    List<Role> findAllActive();

    /**
     * 根据名称查询用户组（未删除）
     */
    @Query("SELECT r FROM Role r WHERE r.name = :name AND r.isDeleted = false")
    Optional<Role> findByNameAndNotDeleted(@Param("name") String name);

    /**
     * 查询系统内置用户组
     */
    @Query("SELECT r FROM Role r WHERE r.isSystem = true AND r.isDeleted = false ORDER BY r.createdAt ASC")
    List<Role> findSystemRoles();

    /**
     * 查询非系统内置用户组
     */
    @Query("SELECT r FROM Role r WHERE r.isSystem = false AND r.isDeleted = false ORDER BY r.createdAt ASC")
    List<Role> findCustomRoles();

    /**
     * 根据用户ID查询用户所属的用户组
     */
    @Query("SELECT r FROM Role r INNER JOIN UserRole ur ON r.id = ur.roleId " +
           "WHERE ur.userId = :userId AND r.isDeleted = false ORDER BY r.createdAt ASC")
    List<Role> findRolesByUserId(@Param("userId") String userId);

    /**
     * 检查用户组名称是否已存在（排除指定ID）
     */
    @Query("SELECT COUNT(r) > 0 FROM Role r WHERE r.name = :name AND r.isDeleted = false AND r.id != :excludeId")
    boolean existsByNameAndNotDeletedExcludingId(@Param("name") String name, @Param("excludeId") String excludeId);

    /**
     * 检查用户组名称是否已存在
     */
    @Query("SELECT COUNT(r) > 0 FROM Role r WHERE r.name = :name AND r.isDeleted = false")
    boolean existsByNameAndNotDeleted(@Param("name") String name);
}

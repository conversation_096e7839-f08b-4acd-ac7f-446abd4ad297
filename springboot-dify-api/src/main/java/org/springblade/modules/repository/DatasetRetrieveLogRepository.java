package org.springblade.modules.repository;

import org.springblade.modules.entity.DatasetRetrieveLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 知识库检索日志仓库接口
 * <AUTHOR> [sijun.zeng]
 * @date 2025-05-21 10:35
 */
@Repository
public interface DatasetRetrieveLogRepository extends JpaRepository<DatasetRetrieveLog, String> {

    /**
     * 根据用户ID查询检索日志，按时间降序排序
     * @param userId 用户ID
     * @return 检索日志列表
     */
    List<DatasetRetrieveLog> findByUserIdOrderByRetrieveTimeDesc(String userId);

    /**
     * 查询所有检索日志，按时间降序排序
     * @return 检索日志列表
     */
    List<DatasetRetrieveLog> findAllByOrderByRetrieveTimeDesc();

    /**
     * 根据关键词模糊查询检索日志
     * @param query 关键词
     * @return 检索日志列表
     */
    @Query("SELECT d FROM DatasetRetrieveLog d WHERE d.query LIKE %:query% ORDER BY d.retrieveTime DESC")
    List<DatasetRetrieveLog> findByQueryContainingOrderByRetrieveTimeDesc(@Param("query") String query);
}

package org.springblade.modules.req;

import lombok.Data;

import java.io.Serializable;

@Data
public class DifyFile implements Serializable {

    /**
     * 支持类型
     * document 具体类型包含：'TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'
     * image 具体类型包含：'JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'
     * audio 具体类型包含：'MP3', 'M4A', 'WAV', 'WEBM', 'AMR'
     * video 具体类型包含：'MP4', 'MOV', 'MPEG', 'MPGA'
     * custom 具体类型包含：其他文件类型
     */
    private String type;

    /**
     * 传递方式
     * remote_url: 图片地址。
     * local_file: 上传文件。
     */
    private String transfer_method;

    /**
     * 图片地址。（仅当传递方式为 remote_url 时）。
     */
    private String url;

    /**
     * 上传文件 ID。（仅当传递方式为 local_file 时）。
     */
    private String upload_file_id;

}

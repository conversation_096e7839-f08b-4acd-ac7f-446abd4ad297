package org.springblade.modules.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.entity.AgentDict;
import org.springblade.modules.entity.ChatHistory;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChatHistoryVO extends ChatHistory implements Serializable {

    private AgentDict agentDict;

    public ChatHistoryVO() {
    }

    public ChatHistoryVO(ChatHistory chatHistory) {
        this.setId(chatHistory.getId());
        this.setAgentDictId(chatHistory.getAgentDictId());
        this.setConversationId(chatHistory.getConversationId());
        this.setName(chatHistory.getName());
        this.setIsTop(chatHistory.getIsTop());
        this.setIsDeleted(chatHistory.getIsDeleted());
        this.setCreateAt(chatHistory.getCreateAt());
        this.setCreateBy(chatHistory.getCreateBy());
    }
}

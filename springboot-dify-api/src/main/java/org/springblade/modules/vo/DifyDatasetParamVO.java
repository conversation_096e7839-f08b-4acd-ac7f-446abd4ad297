package org.springblade.modules.vo;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * dify的知识库yml配置对象
 * <AUTHOR> [sijun.zeng]
 * @date 2025-05-20 22:21
 */
@Component
public class DifyDatasetParamVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Value("${dify.dataset.api-key}")
    private String apiKey;

    @Value("${dify.dataset.search-ids}")
    private String searchIdsStr;

    @Value("${dify.dataset.api-server-host}")
    private String apiServerHost;

    private List<String> searchIds = new ArrayList<>();

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getSearchIdsStr() {
        return searchIdsStr;
    }

    public void setSearchIdsStr(String searchIdsStr) {
        if (StrUtil.isBlank(searchIdsStr)) {
            setSearchIds(ListUtil.empty());
        } else {
            setSearchIds(StrUtil.splitTrim(searchIdsStr, ","));
        }

        this.searchIdsStr = searchIdsStr;
    }

    public List<String> getSearchIds() {
        if (StrUtil.isNotBlank(searchIdsStr) && IterUtil.isEmpty(searchIds)) {
            setSearchIds(StrUtil.splitTrim(searchIdsStr, ","));
        }

        return searchIds;
    }

    public void setSearchIds(List<String> searchIds) {
        this.searchIds = searchIds;
    }

    public String getApiServerHost() {
        return apiServerHost;
    }

    public void setApiServerHost(String apiServerHost) {
        this.apiServerHost = apiServerHost;
    }
}

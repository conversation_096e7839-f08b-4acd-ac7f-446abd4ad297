package org.springblade.modules.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * dify的知识库查询参数对象
 * <AUTHOR> [sijun.zeng]
 * @date 2025-05-20 22:27
 */
@Data
public class DifyDatasetSearchVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检索关键词
     */
    private String query;

    /**
     * 检索参数
     */
    @JsonProperty("retrieval_model")
    private RetrievalModel retrievalModel;

    /**
     * 检索模型参数
     */
    @Data
    public static class RetrievalModel implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 检索方法：keyword_search(关键字检索)、semantic_search(语义检索)、full_text_search(全文检索)、hybrid_search(混合检索)
         */
        @JsonProperty("search_method")
        private String searchMethod;

        /**
         * 是否启用 Reranking
         */
        @JsonProperty("reranking_enable")
        private Boolean rerankingEnable;

        /**
         * Rerank 模型配置
         */
        @JsonProperty("reranking_model")
        private RerankingModel rerankingModel;

        /**
         * 混合检索模式下语意检索的权重设置
         */
        private Float weights;

        /**
         * 返回结果数量
         */
        @JsonProperty("top_k")
        private Integer topK;

        /**
         * 是否开启 score 阈值
         */
        @JsonProperty("score_threshold_enabled")
        private Boolean scoreThresholdEnabled;

        /**
         * Score 阈值
         */
        @JsonProperty("score_threshold")
        private Float scoreThreshold;
    }

    /**
     * Rerank 模型配置
     */
    @Data
    public static class RerankingModel implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * Rerank 模型提供商
         */
        @JsonProperty("reranking_provider_name")
        private String rerankingProviderName;

        /**
         * Rerank 模型名称
         */
        @JsonProperty("reranking_model_name")
        private String rerankingModelName;
    }
}

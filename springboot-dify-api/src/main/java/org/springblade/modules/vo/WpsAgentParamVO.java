package org.springblade.modules.vo;

import cn.hutool.extra.spring.SpringUtil;
import org.springblade.modules.entity.AgentDict;
import org.springblade.modules.service.AgentDictService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
public class WpsAgentParamVO implements Serializable {

    @Value("${wps.api_server_host}")
    private String apiServerHost;

    @Value("${wps.api_key}")
    private String apiKey;

    @Value("${wps.name}")
    private String name;

    private AgentDict agentDict;

    public String getApiServerHost() {
        return apiServerHost;
    }

    public void setApiServerHost(String apiServerHost) {
        this.apiServerHost = apiServerHost;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public AgentDict getAgentDict() {
        if (this.agentDict == null) {
            AgentDictService agentDictService = SpringUtil.getBean(AgentDictService.class);
            this.agentDict = agentDictService.findByApiKeyAndApiServerHost(this.apiKey, this.apiServerHost);
            if (this.agentDict == null) {
                this.agentDict = new AgentDict();
                this.agentDict.setApiKey(this.apiKey);
                this.agentDict.setApiServerHost(this.apiServerHost);
            }
            this.agentDict.setName(this.name);
        }

        return agentDict;
    }

}

package org.springblade.modules.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.modules.entity.AgentDict;
import org.springblade.modules.entity.UserAgentList;

import java.io.Serializable;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-04-06 08:42
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserAgentListVO extends UserAgentList implements Serializable {

    private AgentDict agentDict;

    public UserAgentListVO() {
    }

    public UserAgentListVO(UserAgentList userAgentList) {
        this.setId(userAgentList.getId());
        this.setAgentDictId(userAgentList.getAgentDictId());
        this.setIsTop(userAgentList.getIsTop());
        this.setCreateAt(userAgentList.getCreateAt());
        this.setCreateBy(userAgentList.getCreateBy());
    }

}

package org.springblade.modules.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * 用户组视图对象
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoleVO {

    /**
     * 用户组ID
     */
    private String id;

    /**
     * 用户组名称
     */
    private String name;

    /**
     * 用户组描述
     */
    private String description;

    /**
     * 是否系统内置用户组
     */
    private Boolean isSystem;

    /**
     * 创建时间
     */
    private Timestamp createdAt;

    /**
     * 更新时间
     */
    private Timestamp updatedAt;

    /**
     * 用户组下的用户数量
     */
    private Integer userCount;

    /**
     * 用户组下的用户列表（可选）
     */
    private List<UserVO> users;
}

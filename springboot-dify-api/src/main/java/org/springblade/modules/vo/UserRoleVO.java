package org.springblade.modules.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 用户用户组关联视图对象
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRoleVO {

    /**
     * 关联ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户组ID
     */
    private String roleId;

    /**
     * 用户信息
     */
    private UserVO user;

    /**
     * 用户组信息
     */
    private RoleVO role;

    /**
     * 创建时间
     */
    private Timestamp createdAt;
}

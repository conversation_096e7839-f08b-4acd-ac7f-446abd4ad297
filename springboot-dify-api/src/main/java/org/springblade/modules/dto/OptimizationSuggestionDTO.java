package org.springblade.modules.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 优化建议箱数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OptimizationSuggestionDTO implements Serializable {

    /**
     * 优化建议标题（不超过150字）
     */
    private String title;

    /**
     * 详细描述（不超过800字）
     */
    private String description;
}

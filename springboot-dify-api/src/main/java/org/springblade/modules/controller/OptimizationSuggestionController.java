package org.springblade.modules.controller;

import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.dto.OptimizationSuggestionDTO;
import org.springblade.modules.entity.OptimizationSuggestion;
import org.springblade.modules.service.OptimizationSuggestionService;
import org.springblade.modules.util.ExcelExportUtil;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 优化建议箱控制器
 */
@RestController
@RequestMapping("/enclosure/suggestion")
@RequiredArgsConstructor
@Slf4j
public class OptimizationSuggestionController {

    private final OptimizationSuggestionService optimizationSuggestionService;

    /**
     * 提交优化建议
     * @param suggestionDTO 优化建议DTO
     * @return 提交结果
     */
    @PostMapping("/submit")
    public Map<String, Object> submitSuggestion(@RequestBody OptimizationSuggestionDTO suggestionDTO) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            
            // 创建优化建议实体
            OptimizationSuggestion suggestion = OptimizationSuggestion.builder()
                    .title(suggestionDTO.getTitle())
                    .description(suggestionDTO.getDescription())
                    .userId(userDetails.getAttributes().getId())
                    .userName(userDetails.getAttributes().getName())
                    .createTime(Timestamp.valueOf(LocalDateTime.now()))
                    .build();
            
            // 保存优化建议
            OptimizationSuggestion saved = optimizationSuggestionService.save(suggestion);
            
            result.put("success", true);
            result.put("message", "提交成功");
            result.put("data", saved);
        } catch (Exception e) {
            log.error("提交优化建议失败", e);
            result.put("success", false);
            result.put("message", "提交失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取优化建议列表
     * @return 优化建议列表
     */
    @GetMapping("/list")
    public Map<String, Object> getSuggestionList() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            
            List<OptimizationSuggestion> suggestions;
            
            // 判断是否为管理员用户"gzyc"
            if ("gzyc".equals(userDetails.getAttributes().getName())) {
                // 管理员查看所有建议
                suggestions = optimizationSuggestionService.findAll();
            } else {
                // 普通用户只查看自己的建议
                suggestions = optimizationSuggestionService.findByUserId(userDetails.getAttributes().getId());
            }
            
            result.put("success", true);
            result.put("data", suggestions);
        } catch (Exception e) {
            log.error("获取优化建议列表失败", e);
            result.put("success", false);
            result.put("message", "获取列表失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 导出优化建议列表为Excel
     * @param response HTTP响应
     */
    @GetMapping("/export")
    public void exportSuggestions(HttpServletResponse response) {
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            
            List<OptimizationSuggestion> suggestions;
            String fileName;
            
            // 判断是否为管理员用户"gzyc"
            if ("gzyc".equals(userDetails.getAttributes().getName())) {
                // 管理员导出所有建议
                suggestions = optimizationSuggestionService.findAll();
                fileName = "全部优化建议";
            } else {
                // 普通用户只导出自己的建议
                suggestions = optimizationSuggestionService.findByUserId(userDetails.getAttributes().getId());
                fileName = userDetails.getAttributes().getName() + "的优化建议";
            }
            
            // 导出Excel
            ExcelExportUtil.exportOptimizationSuggestions(response, suggestions, fileName);
        } catch (Exception e) {
            log.error("导出优化建议列表失败", e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }


    /**
     * 删除优化建议（仅管理员可用）
     * @param id 优化建议ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Map<String, Object> deleteSuggestion(@PathVariable("id") String id) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();

            // 判断是否为管理员用户"gzyc"
            if (!"gzyc".equals(userDetails.getAttributes().getName())) {
                response.put("success", false);
                response.put("message", "权限不足，只有管理员可以删除建议");
                return response;
            }

            // 删除建议
            optimizationSuggestionService.delete(id);

            response.put("success", true);
            response.put("message", "删除成功");
        } catch (Exception e) {
            log.error("删除优化建议失败", e);
            response.put("success", false);
            response.put("message", "删除失败：" + e.getMessage());
        }

        return response;
    }
}

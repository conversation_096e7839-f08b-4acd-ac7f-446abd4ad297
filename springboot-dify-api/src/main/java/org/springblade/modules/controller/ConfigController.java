package org.springblade.modules.controller;

import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.qyweixin.common.WechatUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-04-22 23:21
 */
@RestController
@RequestMapping("/enclosure/config")
@RequiredArgsConstructor
@Slf4j
public class ConfigController {

    @Value("${wechat.second-level-domain-names}")
    String secondLevelDomainNames;

    @Value("${chat-voice.url}")
    String chatVoiceUrl;


    @GetMapping("/getSecondLevelDomainNames")
    public String getSecondLevelDomainNames() {
        System.out.println("secondLevelDomainNames:::" + secondLevelDomainNames);
        return secondLevelDomainNames;
    }

    @GetMapping("/getChatVoiceUrl")
    public String getChatVoiceUrl() {
        System.out.println("chatVoiceUrl:::" + chatVoiceUrl);
        return chatVoiceUrl;
    }


}

package org.springblade.modules.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springblade.modules.entity.AgentDict;
import org.springblade.modules.entity.ChatHistory;
import org.springblade.modules.service.ChatHistoryService;
import org.springblade.modules.service.DifyService;
import org.springblade.modules.vo.ChatHistoryVO;
import org.springblade.modules.vo.MobileAgentParamVO;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-04-01 22:19
 */
@RestController
@RequestMapping("/enclosure/chatHistory")
@RequiredArgsConstructor
public class ChatHistoryController {

    private final HttpServletRequest request;

    private final ChatHistoryService chatHistoryService;

    private final DifyService difyService;

    private final MobileAgentParamVO mobileAgentParamVO;

    @GetMapping("/list")
    public List<ChatHistoryVO> getChatHistoryList(@RequestParam(required = false) String conversationId, @RequestParam(required = false) String agentId) {
        if (StrUtil.isNotBlank(agentId) && StrUtil.isNotBlank(conversationId)) {
            UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
            CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();

            List<ChatHistory> allRecords = chatHistoryService.findAllDefByCreateBy();
            // 如果 conversationId 已经存在，就不做添加
            for (ChatHistory record : allRecords) {
                if (record.getConversationId().toString().equals(conversationId)) {
                    return chatHistoryService.findAllByCreateBy();
                }
            }

            JSONObject conversations = difyService.conversations(principal.getAttributes().getName(), agentId);
            JSONArray dataArray = conversations.getJSONArray("data");
            for (JSONObject conversation : dataArray.jsonIter()) {
                if (conversation.getStr("id").equals(conversationId)) {
                    String name = conversation.getStr("name");
                    chatHistoryService.createNew(agentId, conversationId, name);
                }
            }

            // 删除超过20条的记录
            if (allRecords.size() > 20) {
                List<ChatHistory> recordsToDelete = allRecords.subList(20, allRecords.size());
                for (ChatHistory record : recordsToDelete) {
                    chatHistoryService.delete(record.getId());
                }
            }

        }

        if (mobileAgentParamVO.isMobile()) {
            AgentDict agentDict = mobileAgentParamVO.getAgentDict();
            return chatHistoryService.findAllByCreateByAndAgentDictId(String.valueOf(agentDict.getId()));
        }

        return chatHistoryService.findAllByCreateBy();
    }

    @GetMapping("/delete")
    public void deleteChatHistory(@RequestParam String id) {
        chatHistoryService.delete(id);
    }

    @GetMapping("/rename")
    public void renameChatHistory(@RequestParam String id, @RequestParam String name) {
        chatHistoryService.rename(id, name);
    }

    @GetMapping("/top")
    public void topChatHistory(@RequestParam String id, @RequestParam Boolean isTop, @RequestParam(required = false) String newName) {
        chatHistoryService.top(id, isTop, newName);
    }

}

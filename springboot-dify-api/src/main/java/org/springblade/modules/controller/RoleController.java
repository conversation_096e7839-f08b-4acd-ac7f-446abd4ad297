package org.springblade.modules.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.service.RoleService;
import org.springblade.modules.vo.RoleVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户组管理控制器
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-27
 */
@RestController
@RequestMapping("/enclosure/roles")
@RequiredArgsConstructor
@Slf4j
public class RoleController {

    private final RoleService roleService;

    /**
     * 获取所有用户组列表
     */
    @GetMapping("/getAllRoles")
    public String getAllRoles() {
        JSONObject result = JSONUtil.createObj();
        try {
            List<RoleVO> roles = roleService.findAllActiveRoles();
            result.set("code", 200);
            result.set("data", roles);
            result.set("msg", "获取用户组列表成功");
        } catch (Exception e) {
            log.error("获取用户组列表失败", e);
            result.set("code", 500);
            result.set("msg", "获取用户组列表失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 根据ID获取用户组详情
     */
    @GetMapping("/getRoleById")
    public String getRoleById(String id) {
        JSONObject result = JSONUtil.createObj();
        try {
            RoleVO role = roleService.findById(id);
            if (role != null) {
                result.set("code", 200);
                result.set("data", role);
                result.set("msg", "获取用户组详情成功");
            } else {
                result.set("code", 404);
                result.set("msg", "用户组不存在");
            }
        } catch (Exception e) {
            log.error("获取用户组详情失败", e);
            result.set("code", 500);
            result.set("msg", "获取用户组详情失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 创建新用户组
     */
    @PostMapping("/createRole")
    public String createRole(@RequestBody JSONObject request) {
        JSONObject result = JSONUtil.createObj();
        try {
            String name = request.getStr("name");
            String description = request.getStr("description");
            Boolean isSystem = request.getBool("isSystem", false);

            if (name == null || name.trim().isEmpty()) {
                result.set("code", 400);
                result.set("msg", "用户组名称不能为空");
                return result.toString();
            }

            RoleVO role = roleService.createRole(name.trim(), description, isSystem);
            result.set("code", 200);
            result.set("data", role);
            result.set("msg", "创建用户组成功");
        } catch (Exception e) {
            log.error("创建用户组失败", e);
            result.set("code", 500);
            result.set("msg", "创建用户组失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 更新用户组信息
     */
    @PostMapping("/updateRole")
    public String updateRole(@RequestParam String id, @RequestBody JSONObject request) {
        JSONObject result = JSONUtil.createObj();
        try {
            String name = request.getStr("name");
            String description = request.getStr("description");

            RoleVO role = roleService.updateRole(id, name, description);
            result.set("code", 200);
            result.set("data", role);
            result.set("msg", "更新用户组成功");
        } catch (Exception e) {
            log.error("更新用户组失败", e);
            result.set("code", 500);
            result.set("msg", "更新用户组失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 删除用户组
     */
    @GetMapping("/deleteRole")
    public String deleteRole(String id) {
        JSONObject result = JSONUtil.createObj();
        try {
            roleService.deleteRole(id);
            result.set("code", 200);
            result.set("msg", "删除用户组成功");
        } catch (Exception e) {
            log.error("删除用户组失败", e);
            result.set("code", 500);
            result.set("msg", "删除用户组失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 获取系统内置用户组
     */
    @GetMapping("/getSystemRoles")
    public String getSystemRoles() {
        JSONObject result = JSONUtil.createObj();
        try {
            List<RoleVO> roles = roleService.findSystemRoles();
            result.set("code", 200);
            result.set("data", roles);
            result.set("msg", "获取系统用户组成功");
        } catch (Exception e) {
            log.error("获取系统用户组失败", e);
            result.set("code", 500);
            result.set("msg", "获取系统用户组失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 初始化系统默认用户组
     */
    @PostMapping("/initializeSystemRoles")
    public String initializeSystemRoles() {
        JSONObject result = JSONUtil.createObj();
        try {
            roleService.initializeSystemRoles();
            result.set("code", 200);
            result.set("msg", "初始化系统用户组成功");
        } catch (Exception e) {
            log.error("初始化系统用户组失败", e);
            result.set("code", 500);
            result.set("msg", "初始化系统用户组失败: " + e.getMessage());
        }
        return result.toString();
    }
}

package org.springblade.modules.controller;

import cn.hutool.json.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 语音服务控制器
 * <AUTHOR> [sijun.zeng]
 * @date 2025-05-25 10:00
 */
@RestController
@RequestMapping("/enclosure/voice")
@RequiredArgsConstructor
@Slf4j
public class VoiceController {

    private final RestTemplate restTemplate;

    @Value("${chat-voice.container}")
    private String chatVoiceUrl;

    /**
     * 语音转文字接口 - 转发到 chatvoice 服务
     * @param file 音频文件（PCM格式）
     * @param sampleRate 采样率，默认16000
     * @return 识别结果
     */
    @PostMapping("/stt/upload")
    public ResponseEntity<String> speechToText(
            @RequestParam(value = "audio_file") MultipartFile file,
            @RequestParam(value = "sample_rate", defaultValue = "16000") Integer sampleRate) {

        log.info("接收到语音转文字请求，文件名: {}, 采样率: {}", file.getOriginalFilename(), sampleRate);

        try {
            // 准备请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 准备请求体
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("sample_rate", sampleRate);

            // 将文件转换为 ByteArrayResource
            ByteArrayResource fileResource = new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return file.getOriginalFilename();
                }
            };
            body.add("audio_file", fileResource);

            // 创建 HTTP 实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求到 chatvoice 服务
            String url = chatVoiceUrl + "/api/stt/upload";
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            log.info("语音转文字请求成功，响应状态码: {}", response.getStatusCode());

            // 返回响应
            return ResponseEntity.status(response.getStatusCode())
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(response.getBody());

        } catch (IOException e) {
            log.error("处理文件时出错", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.set("status", "error");
            errorResponse.set("message", "处理文件时出错: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(errorResponse.toString());
        } catch (Exception e) {
            log.error("请求 chatvoice 服务时出错", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.set("status", "error");
            errorResponse.set("message", "请求语音服务时出错: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(errorResponse.toString());
        }
    }
}

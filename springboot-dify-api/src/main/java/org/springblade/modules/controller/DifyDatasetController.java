package org.springblade.modules.controller;

import cn.hutool.json.JSONArray;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.service.DatasetRetrieveLogService;
import org.springblade.modules.service.DifyDatasetService;
import org.springblade.modules.vo.DifyDatasetParamVO;
import org.springblade.modules.vo.DifyDatasetSearchVO;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.web.bind.annotation.*;

/**
 * Dify的知识库接口
 * <AUTHOR> [sijun.zeng]
 * @date 2025-05-20 22:18
 */
@RestController
@RequestMapping("/enclosure/dify/dataset")
@RequiredArgsConstructor
@Slf4j
public class DifyDatasetController {

    private final DifyDatasetService difyDatasetService;
    private final DatasetRetrieveLogService datasetRetrieveLogService;
    private final DifyDatasetParamVO difyDatasetParamVO;
    private final HttpServletRequest request;

    /**
     * 检索知识库
     * @param searchVO 检索参数
     * @return 检索结果
     */
    @GetMapping("/retrieve")
    public String retrieveDatasets(DifyDatasetSearchVO searchVO) {
        try {
            // 获取当前用户信息，可以用于日志记录或权限检查
//            UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
//            if (userPrincipal != null) {
//                CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
//                String user = principal.getAttributes().getName();
//                log.info("User {} is retrieving datasets with query: {}", user, searchVO.getQuery());
//
//                // 记录检索日志
//                datasetRetrieveLogService.saveLog(
//                    searchVO,
//                    difyDatasetParamVO.getSearchIds(),
//                    results.size(),
//                    principal
//                );
//
//                return results;
//            } else {
//                log.warn("User not authenticated when retrieving datasets");
//                return new JSONArray();
//            }

            // 调用服务层方法检索知识库
            JSONArray results = difyDatasetService.retrieveDatasets(searchVO);
            return results.toString();
        } catch (Exception e) {
            log.error("Error retrieving datasets", e);
            return null;
        }
    }

    /**
     * 获取当前用户的检索日志
     * @return 检索日志列表
     */
    @GetMapping("/logs")
    public Object getUserRetrieveLogs() {
        return datasetRetrieveLogService.getCurrentUserLogs();
    }

    /**
     * 获取所有检索日志（管理员使用）
     * @return 检索日志列表
     */
    @GetMapping("/logs/all")
    public Object getAllRetrieveLogs() {
        return datasetRetrieveLogService.getAllLogs();
    }

    /**
     * 根据关键词搜索检索日志
     * @param query 关键词
     * @return 检索日志列表
     */
    @GetMapping("/logs/search")
    public Object searchRetrieveLogs(@RequestParam String query) {
        return datasetRetrieveLogService.searchLogs(query);
    }
}

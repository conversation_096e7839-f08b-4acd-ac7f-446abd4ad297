package org.springblade.modules.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.service.UserService;
import org.springblade.modules.vo.UserVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户控制器，提供用户相关的API
 */
@RestController
@RequestMapping("/enclosure/user")
@RequiredArgsConstructor
@Slf4j
public class UserController {

    private final UserService userService;

    /**
     * 获取所有用户列表
     * @return 用户列表
     */
    @GetMapping("/list")
    public JSONObject getAllUsers() {
        JSONObject result = JSONUtil.createObj();
        try {
            List<UserVO> users = userService.findAllUsers();
            result.set("code", 200);
            result.set("data", users);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            result.set("code", 500);
            result.set("msg", "获取用户列表失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 根据用户ID获取用户信息
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/info")
    public JSONObject getUserInfo(@RequestParam("id") String id) {
        JSONObject result = JSONUtil.createObj();
        try {
            UserVO user = userService.findUserById(id);
            if (user != null) {
                result.set("code", 200);
                result.set("data", user);
            } else {
                result.set("code", 404);
                result.set("msg", "未找到指定用户");
            }
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            result.set("code", 500);
            result.set("msg", "获取用户信息失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 搜索用户
     * @param keyword 搜索关键词
     * @return 匹配的用户列表
     */
    @GetMapping("/search")
    public JSONObject searchUsers(@RequestParam(value = "keyword", required = false) String keyword) {
        JSONObject result = JSONUtil.createObj();
        try {
            List<UserVO> users;
            if (StrUtil.isNotBlank(keyword)) {
                users = userService.searchUsersByName(keyword);
            } else {
                users = userService.findAllUsers();
            }
            result.set("code", 200);
            result.set("data", users);
        } catch (Exception e) {
            log.error("搜索用户失败", e);
            result.set("code", 500);
            result.set("msg", "搜索用户失败：" + e.getMessage());
        }
        return result;
    }
}

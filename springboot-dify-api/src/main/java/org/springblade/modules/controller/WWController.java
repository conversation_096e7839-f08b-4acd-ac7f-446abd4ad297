package org.springblade.modules.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.qyweixin.common.WechatUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-05-07 10:53
 */
@RestController
@RequestMapping("/enclosure/ww")
@RequiredArgsConstructor
@Slf4j
public class WWController {

    @Value("${wechat.corp-id}")
    String corpId;
    @Value("${wechat.secret}")
    String secret;
    @Value("${wechat.agent-id}")
    String agentId;
    @Value("${qywx.url}")
    String wwUrl;

    //获取corpId
    @GetMapping("/getCorpId")
    public String getCorpId() {
        return corpId;
    }

    @GetMapping("/generateSignature")
    public JSONObject generateSignature(@RequestParam String url) {
        log.info("开始获取企业微信JS-SDK注册数据");

        // 获取企业微信的accessToken
        String accessToken = WechatUtil.accessToken(corpId, secret);
        log.info("获取到accessToken: {}", accessToken);

        // 生成随机字符串作为noncestr
        String noncestr = java.util.UUID.randomUUID().toString().replace("-", "");

        // 获取当前时间戳
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);

        // 获取JS-SDK签名
        String signature = WechatUtil.getJsapiSignature(accessToken, noncestr, timestamp, url);
        log.info("生成的JS-SDK签名: {}", signature);

        // 构建返回数据
        JSONObject result = new JSONObject();
        result.put("corpid", corpId);
        result.put("agentid", agentId);
        result.put("timestamp", timestamp);
        result.put("nonceStr", noncestr);
        result.put("signature", signature);

        log.info("返回企业微信JS-SDK注册数据: {}", result);
        return result;
    }

}

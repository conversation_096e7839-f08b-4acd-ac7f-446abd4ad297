package org.springblade.modules.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springblade.modules.req.DifyFile;
import org.springblade.modules.resp.BlockResponse;
import org.springblade.modules.resp.StreamResponse;
import org.springblade.modules.security.jwt.JwtTokenProvider;
import org.springblade.modules.service.DifyService;
import org.springblade.modules.service.RedisService;
import lombok.RequiredArgsConstructor;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/enclosure/dify")
@RequiredArgsConstructor
public class DifyApiController {

    private final HttpServletRequest request;

    private final HttpServletResponse response;

    private final DifyService difyService;

    private final JwtTokenProvider jwtTokenProvider;

    private final RedisService redisService;

    /**
     * 阻塞式调用
     *
     * @param query
     * @param key
     * @param conversationId
     * @return
     */
    @GetMapping("/block")
    public String blockApi(@RequestParam String query, @RequestParam String key, @RequestParam String conversationId) {

        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        BlockResponse blockResponse = difyService.blockingMessage(query, principal.getAttributes().getName(), key, conversationId);
        return blockResponse.getAnswer();
    }

    /**
     * 流式调用
     *
     * @param query
     * @param key
     * @param conversationId
     * @return
     */
    @GetMapping("/stream")
    public Flux<StreamResponse> testSteamApi(@RequestParam String query,
                                             @RequestParam String key,
                                             @RequestParam(value = "token") String token,
                                             @RequestParam(value = "conversationId", required = false) String conversationId,
                                             @RequestParam(value = "fileList", required = false) String fileListJson,
                                             @RequestParam(value = "inputs", required = false) String inputs,
                                             @RequestParam(value = "parentMessageId", required = false) String parentMessageId
    ) {
        try {
            // 从JWT token中获取用户信息，而不是从当前认证上下文中获取
            Authentication authentication = jwtTokenProvider.getAuthentication(token);
            CustomUserDetails principal = (CustomUserDetails) authentication.getPrincipal();

            List<DifyFile> fileList = null;
            if (StrUtil.isNotBlank(fileListJson)) {
                // fileListJson 转换List<DifyFile>
                fileList = JSONUtil.toList(JSONUtil.parseArray(fileListJson), DifyFile.class);
            }

            JSONObject inputsJson = null;
            if (StrUtil.isNotBlank(inputs)) {
                inputsJson = JSONUtil.parseObj(inputs);
            }

            Flux<StreamResponse> streamResponseFlux = difyService.streamingMessage(query, principal.getAttributes(), key, conversationId, fileList, inputsJson, parentMessageId);
            return streamResponseFlux;
        } catch (Exception e) {
            // 处理令牌无效或解析失败的情况
            throw new RuntimeException("Invalid or expired token: " + e.getMessage(), e);
        }
    }

    /**
     * 文件上传
     *
     * @param file
     * @param fileId
     * @return
     */
    @PostMapping("/upload")
    public String upload(@RequestParam MultipartFile file,
                         @RequestParam String agentId) {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        String user = principal.getAttributes().getName();

        JSONObject upload = difyService.upload(agentId, file, user);
        return upload.toString();
    }

    /**
     * 获取建议
     *
     * @param messageId
     * @param key
     * @return
     */
    @GetMapping("/suggested")
    public JSONObject suggested(@RequestParam String messageId, @RequestParam String key) {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        String user = principal.getAttributes().getName();
        return difyService.suggested(messageId, key, user);
    }

    /**
     * 停止任务
     *
     * @param key
     * @param taskId
     * @return
     */
    @GetMapping("/stop")
    public Integer stop(@RequestParam String key, @RequestParam String taskId) {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        String user = principal.getAttributes().getName();
        return difyService.stop(key, taskId, user);
    }

    /**
     * 获取会话
     *
     * @param key
     * @return
     */
    @GetMapping("/conversations")
    public JSONObject conversations(@RequestParam String key) {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        String user = principal.getAttributes().getName();
        return difyService.conversations(user, key);
    }

    /**
     * 获取消息
     *
     * @param key
     * @param conversationId
     * @return
     */
    @GetMapping("/message")
    public String message(@RequestParam String key, @RequestParam String conversationId) {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        String user = principal.getAttributes().getName();
        JSONObject message = difyService.message(user, key, conversationId);
        return message.toString();
    }

    /**
     * 获取参数
     *
     * @param key
     * @return
     */
    @GetMapping("/parameters")
    public JSONObject parameters(@RequestParam String key) {
        return difyService.parameters(key);
    }

    @RequestMapping(value = "/user/info", produces = "application/json")
    public @ResponseBody Object userInfo() {
        if (request.getUserPrincipal() == null) {
            response.setStatus(403);
            return null;
        }
        return request.getUserPrincipal();
    }

    @GetMapping("/deleteConversation")
    public String deleteConversation(@RequestParam String key, @RequestParam String conversationId) {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        String user = principal.getAttributes().getName();
        return difyService.deleteConversation(user, key, conversationId);
    }

    @PostMapping("/renameConversation")
    public JSONObject renameConversation(@Valid @RequestBody Map<String, String> map) {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        String user = principal.getAttributes().getName();
        String agentId = map.get("agentId");
        String conversationId = map.get("conversationId");
        String newName = map.get("newName");
        return difyService.renameConversation(agentId, conversationId, user, newName);
    }

    @GetMapping("/info")
    public JSONObject info(@RequestParam String host, @RequestParam String apiKey) {
        return difyService.info(host, apiKey);
    }

    @GetMapping("/feedback")
    public JSONObject sendFeedback(@RequestParam String agentId, @RequestParam String messageId, @RequestParam(required = false) String rating) {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        String user = principal.getAttributes().getName();
        String content = "";

        if (rating != null) {
            content = switch (rating) {
                case "like" -> "点赞";
                case "dislike" -> "点踩";
                default -> {
                    rating = "like";
                    yield "点赞";
                }
            };
        }

        return difyService.sendFeedback(agentId, user, messageId, rating, content);
    }

    /**
     * WPS文档上传接口 - 使用JSON格式
     * 保存文档名称和内容到Redis，返回文档ID
     *
     * @return 包含文档ID的JSON对象
     */
    @PostMapping("/wpsDocumentUpload")
    public String wpsDocumentUpload(@RequestParam MultipartFile file,
                                        @RequestParam String agentId) {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        String user = principal.getAttributes().getName();

        // 修改后缀名为txt，上传的文件名是不确定的
        String fileName = file.getOriginalFilename();
        if (fileName != null) {
            int dotIndex = fileName.lastIndexOf('.');
            if (dotIndex != -1) {
                fileName = fileName.substring(0, dotIndex) + ".txt";
            }
        }


        return difyService.upload(agentId, file, user, fileName).toString();
    }


}

package org.springblade.modules.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/enclosure/public")
@RequiredArgsConstructor
public class PublicController {

    @RequestMapping("/generateMeiliSearchIndex")
    public JSONObject generateMeiliSearchIndex() {
        // 生成纯数字的唯一 ID
        String uniqueId = IdUtil.getSnowflakeNextIdStr();

        // 获取当前日期时间并格式化
        LocalDateTime now = LocalDateTime.now();
        String dateTimeStr = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));

        // 创建JSON对象并添加数据
        JSONObject jsonObject = JSONUtil.createObj()
                .set("id", uniqueId)
                .set("dateTime", dateTimeStr);

        // 返回JSON字符串
        return jsonObject;
    }

}

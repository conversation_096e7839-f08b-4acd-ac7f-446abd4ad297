package org.springblade.modules.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.service.RoleService;
import org.springblade.modules.service.UserRoleService;
import org.springblade.modules.vo.RoleVO;
import org.springblade.modules.vo.UserRoleVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户用户组关联管理控制器
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-27
 */
@RestController
@RequestMapping("/enclosure/user-roles")
@RequiredArgsConstructor
@Slf4j
public class UserRoleController {

    private final UserRoleService userRoleService;
    private final RoleService roleService;

    /**
     * 获取用户所属的用户组列表
     */
    @GetMapping("/getUserRoles")
    public String getUserRoles(String userId) {
        JSONObject result = JSONUtil.createObj();
        try {
            List<RoleVO> roles = roleService.findRolesByUserId(userId);
            result.set("code", 200);
            result.set("data", roles);
            result.set("msg", "获取用户用户组列表成功");
        } catch (Exception e) {
            log.error("获取用户用户组列表失败", e);
            result.set("code", 500);
            result.set("msg", "获取用户用户组列表失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 获取用户组下的用户列表
     */
    @GetMapping("/getRoleUsers")
    public String getRoleUsers(String roleId) {
        JSONObject result = JSONUtil.createObj();
        try {
            List<UserRoleVO> userRoles = userRoleService.findUserRolesByRoleId(roleId);
            result.set("code", 200);
            result.set("data", userRoles);
            result.set("msg", "获取用户组用户列表成功");
        } catch (Exception e) {
            log.error("获取用户组用户列表失败", e);
            result.set("code", 500);
            result.set("msg", "获取用户组用户列表失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 为用户分配用户组
     */
    @PostMapping("/assignRoleToUser")
    public String assignRoleToUser(@RequestBody JSONObject request) {
        JSONObject result = JSONUtil.createObj();
        try {
            String userId = request.getStr("userId");
            String roleId = request.getStr("roleId");

            if (userId == null || roleId == null) {
                result.set("code", 400);
                result.set("msg", "用户ID和用户组ID不能为空");
                return result.toString();
            }

            UserRoleVO userRole = userRoleService.assignRoleToUser(userId, roleId);
            result.set("code", 200);
            result.set("data", userRole);
            result.set("msg", "分配用户组成功");
        } catch (Exception e) {
            log.error("分配用户组失败", e);
            result.set("code", 500);
            result.set("msg", "分配用户组失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 移除用户的用户组
     */
    @GetMapping("/removeRoleFromUser")
    public String removeRoleFromUser(String userId, String roleId) {
        JSONObject result = JSONUtil.createObj();
        try {
            userRoleService.removeRoleFromUser(userId, roleId);
            result.set("code", 200);
            result.set("msg", "移除用户组成功");
        } catch (Exception e) {
            log.error("移除用户组失败", e);
            result.set("code", 500);
            result.set("msg", "移除用户组失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 批量为用户分配用户组
     */
    @PostMapping("/batch/assignRolesToUser")
    public String assignRolesToUser(@RequestBody JSONObject request) {
        JSONObject result = JSONUtil.createObj();
        try {
            String userId = request.getStr("userId");
            List<String> roleIds = request.getBeanList("roleIds", String.class);

            if (userId == null || roleIds == null) {
                result.set("code", 400);
                result.set("msg", "用户ID和用户组ID列表不能为空");
                return result.toString();
            }

            userRoleService.assignRolesToUser(userId, roleIds);
            result.set("code", 200);
            result.set("msg", "批量分配用户组成功");
        } catch (Exception e) {
            log.error("批量分配用户组失败", e);
            result.set("code", 500);
            result.set("msg", "批量分配用户组失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 批量为用户组分配用户
     */
    @PostMapping("/batch/assignUsersToRole")
    public String assignUsersToRole(@RequestBody JSONObject request) {
        JSONObject result = JSONUtil.createObj();
        try {
            String roleId = request.getStr("roleId");
            List<String> userIds = request.getBeanList("userIds", String.class);

            if (roleId == null || userIds == null) {
                result.set("code", 400);
                result.set("msg", "用户组ID和用户ID列表不能为空");
                return result.toString();
            }

            userRoleService.assignUsersToRole(roleId, userIds);
            result.set("code", 200);
            result.set("msg", "批量分配用户成功");
        } catch (Exception e) {
            log.error("批量分配用户失败", e);
            result.set("code", 500);
            result.set("msg", "批量分配用户失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 检查用户是否属于指定用户组
     */
    @GetMapping("/checkUserInRole")
    public String checkUserInRole(@RequestParam String userId, @RequestParam String roleId) {
        JSONObject result = JSONUtil.createObj();
        try {
            boolean isInRole = userRoleService.isUserInRole(userId, roleId);
            result.set("code", 200);
            result.set("data", isInRole);
            result.set("msg", "检查用户用户组关系成功");
        } catch (Exception e) {
            log.error("检查用户用户组关系失败", e);
            result.set("code", 500);
            result.set("msg", "检查用户用户组关系失败: " + e.getMessage());
        }
        return result.toString();
    }

    /**
     * 为用户初始化默认用户组
     */
    @PostMapping("/initializeUserDefaultRole")
    public String initializeUserDefaultRole(String userId) {
        JSONObject result = JSONUtil.createObj();
        try {
            userRoleService.initializeUserDefaultRole(userId);
            result.set("code", 200);
            result.set("msg", "初始化用户默认用户组成功");
        } catch (Exception e) {
            log.error("初始化用户默认用户组失败", e);
            result.set("code", 500);
            result.set("msg", "初始化用户默认用户组失败: " + e.getMessage());
        }
        return result.toString();
    }
}

package org.springblade.modules.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.springblade.modules.entity.UserAgentList;
import org.springblade.modules.service.UserAgentListService;
import org.springblade.modules.vo.UserAgentListVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-04-01 22:19
 */
@RestController
@RequestMapping("/enclosure/userAgentList")
@RequiredArgsConstructor
public class UserAgentListController {

    private final UserAgentListService userAgentListService;

    // 1. 获取userAgent列表
    @GetMapping("/list")
    public List<UserAgentListVO> getUserAgentList() {
        return userAgentListService.findAllByCreateByOrderByOrder();
    }

    // 2. 删除userAgent
    @GetMapping("/delete")
    public void deleteUserAgent(@RequestParam("id") String id) {
        userAgentListService.delete(id);
    }

    // 3. 调整userAgent的排序
//    @GetMapping("/updateOrder")
//    public void updateUserAgentOrder(@RequestParam Integer order, @RequestParam String id) {
//        userAgentListService.updateOrderById(order, id);
//    }

    // 4. 调整userAgent的置顶
    @GetMapping("/top")
    public void updateIsTopById(@RequestParam Boolean isTop, @RequestParam String id) {
        userAgentListService.updateIsTopById(isTop, id);
    }

    // 4. 新增userAgent
    @PostMapping("/add")
    public JSONObject addUserAgent(@RequestBody UserAgentList userAgentList) {
        return userAgentListService.save(userAgentList);
    }

}

package org.springblade.modules.controller;

import cn.hutool.json.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.security.jwt.JwtTokenProvider;
import org.springblade.modules.service.MinioService;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.io.InputStream;

/**
 * Minio 控制器
 */
@Slf4j
@RestController
@RequestMapping("/enclosure/minio")
@RequiredArgsConstructor
public class MinioController {

    private final MinioService minioService;
    private final HttpServletRequest request;
    private final JwtTokenProvider jwtTokenProvider;
    /**
     * 上传文件
     *
     * @param file 文件
     * @return 文件信息
     */
    @PostMapping("/upload")
    public JSONObject upload(@RequestParam("file") MultipartFile file,
                             @RequestParam(value = "token") String token) {
        // 从JWT token中获取用户信息，而不是从当前认证上下文中获取
        Authentication authentication = jwtTokenProvider.getAuthentication(token);
        CustomUserDetails principal = (CustomUserDetails) authentication.getPrincipal();

        // 获取当前用户信息
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        String user = principal.getAttributes().getName();
        
        log.info("用户 {} 上传文件: {}", user, file.getOriginalFilename());
        
        return minioService.uploadFile(file);
    }

    /**
     * 获取文件
     *
     * @param objectName 对象名称
     * @return 文件流
     */
    @GetMapping("/object/{objectName}")
    public InputStream getObject(@PathVariable String objectName) throws Exception {
        return minioService.getObject(objectName);
    }

    /**
     * 删除文件
     *
     * @param objectName 对象名称
     * @return 操作结果
     */
    @DeleteMapping("/object/{objectName}")
    public JSONObject removeObject(@PathVariable String objectName) {
        JSONObject result = new JSONObject();
        try {
            minioService.removeObject(objectName);
            result.set("success", true);
            result.set("message", "文件删除成功");
        } catch (Exception e) {
            log.error("删除文件失败", e);
            result.set("success", false);
            result.set("message", "文件删除失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取文件的临时访问URL
     *
     * @param objectName 对象名称
     * @param expiry     过期时间（以秒为单位），默认为7天
     * @return 临时访问URL
     */
    @GetMapping("/url/{objectName}")
    public JSONObject getPresignedObjectUrl(@PathVariable String objectName,
                                          @RequestParam(defaultValue = "604800") Integer expiry) {
        JSONObject result = new JSONObject();
        try {
            String url = minioService.getPresignedObjectUrl(objectName, expiry);
            result.set("success", true);
            result.set("url", url);
        } catch (Exception e) {
            log.error("获取文件URL失败", e);
            result.set("success", false);
            result.set("message", "获取文件URL失败: " + e.getMessage());
        }
        return result;
    }
}

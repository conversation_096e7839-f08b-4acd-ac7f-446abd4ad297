package org.springblade.modules.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import java.sql.Timestamp;

/**
 * 用户用户组关联实体
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-27
 */
@Data
@Builder
@Entity
@Table(name = "user_roles", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"user_id", "role_id"}))
@NoArgsConstructor
@AllArgsConstructor
public class UserRole {

    /**
     * 主键ID（UUID格式）
     */
    @Id
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(36)")
    private String id;

    /**
     * 用户ID（关联accounts表）
     */
    @Column(name = "user_id", nullable = false, columnDefinition = "VARCHAR(36)")
    private String userId;

    /**
     * 用户组ID（关联roles表）
     */
    @Column(name = "role_id", nullable = false, columnDefinition = "VARCHAR(36)")
    private String roleId;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private Timestamp createdAt;

    @PrePersist
    protected void onCreate() {
        createdAt = new Timestamp(System.currentTimeMillis());
    }
}

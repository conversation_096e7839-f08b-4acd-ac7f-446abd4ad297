package org.springblade.modules.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import java.sql.Timestamp;
import java.util.UUID;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-04-01 17:22
 */
@Data
@Builder
@Entity
@Table(name = "portal_chat_history")
@NoArgsConstructor
@AllArgsConstructor
public class ChatHistory {

    /**
     * 主键ID（UUID格式）
     */
    @Id
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(36)")
    private String id;

    /**
     * 智能体的ID（关联智能体字典表）
     */
    @Column(name = "agent_dict_id")
    private String agentDictId;

    /**
     * Dify系统的聊天记录唯一标识
     */
    @Column(name = "conversation_id")
    private String conversationId;

    /**
     * 对话名称（用户自定义的对话标题）
     */
    private String name;

    /**
     * 是否置顶标记（true表示置顶显示）
     */
    @Column(name = "is_top", columnDefinition = "TINYINT(1)")
    private Boolean isTop;

    /**
     * 逻辑删除标记（true表示已删除）
     */
    @Column(name = "is_deleted", columnDefinition = "TINYINT(1)")
    private Boolean isDeleted;

    /**
     * 创建人标识（UUID格式，通常为用户ID）
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间戳
     */
    @Column(name = "create_at")
    private Timestamp createAt;

}

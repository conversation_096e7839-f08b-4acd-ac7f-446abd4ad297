package org.springblade.modules.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import java.sql.Timestamp;

/**
 * 优化建议箱实体类
 */
@Data
@Builder
@Entity
@Table(name = "optimization_suggestions")
@NoArgsConstructor
@AllArgsConstructor
public class OptimizationSuggestion {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(36)")
    private String id;

    /**
     * 优化建议标题（不超过150字）
     */
    @Column(name = "title", nullable = false, length = 200)
    private String title;

    /**
     * 详细描述（不超过800字）
     */
    @Column(name = "description", nullable = false, length = 1000)
    private String description;

    /**
     * 提交用户ID
     */
    @Column(name = "user_id", nullable = false)
    private String userId;

    /**
     * 提交用户名称
     */
    @Column(name = "user_name", nullable = false)
    private String userName;

    /**
     * 提交时间
     */
    @Column(name = "create_time", nullable = false)
    private Timestamp createTime;


}

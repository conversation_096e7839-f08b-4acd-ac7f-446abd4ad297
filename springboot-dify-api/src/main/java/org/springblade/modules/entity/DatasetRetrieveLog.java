package org.springblade.modules.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import java.sql.Timestamp;

/**
 * 知识库检索日志实体类
 * <AUTHOR> [sijun.zeng]
 * @date 2025-05-21 10:30
 */
@Data
@Builder
@Entity
@Table(name = "portal_dataset_retrieve_log")
@NoArgsConstructor
@AllArgsConstructor
public class DatasetRetrieveLog {

    /**
     * 主键ID（UUID格式）
     */
    @Id
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(36)")
    private String id;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private String userId;

    /**
     * 用户名称
     */
    @Column(name = "user_name", nullable = false)
    private String userName;

    /**
     * 检索关键词
     */
    @Column(name = "query", nullable = false)
    private String query;

    /**
     * 检索方法
     */
    @Column(name = "search_method")
    private String searchMethod;

    /**
     * 检索的知识库ID列表，以逗号分隔
     */
    @Column(name = "dataset_ids", length = 1000)
    private String datasetIds;

    /**
     * 检索结果数量
     */
    @Column(name = "result_count")
    private Integer resultCount;

    /**
     * 检索时间
     */
    @Column(name = "retrieve_time", nullable = false)
    private Timestamp retrieveTime;

    /**
     * IP地址
     */
    @Column(name = "ip_address")
    private String ipAddress;
}

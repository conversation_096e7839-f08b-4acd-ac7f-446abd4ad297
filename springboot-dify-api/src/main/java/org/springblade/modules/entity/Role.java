package org.springblade.modules.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import java.sql.Timestamp;

/**
 * 用户组实体
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-27
 */
@Data
@Builder
@Entity
@Table(name = "roles")
@NoArgsConstructor
@AllArgsConstructor
public class Role {

    /**
     * 主键ID（UUID格式）
     */
    @Id
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(36)")
    private String id;

    /**
     * 用户组名称
     */
    @Column(name = "name", nullable = false, length = 255)
    private String name;

    /**
     * 用户组描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 是否系统内置用户组（true表示系统内置，不可删除）
     */
    @Column(name = "is_system", columnDefinition = "TINYINT(1) DEFAULT 0")
    private Boolean isSystem = false;

    /**
     * 逻辑删除标记（true表示已删除）
     */
    @Column(name = "is_deleted", columnDefinition = "TINYINT(1) DEFAULT 0")
    private Boolean isDeleted = false;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private Timestamp createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private Timestamp updatedAt;

    @PrePersist
    protected void onCreate() {
        Timestamp now = new Timestamp(System.currentTimeMillis());
        createdAt = now;
        updatedAt = now;
        if (isSystem == null) {
            isSystem = false;
        }
        if (isDeleted == null) {
            isDeleted = false;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Timestamp(System.currentTimeMillis());
    }
}

package org.springblade.modules.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import java.sql.Timestamp;
import java.util.UUID;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-04-01 17:22
 */
@Data
@Builder
@Entity
@Table(name = "portal_user_agent_list")
@NoArgsConstructor
@AllArgsConstructor
public class UserAgentList {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(36)")
    private String id;

    /**
     * 智能体的ID
     */
    @Column(name = "agent_dict_id", columnDefinition = "VARCHAR(36)")
    private String agentDictId;

    /**
     * 是否置顶标记（true表示置顶显示）
     */
    @Column(name = "is_top", columnDefinition = "TINYINT(1)")
    private Boolean isTop;


    /**
     * 用户ID
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间戳
     */
    @Column(name = "create_at")
    private Timestamp createAt;

}

package org.springblade.modules.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import org.springblade.modules.entity.Account;
import org.springblade.modules.entity.AgentDict;
import org.springblade.modules.req.DifyFile;
import org.springblade.modules.req.DifyRequestBody;
import org.springblade.modules.resp.BlockResponse;
import org.springblade.modules.resp.StreamResponse;
import org.springblade.modules.util.FileProcessUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;


@Service
@RequiredArgsConstructor
@Slf4j
public class DifyService {

    private final AgentDictService agentDictService;

    private final RestTemplate restTemplate;

    private final WebClient webClient;

    @Autowired
    private RedisService redisService;

//    private final HashMap<String, String> fileContentMaps = new HashMap<>();

    /**
     * 流式调用dify.
     *
     * @param query  查询文本
     * @return Flux 响应流
     */
    public Flux<StreamResponse> streamingMessage(String query, Account userAccount, String agentId, String conversationId, List<DifyFile> fileList, JSONObject inputs, String parentMessageId) {
        if (StrUtil.isBlank(agentId)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }

        //1.设置请求体
        DifyRequestBody body = new DifyRequestBody();
        Map<String, String> map = new HashMap<>();
        if (inputs != null) {
            for (String key : inputs.keySet()) {
                map.put(key, inputs.getStr(key));
            }

//            // 检查是否有WPS文档ID
//            if (inputs.containsKey("wpsDocumentId")) {
//                String wpsDocumentId = inputs.getStr("wpsDocumentId");
//                if (StrUtil.isNotBlank(wpsDocumentId)) {
//                    // 从Redis获取文档信息
//                    Map<Object, Object> documentInfo = redisService.getWpsDocument(wpsDocumentId);
//                    if (documentInfo != null && !documentInfo.isEmpty()) {
//                        // 将文档名称和内容添加到inputs中
//                        map.put("document_name", documentInfo.get("name").toString());
//                        map.put("document_content", documentInfo.get("content").toString());
//                        log.info("从Redis获取WPS文档信息成功，ID: {}, 名称: {}", wpsDocumentId, documentInfo.get("name"));
//                    } else {
//                        log.warn("未找到WPS文档或文档已过期，ID: {}", wpsDocumentId);
//                    }
//                }
//            }
        }
        map.put("userId", userAccount.getWorkNo());
        body.setInputs(map);

//        if (StrUtil.isNotBlank(parentMessageId)) {
//            body.setParentMessageId(parentMessageId);
//        }

        if (fileList != null) {
            body.setFiles(fileList);
        }
        body.setQuery(query);
        body.setResponseMode("streaming");
        if (conversationId == null || conversationId.equals("null")) {
            conversationId = "";
        }
        body.setConversationId(conversationId);
        body.setUser(userAccount.getName());
        //2.使用webclient发送post请求
        return webClient.post()
                .uri(agentDict.getApiServerHost() + "/chat-messages")
                .headers(httpHeaders -> {
                    httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                    httpHeaders.setBearerAuth(agentDict.getApiKey());
                })
                .bodyValue(JSON.toJSONString(body))
                .retrieve()
                .bodyToFlux(StreamResponse.class);
    }


    /**
     * 阻塞式调用dify.
     *
     * @param query  查询文本
     * @return BlockResponse
     */
    public BlockResponse blockingMessage(String query, String userName, String agentId, String conversationId) {
        if (StrUtil.isBlank(agentId)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }

        //1.设置请求体
        DifyRequestBody body = new DifyRequestBody();
        body.setInputs(new HashMap<>());
        body.setQuery(query);
        body.setResponseMode("blocking");
        body.setConversationId(conversationId);
        body.setUser(userName);
        //2.设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        headers.setBearerAuth(agentDict.getApiKey());
        //3.封装请求体和请求头
        String jsonString = JSON.toJSONString(body);
        HttpEntity<String> entity = new HttpEntity<>(jsonString, headers);
        //4.发送post请求，阻塞式
        ResponseEntity<BlockResponse> stringResponseEntity =
                restTemplate.postForEntity(agentDict.getApiServerHost() + "/chat-messages", entity, BlockResponse.class);
        //5.返回响应体
        return stringResponseEntity.getBody();
    }

//    public String upload(String fileId, MultipartFile file) {
//        try {
//            List<String> list = FileProcessUtils.processFile(file);
//            String content = String.join("\n", list) + "\n";
//            fileContentMaps.put(fileId, content);
//            return "success";
//        } catch (IOException e) {
//            log.error(e.getMessage());
//        }
//        return "fail";
//
//    }

    public JSONObject upload(String agentId, MultipartFile file, String user) {
        return upload(agentId, file, user, null);
    }

    public JSONObject upload(String agentId, MultipartFile file, String user, String customFileName) {
        if (StrUtil.isBlank(agentId)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.setBearerAuth(agentDict.getApiKey());

            // 创建请求体
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
//            Resource resource = new InputStreamResource(file.getInputStream()) {
//                @Override
//                public String getFilename() {
//                    return file.getOriginalFilename();
//                }
//            };

            byte[] fileBytes = file.getBytes();
            Resource resource = new ByteArrayResource(fileBytes) {
                @Override
                public String getFilename() {
                    return StrUtil.isNotBlank(customFileName) ? customFileName : file.getOriginalFilename();
                }
            };
            body.add("file", resource);
            body.add("user", user);

            // 创建 HttpEntity
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送 POST 请求
            String serverUrl = agentDict.getApiServerHost() + "/files/upload";
            String response = restTemplate.postForObject(serverUrl, requestEntity, String.class);

            System.out.println("Response: " + response);

            // 输出响应
            JSONObject obj = JSONUtil.createObj();
            obj.set("code", 200);
            obj.set("data", response);
            return obj;
        } catch (HttpClientErrorException e) {
            JSONObject obj = JSONUtil.createObj();
            if (e.getStatusCode().value() == 413) {
                obj.set("code", 401);
                obj.set("message", "文件过大");
            } else if (e.getStatusCode().value() == 415) {
                obj.set("code", 415);
                obj.set("message", "不支持的扩展名");
            } else {
                obj.set("code", e.getStatusCode().value());
                obj.set("message", e.getStatusText());
            }
            return obj;
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        //3.返回响应体
        JSONObject obj = JSONUtil.createObj();
        obj.set("code", 401);
        return obj;
    }

    /**
     * 获取下一轮建议
     * @param messageId
     * @param agentId
     * @return
     */
    public JSONObject suggested(String messageId, String agentId, String user) {
        if (StrUtil.isBlank(agentId)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }


        String suggest = "";
        try {
            //1.设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(List.of(MediaType.APPLICATION_JSON));
            headers.setBearerAuth(agentDict.getApiKey());

            //2.封装请求体和请求头
            HttpEntity<String> entity = new HttpEntity<>(headers);
            String url1 = agentDict.getApiServerHost() + "/messages/" + messageId + "/suggested?user=" + user;
            ResponseEntity<String> response = restTemplate.exchange(
                    url1,
                    HttpMethod.GET,
                    entity,
                    String.class
            );
            return JSONUtil.parseObj(response.getBody());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        //3.返回响应体
        JSONObject obj = JSONUtil.createObj();
        obj.set("code", 401);
        return obj;
    }

    /**
     * 停止响应
     * @param agentId
     * @param taskId
     * @param user
     * @return
     */
    public Integer stop(String agentId, String taskId, String user) {
        if (StrUtil.isBlank(agentId)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }

        try {
            // 1. 设置请求体
            String jsonInputString = "{\"user\": \"" + user + "\"}";

            // 2. 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(agentDict.getApiKey());

            // 3. 封装请求体和请求头
            HttpEntity<String> entity = new HttpEntity<>(jsonInputString, headers);


            // 4. 发送 POST 请求
            String url = agentDict.getApiServerHost() + "/chat-messages/" + taskId + "/stop";
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            // 5. 返回响应码
            Integer responseCode = response.getStatusCode().value();
            System.out.println("响应码: " + responseCode);
            return responseCode;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return 401;
    }

    /**
     * 获取会话列表
     * @param user
     * @param agentId
     * @return
     */
    public JSONObject conversations(String user, String agentId) {
        if (StrUtil.isBlank(agentId)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }

        String url = agentDict.getApiServerHost() + "/conversations?user=" + user + "&last_id=&limit=20";

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(agentDict.getApiKey());

            // 封装请求头
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            // 发送 GET 请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            // 输出响应状态码和响应体
            System.out.println("Status Code: " + response.getStatusCode());
            System.out.println("Response Body: " + response.getBody());

            return JSONUtil.parseObj(response.getBody());
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        JSONObject obj = JSONUtil.createObj();
        obj.set("code", 401);
        return obj;
    }

    /**
     * 获取会话消息
     * @param user
     * @param agentId
     * @param conversationId
     * @return
     */
    public JSONObject message(String user, String agentId, String conversationId) {
        if (StrUtil.isBlank(agentId)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }

        try {
            // 构建请求 URL
            String url = agentDict.getApiServerHost() + "/messages?user=" + user + "&conversation_id=" + conversationId;

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(agentDict.getApiKey());

            // 封装请求头
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            // 发送 GET 请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            // 输出响应状态码和响应体
            System.out.println("Status Code: " + response.getStatusCode());
            System.out.println("Response Body: " + response.getBody());

            return JSONUtil.parseObj(response.getBody());
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        JSONObject obj = JSONUtil.createObj();
        obj.set("code", 401);
        return obj;

    }

    /**
     * 获取参数
     * @param agentId
     * @return
     */
    public JSONObject parameters(String agentId) {
        if (StrUtil.isBlank(agentId)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }

        try {
            // 构建请求 URL
            String url = agentDict.getApiServerHost() + "/parameters";

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(agentDict.getApiKey());

            // 封装请求头
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            System.out.println("Status Code: " + response.getStatusCode());
            System.out.println("Response Body: " + response.getBody());

            return JSONUtil.parseObj(response.getBody());
        }
        catch (Exception e) {
            log.error(e.getMessage());
        }

        JSONObject obj = JSONUtil.createObj();
        obj.set("code", 401);
        return obj;
    }

    /**
     * 删除会话
     * @param user
     * @param agentId
     * @param conversationId
     * @return
     */
    public String deleteConversation(String user, String agentId, String conversationId) {
        if (StrUtil.isBlank(agentId)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }

        try {
            // 构建请求 URL
            String url = agentDict.getApiServerHost() + "/conversations/" + conversationId;

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(agentDict.getApiKey());

            String requestBody = "{\"user\": \"" + user + "\"}";

            // 封装请求头
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);

            // 发送DELETE请求
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.DELETE,
                    entity,
                    String.class
            );

            return response.getBody();
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        JSONObject obj = JSONUtil.createObj();
        obj.set("code", 401);
        return obj.toString();
    }

    /**
     * 重命名会话
     * @param agentId
     * @param conversationId
     * @param user
     * @param newName
     * @return
     */
    public JSONObject renameConversation(String agentId, String conversationId, String user, String newName) {
        if (StrUtil.isBlank(conversationId) || StrUtil.isBlank(agentId) || StrUtil.isBlank(user) || StrUtil.isBlank(newName)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }

        try {
            // 1. 设置请求体
            String jsonInputString = "{\"name\": \"" + newName + "\", \"auto_generate\": false, \"user\": \"" + user + "\"}";

            // 2. 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(agentDict.getApiKey());

            // 3. 封装请求体和请求头
            HttpEntity<String> entity = new HttpEntity<>(jsonInputString, headers);

            // 4. 发送 POST 请求
            String url = agentDict.getApiServerHost() + "/conversations/" + conversationId + "/name";
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            // 5. 返回响应体
            return JSONUtil.parseObj(response.getBody());
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        // 6. 返回错误响应
        JSONObject obj = JSONUtil.createObj();
        obj.set("code", 401);
        return obj;
    }

    /**
     * 获取下一轮建议
     * @return
     */
    public JSONObject info(String host, String apiKey) {
        if (StrUtil.isBlank(host) || StrUtil.isBlank(apiKey)) {
            return null;
        }

        try {
            //1.设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            //2.封装请求体和请求头
            HttpEntity<String> entity = new HttpEntity<>(headers);
            String url1 = host + "/info";
            ResponseEntity<String> response = restTemplate.exchange(
                    url1,
                    HttpMethod.GET,
                    entity,
                    String.class
            );
            return JSONUtil.parseObj(response.getBody());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        //3.返回响应体
        JSONObject obj = JSONUtil.createObj();
        obj.set("code", 401);
        return obj;
    }

    /**
     * 发送反馈
     * @param agentId
     * @param user
     * @param messageId
     * @param rating
     * @param content
     * @return
     */
    public JSONObject sendFeedback(String agentId, String user, String messageId, String rating, String content) {
        if (StrUtil.isBlank(agentId) || StrUtil.isBlank(user) || StrUtil.isBlank(messageId)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }

        // 1. 构建请求 URL
        String url = agentDict.getApiServerHost() + "/messages/" + messageId + "/feedbacks";

        // 2. 设置请求体
        JSONObject requestBody = new JSONObject();
        requestBody.set("rating", rating);
        requestBody.set("user", user);
        requestBody.set("content", content);

        // 3. 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(agentDict.getApiKey());

        // 4. 封装请求体和请求头
        HttpEntity<String> entity = new HttpEntity<>(requestBody.toString(), headers);

        // 5. 发送 POST 请求
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            return JSONUtil.parseObj(response.getBody());
        } catch (Exception e) {
            log.error("Error sending feedback: ", e);
        }

        // 6. 返回错误响应
        JSONObject obj = JSONUtil.createObj();
        obj.set("code", 401);
        return obj;
    }


}

package org.springblade.modules.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.entity.AccountIntegrates;
import org.springblade.modules.repository.AccountIntegratesRepository;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-03-17 00:13
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AccountIntegratesService {

    private final AccountIntegratesRepository accountIntegratesRepository;

    public AccountIntegrates save(AccountIntegrates accountIntegrates) {
        return accountIntegratesRepository.save(accountIntegrates);
    }

    public AccountIntegrates findByOpenId(String openId) {
        return accountIntegratesRepository.findByOpenId(openId);
    }

    public AccountIntegrates findByAccountIdAndProvider(String accountId, String provider) {
        return accountIntegratesRepository.findByAccountIdAndProvider(accountId, provider);
    }

}

package org.springblade.modules.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

/**
 * 系统初始化服务
 * 在应用启动时自动执行初始化任务
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-27
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SystemInitializationService implements ApplicationRunner {

    private final RoleService roleService;
    private final UserRoleService userRoleService;
    private final AccountService accountService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始执行系统初始化...");
        
        try {
            // 初始化系统默认用户组
            initializeSystemRoles();
            
            // 为现有用户初始化默认用户组
            initializeExistingUsersDefaultRoles();
            
            log.info("系统初始化完成");
        } catch (Exception e) {
            log.error("系统初始化失败", e);
        }
    }

    /**
     * 初始化系统默认用户组
     */
    private void initializeSystemRoles() {
        try {
            roleService.initializeSystemRoles();
            log.info("系统默认用户组初始化完成");
        } catch (Exception e) {
            log.error("系统默认用户组初始化失败", e);
        }
    }

    /**
     * 为现有用户初始化默认用户组
     */
    private void initializeExistingUsersDefaultRoles() {
        try {
            // 获取所有用户
            var users = accountService.findAll();
            
            for (var user : users) {
                try {
                    // 为每个用户初始化默认用户组
                    userRoleService.initializeUserDefaultRole(user.getId());
                } catch (Exception e) {
                    log.warn("为用户 {} 初始化默认用户组失败: {}", user.getName(), e.getMessage());
                }
            }
            
            log.info("现有用户默认用户组初始化完成");
        } catch (Exception e) {
            log.error("现有用户默认用户组初始化失败", e);
        }
    }
}

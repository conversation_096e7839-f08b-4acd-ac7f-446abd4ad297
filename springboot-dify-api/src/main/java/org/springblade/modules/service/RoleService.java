package org.springblade.modules.service;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.entity.Role;
import org.springblade.modules.repository.RoleRepository;
import org.springblade.modules.repository.UserRoleRepository;
import org.springblade.modules.vo.RoleVO;
import org.springblade.modules.vo.UserVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户组服务类
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-27
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RoleService {

    private final RoleRepository roleRepository;
    private final UserRoleRepository userRoleRepository;
    private final UserService userService;

    /**
     * 获取所有活跃的用户组
     */
    public List<RoleVO> findAllActiveRoles() {
        List<Role> roles = roleRepository.findAllActive();
        return roles.stream()
                .map(this::convertToRoleVO)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID获取用户组
     */
    public RoleVO findById(String id) {
        Role role = roleRepository.findById(id).orElse(null);
        return role != null && !role.getIsDeleted() ? convertToRoleVO(role) : null;
    }

    /**
     * 根据名称获取用户组
     */
    public RoleVO findByName(String name) {
        Role role = roleRepository.findByNameAndNotDeleted(name).orElse(null);
        return role != null ? convertToRoleVO(role) : null;
    }

    /**
     * 创建用户组
     */
    @Transactional
    public RoleVO createRole(String name, String description, Boolean isSystem) {
        // 检查名称是否已存在
        if (roleRepository.existsByNameAndNotDeleted(name)) {
            throw new RuntimeException("用户组名称已存在: " + name);
        }

        Role role = Role.builder()
                .name(name)
                .description(description)
                .isSystem(isSystem != null ? isSystem : false)
                .build();

        role = roleRepository.save(role);
        log.info("创建用户组成功: {}", role.getName());
        return convertToRoleVO(role);
    }

    /**
     * 更新用户组
     */
    @Transactional
    public RoleVO updateRole(String id, String name, String description) {
        Role role = roleRepository.findById(id).orElse(null);
        if (role == null || role.getIsDeleted()) {
            throw new RuntimeException("用户组不存在: " + id);
        }

        // 系统内置用户组不允许修改名称
        if (role.getIsSystem() && !role.getName().equals(name)) {
            throw new RuntimeException("系统内置用户组不允许修改名称");
        }

        // 检查名称是否已存在（排除当前用户组）
        if (StrUtil.isNotBlank(name) && !name.equals(role.getName())) {
            if (roleRepository.existsByNameAndNotDeletedExcludingId(name, id)) {
                throw new RuntimeException("用户组名称已存在: " + name);
            }
            role.setName(name);
        }

        if (StrUtil.isNotBlank(description)) {
            role.setDescription(description);
        }

        role = roleRepository.save(role);
        log.info("更新用户组成功: {}", role.getName());
        return convertToRoleVO(role);
    }

    /**
     * 删除用户组（软删除）
     */
    @Transactional
    public void deleteRole(String id) {
        Role role = roleRepository.findById(id).orElse(null);
        if (role == null || role.getIsDeleted()) {
            throw new RuntimeException("用户组不存在: " + id);
        }

        // 系统内置用户组不允许删除
        if (role.getIsSystem()) {
            throw new RuntimeException("系统内置用户组不允许删除");
        }

        // 删除用户组的所有用户关联
        userRoleRepository.deleteByRoleId(id);

        // 软删除用户组
        role.setIsDeleted(true);
        roleRepository.save(role);
        log.info("删除用户组成功: {}", role.getName());
    }

    /**
     * 获取用户所属的用户组列表
     */
    public List<RoleVO> findRolesByUserId(String userId) {
        List<Role> roles = roleRepository.findRolesByUserId(userId);
        return roles.stream()
                .map(this::convertToRoleVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取系统内置用户组
     */
    public List<RoleVO> findSystemRoles() {
        List<Role> roles = roleRepository.findSystemRoles();
        return roles.stream()
                .map(this::convertToRoleVO)
                .collect(Collectors.toList());
    }

    /**
     * 初始化系统默认用户组
     */
    @Transactional
    public void initializeSystemRoles() {
        // 创建管理员用户组
        if (!roleRepository.existsByNameAndNotDeleted("ADMIN")) {
            createRole("ADMIN", "系统管理员用户组，拥有所有权限", true);
        }

        // 创建普通用户组
        if (!roleRepository.existsByNameAndNotDeleted("USER")) {
            createRole("USER", "普通用户组，拥有基本权限", true);
        }

        log.info("系统默认用户组初始化完成");
    }

    /**
     * 将Role实体转换为RoleVO
     */
    private RoleVO convertToRoleVO(Role role) {
        // 获取用户组下的用户数量
        List<String> userIds = userRoleRepository.findUserIdsByRoleId(role.getId());
        
        return RoleVO.builder()
                .id(role.getId())
                .name(role.getName())
                .description(role.getDescription())
                .isSystem(role.getIsSystem())
                .createdAt(role.getCreatedAt())
                .updatedAt(role.getUpdatedAt())
                .userCount(userIds.size())
                .build();
    }

    /**
     * 将Role实体转换为RoleVO（包含用户列表）
     */
    public RoleVO convertToRoleVOWithUsers(Role role) {
        RoleVO roleVO = convertToRoleVO(role);
        
        // 获取用户组下的用户列表
        List<String> userIds = userRoleRepository.findUserIdsByRoleId(role.getId());
        List<UserVO> users = userIds.stream()
                .map(userService::findUserById)
                .filter(user -> user != null)
                .collect(Collectors.toList());
        
        roleVO.setUsers(users);
        return roleVO;
    }
}

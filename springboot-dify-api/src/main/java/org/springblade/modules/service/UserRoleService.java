package org.springblade.modules.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.entity.Role;
import org.springblade.modules.entity.UserRole;
import org.springblade.modules.repository.RoleRepository;
import org.springblade.modules.repository.UserRoleRepository;
import org.springblade.modules.vo.RoleVO;
import org.springblade.modules.vo.UserRoleVO;
import org.springblade.modules.vo.UserVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户用户组关联服务类
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-27
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserRoleService {

    private final UserRoleRepository userRoleRepository;
    private final RoleRepository roleRepository;
    private final UserService userService;
    private final RoleService roleService;

    /**
     * 为用户分配用户组
     */
    @Transactional
    public UserRoleVO assignRoleToUser(String userId, String roleId) {
        // 检查用户是否存在
        UserVO user = userService.findUserById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }

        // 检查用户组是否存在
        Role role = roleRepository.findById(roleId).orElse(null);
        if (role == null || role.getIsDeleted()) {
            throw new RuntimeException("用户组不存在: " + roleId);
        }

        // 检查是否已经分配
        if (userRoleRepository.existsByUserIdAndRoleId(userId, roleId)) {
            throw new RuntimeException("用户已经属于该用户组");
        }

        UserRole userRole = UserRole.builder()
                .userId(userId)
                .roleId(roleId)
                .build();

        userRole = userRoleRepository.save(userRole);
        log.info("为用户 {} 分配用户组 {} 成功", user.getName(), role.getName());
        
        return convertToUserRoleVO(userRole);
    }

    /**
     * 移除用户的用户组
     */
    @Transactional
    public void removeRoleFromUser(String userId, String roleId) {
        if (!userRoleRepository.existsByUserIdAndRoleId(userId, roleId)) {
            throw new RuntimeException("用户不属于该用户组");
        }

        userRoleRepository.deleteByUserIdAndRoleId(userId, roleId);
        log.info("移除用户 {} 的用户组 {} 成功", userId, roleId);
    }

    /**
     * 批量为用户分配用户组
     */
    @Transactional
    public void assignRolesToUser(String userId, List<String> roleIds) {
        // 先移除用户的所有用户组
        userRoleRepository.deleteByUserId(userId);

        // 再分配新的用户组
        for (String roleId : roleIds) {
            try {
                assignRoleToUser(userId, roleId);
            } catch (Exception e) {
                log.warn("为用户 {} 分配用户组 {} 失败: {}", userId, roleId, e.getMessage());
            }
        }
    }

    /**
     * 批量为用户组分配用户
     */
    @Transactional
    public void assignUsersToRole(String roleId, List<String> userIds) {
        // 先移除用户组的所有用户
        userRoleRepository.deleteByRoleId(roleId);

        // 再分配新的用户
        for (String userId : userIds) {
            try {
                assignRoleToUser(userId, roleId);
            } catch (Exception e) {
                log.warn("为用户组 {} 分配用户 {} 失败: {}", roleId, userId, e.getMessage());
            }
        }
    }

    /**
     * 获取用户的用户组列表
     */
    public List<UserRoleVO> findUserRolesByUserId(String userId) {
        List<UserRole> userRoles = userRoleRepository.findByUserId(userId);
        return userRoles.stream()
                .map(this::convertToUserRoleVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取用户组的用户列表
     */
    public List<UserRoleVO> findUserRolesByRoleId(String roleId) {
        List<UserRole> userRoles = userRoleRepository.findByRoleId(roleId);
        return userRoles.stream()
                .map(this::convertToUserRoleVO)
                .collect(Collectors.toList());
    }

    /**
     * 检查用户是否属于指定用户组
     */
    public boolean isUserInRole(String userId, String roleId) {
        return userRoleRepository.existsByUserIdAndRoleId(userId, roleId);
    }

    /**
     * 检查用户是否属于指定用户组（按名称）
     */
    public boolean isUserInRoleByRoleName(String userId, String roleName) {
        List<String> roleIds = userRoleRepository.findRoleIdsByUserId(userId);
        for (String roleId : roleIds) {
            Role role = roleRepository.findById(roleId).orElse(null);
            if (role != null && !role.getIsDeleted() && roleName.equals(role.getName())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取用户所属的用户组ID列表
     */
    public List<String> getUserRoleIds(String userId) {
        return userRoleRepository.findRoleIdsByUserId(userId);
    }

    /**
     * 初始化用户的默认用户组
     */
    @Transactional
    public void initializeUserDefaultRole(String userId) {
        // 检查用户是否已有用户组
        List<String> roleIds = getUserRoleIds(userId);
        if (!roleIds.isEmpty()) {
            return; // 用户已有用户组，不需要初始化
        }

        // 为用户分配默认的USER用户组
        Role userRole = roleRepository.findByNameAndNotDeleted("USER").orElse(null);
        if (userRole != null) {
            try {
                assignRoleToUser(userId, userRole.getId());
                log.info("为用户 {} 初始化默认用户组 USER", userId);
            } catch (Exception e) {
                log.warn("为用户 {} 初始化默认用户组失败: {}", userId, e.getMessage());
            }
        }
    }

    /**
     * 将UserRole实体转换为UserRoleVO
     */
    private UserRoleVO convertToUserRoleVO(UserRole userRole) {
        UserVO user = userService.findUserById(userRole.getUserId());
        RoleVO role = roleService.findById(userRole.getRoleId());

        return UserRoleVO.builder()
                .id(userRole.getId())
                .userId(userRole.getUserId())
                .roleId(userRole.getRoleId())
                .user(user)
                .role(role)
                .createdAt(userRole.getCreatedAt())
                .build();
    }
}

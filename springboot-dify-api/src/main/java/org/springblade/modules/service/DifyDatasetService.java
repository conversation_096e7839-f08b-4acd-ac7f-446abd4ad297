package org.springblade.modules.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.vo.DifyDatasetParamVO;
import org.springblade.modules.vo.DifyDatasetSearchVO;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * dify知识库服务类
 * <AUTHOR> [sijun.zeng]
 * @date 2025-05-20 22:19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DifyDatasetService {

    private final DifyDatasetParamVO difyDatasetParamVO;
    private final RestTemplate restTemplate;

    /**
     * 检索知识库
     * @param searchVO 检索参数
     * @return 检索结果
     */
    public JSONArray retrieveDatasets(DifyDatasetSearchVO searchVO) {
        List<String> datasetIds = difyDatasetParamVO.getSearchIds();
        if (CollUtil.isEmpty(datasetIds)) {
            log.warn("No dataset IDs configured");
            return null;
        }

        // 创建并发请求任务列表
        List<CompletableFuture<JSONObject>> futures = datasetIds.stream()
                .map(datasetId -> CompletableFuture.supplyAsync(() -> retrieveSingleDataset(datasetId, searchVO)))
                .collect(Collectors.toList());

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );

        // 合并结果
        JSONArray result = new JSONArray();
        try {
            allFutures.get(); // 等待所有请求完成

            // 收集所有成功的结果
            for (CompletableFuture<JSONObject> future : futures) {
                try {
                    JSONObject response = future.get();
                    if (response != null && response.containsKey("records")) {
                        // 将每个知识库的检索结果添加到最终结果中
                        result.addAll(response.getJSONArray("records"));
                    }
                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error getting dataset retrieval result", e);
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("Error waiting for dataset retrieval tasks", e);
        }

        return result;
    }

    /**
     * 检索单个知识库
     * @param datasetId 知识库ID
     * @param searchVO 检索参数
     * @return 检索结果
     */
    private JSONObject retrieveSingleDataset(String datasetId, DifyDatasetSearchVO searchVO) {
        try {
            // 构建请求URL
            String url = String.format("%s/datasets/%s/retrieve", difyDatasetParamVO.getApiServerHost(), datasetId);
            log.info("retrieveSingleDataset url: {}", url);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(difyDatasetParamVO.getApiKey());

            // 构建请求体
            String requestBody = JSONUtil.toJsonStr(searchVO);

            // 发送请求
            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            // 解析响应
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                JSONObject result = JSONUtil.parseObj(response.getBody());

                // 处理records中的document
                if (result.containsKey("records")) {
                    JSONArray records = result.getJSONArray("records");
                    for (int i = 0; i < records.size(); i++) {
                        JSONObject record = records.getJSONObject(i);
                        if (record.containsKey("segment") && record.getJSONObject("segment").containsKey("document")) {
                            JSONObject document = record.getJSONObject("segment").getJSONObject("document");
                            if (document != null && document.containsKey("id")) {
                                String documentId = document.getStr("id");
                                JSONObject fileInfo = getDocumentUploadFile(datasetId, documentId);
                                if (fileInfo != null) {
                                    // 合并文件信息到document对象中
                                    document.putAll(fileInfo);
                                }
                            }
                        }
                    }
                }

                return result;
            } else {
                log.error("Failed to retrieve dataset {}: {}", datasetId, response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error retrieving dataset " + datasetId, e);
        }

        return null;
    }

    /**
     * 获取文档上传文件信息
     * @param datasetId 知识库ID
     * @param documentId 文档ID
     * @return 文件信息
     */
    public JSONObject getDocumentUploadFile(String datasetId, String documentId) {
        try {
            // 构建请求URL
            String url = String.format("%s/datasets/%s/documents/%s/upload-file",
                    difyDatasetParamVO.getApiServerHost(), datasetId, documentId);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(difyDatasetParamVO.getApiKey());

            // 发送请求
            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            // 解析响应
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return JSONUtil.parseObj(response.getBody());
            } else {
                log.error("Failed to get document upload file for dataset {}, document {}: {}",
                        datasetId, documentId, response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error getting document upload file for dataset " + datasetId + ", document " + documentId, e);
        }

        return null;
    }


}

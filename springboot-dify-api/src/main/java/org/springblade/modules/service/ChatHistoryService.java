package org.springblade.modules.service;

import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.entity.AgentDict;
import org.springblade.modules.entity.ChatHistory;
import org.springblade.modules.repository.ChatHistoryRepository;
import org.springblade.modules.vo.ChatHistoryVO;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-04-01 20:46
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ChatHistoryService {

    private final HttpServletRequest request;

    private final ChatHistoryRepository chatHistoryRepository;

    private final AgentDictService agentDictService;

    public List<ChatHistory> findAllDefByCreateBy() {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();

        return chatHistoryRepository.findChatHistoriesByCreateAt(principal.getAttributes().getId());
    }

    public List<ChatHistory> findAllDefByCreateByAndAgentDictId(String agentDictId) {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();

        return chatHistoryRepository.findChatHistoriesByCreateAtAndAgentDictId(principal.getAttributes().getId(), agentDictId);
    }

    /**
     * 查询用户创建的会话记录
     * @return 用户可见的会话记录
     */
    public List<ChatHistoryVO> findAllByCreateBy() {
        List<ChatHistory> chatHistoriesByCreateAt = findAllDefByCreateBy();

        // 判断是否为管理员
        boolean isAdmin = isCurrentUserAdmin();

        List<ChatHistoryVO> list = new ArrayList<>();

        if (isAdmin) {
            // 管理员可以看到所有会话记录
            for (ChatHistory chatHistory : chatHistoriesByCreateAt) {
                ChatHistoryVO chatHistoryVO = new ChatHistoryVO(chatHistory);
                AgentDict agentDict = agentDictService.findById(chatHistory.getAgentDictId());
                if (agentDict != null && !agentDict.getIsDeleted()) {
                    chatHistoryVO.setAgentDict(agentDict);
                    list.add(chatHistoryVO);
                }
            }
        } else {
            // 普通用户只能看到有权限的AgentDict相关的会话记录
            // 获取用户可见的所有AgentDict
            List<AgentDict> visibleAgentDicts = agentDictService.findAllVisibleToCurrentUser();
            // 创建一个可见AgentDict的ID集合，用于快速查找
            List<String> visibleAgentDictIds = new ArrayList<>();
            for (AgentDict agentDict : visibleAgentDicts) {
                visibleAgentDictIds.add(agentDict.getId());
            }

            // 加上默认 Agent 也是可见的
            visibleAgentDictIds.add(agentDictService.getAgentDictByIsDefaultTrue().getId());

            // 填充对应 Agent 信息，只包含用户有权限查看的Agent
            for (ChatHistory chatHistory : chatHistoriesByCreateAt) {
                // 只展示用户有权限查看的AgentDict相关的会话记录
                if (visibleAgentDictIds.contains(chatHistory.getAgentDictId())) {
                    ChatHistoryVO chatHistoryVO = new ChatHistoryVO(chatHistory);
                    AgentDict agentDict = agentDictService.findById(chatHistory.getAgentDictId());
                    chatHistoryVO.setAgentDict(agentDict);
                    list.add(chatHistoryVO);
                }
            }
        }

        return list;
    }

    public List<ChatHistoryVO> findAllByCreateByAndAgentDictId(String agentDictId) {
        // 判断是否为管理员
        boolean isAdmin = isCurrentUserAdmin();

        // 如果不是管理员，需要检查权限
        if (!isAdmin) {
            // 首先检查用户是否有权限查看该AgentDict
            List<AgentDict> visibleAgentDicts = agentDictService.findAllVisibleToCurrentUser();
            boolean hasPermission = false;
            for (AgentDict agentDict : visibleAgentDicts) {
                if (agentDict.getId().equals(agentDictId)) {
                    hasPermission = true;
                    break;
                }
            }

            // 加上默认 Agent 也是可见的
            AgentDict defaultAgent = agentDictService.getAgentDictByIsDefaultTrue();
            if (defaultAgent != null && defaultAgent.getId().equals(agentDictId)) {
                hasPermission = true;
            }

            // 如果没有权限，返回空列表
            if (!hasPermission) {
                return new ArrayList<>();
            }
        }

        List<ChatHistory> chatHistoriesByCreateAt = findAllDefByCreateByAndAgentDictId(agentDictId);

        List<ChatHistoryVO> list = new ArrayList<>();
        // 填充对应 Agent 信息
        for (ChatHistory chatHistory : chatHistoriesByCreateAt) {
            ChatHistoryVO chatHistoryVO = new ChatHistoryVO(chatHistory);
            AgentDict agentDict = agentDictService.findById(chatHistory.getAgentDictId());
            chatHistoryVO.setAgentDict(agentDict);
            list.add(chatHistoryVO);
        }

        return list;
    }

    /**
     * 新建会话记录
     * @param agentDictId
     * @param conversationId
     * @param name
     * @return
     */
    public ChatHistory createNew(String agentDictId, String conversationId, String name) {
        ChatHistory chatHistory = new ChatHistory();
        chatHistory.setAgentDictId(agentDictId);
        chatHistory.setConversationId(conversationId);
        chatHistory.setName(name);
        chatHistory.setIsTop(false);
        chatHistory.setIsDeleted(false);
        chatHistory.setCreateAt(new java.sql.Timestamp(System.currentTimeMillis()));

        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        chatHistory.setCreateBy(principal.getAttributes().getId());

        return chatHistoryRepository.save(chatHistory);
    }

    /**
     * 删除会话记录
     * @param id
     */
    public void delete(String id) {
        ChatHistory chatHistory = chatHistoryRepository.findById(id).orElse(null);
        if (chatHistory != null) {
            chatHistory.setIsDeleted(true);
            chatHistoryRepository.save(chatHistory);
        }
    }

    /**
     * 修改会话记录
     * @param id
     * @param name
     */
    public void rename(String id, String name) {
        ChatHistory chatHistory = chatHistoryRepository.findById(id).orElse(null);
        if (chatHistory != null) {
            chatHistory.setName(name);
            chatHistoryRepository.save(chatHistory);
        }
    }

    /**
     * 置顶会话记录
     * @param id
     */
    public void top(String id, Boolean isTop, String newName) {
        ChatHistory chatHistory = chatHistoryRepository.findById(id).orElse(null);
        if (chatHistory != null) {
            chatHistory.setIsTop(isTop);
            if (StrUtil.isNotBlank(newName)) {
                chatHistory.setName(newName);
            }
            chatHistoryRepository.save(chatHistory);
        }
    }

    @Transactional
    public void deleteByAgentDictId(String agentDictId) {
        try {
            chatHistoryRepository.updateIsDeletedByAgentDictId(agentDictId, true);
        } catch (Exception e) {
            log.error("删除会话记录失败", e);
        }
    }

    /**
     * 判断当前用户是否为管理员
     * @return 是否为管理员
     */
    private boolean isCurrentUserAdmin() {
        try {
            UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
            if (userPrincipal != null) {
                CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
                // 判断用户名是否为"gzyc"
                return "1044010100000218".equals(principal.getAttributes().getWorkNo());
            }
        } catch (Exception e) {
            log.error("判断用户是否为管理员失败", e);
        }
        return false;
    }

}

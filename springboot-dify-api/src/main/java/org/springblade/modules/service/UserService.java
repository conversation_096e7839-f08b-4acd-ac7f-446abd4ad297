package org.springblade.modules.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.entity.Account;
import org.springblade.modules.repository.AccountRepository;
import org.springblade.modules.vo.UserVO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户服务类，提供用户相关的业务逻辑
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserService {

    private final AccountRepository accountRepository;

    /**
     * 获取所有用户列表
     * @return 用户列表
     */
    public List<UserVO> findAllUsers() {
        List<Account> accounts = accountRepository.findAll();
        return accounts.stream()
                .map(this::convertToUserVO)
                .collect(Collectors.toList());
    }

    /**
     * 根据用户ID获取用户信息
     * @param id 用户ID
     * @return 用户信息
     */
    public UserVO findUserById(String id) {
        Account account = accountRepository.findById(id).orElse(null);
        return account != null ? convertToUserVO(account) : null;
    }

    /**
     * 根据用户名模糊搜索用户
     * @param keyword 搜索关键词
     * @return 匹配的用户列表
     */
    public List<UserVO> searchUsersByName(String keyword) {
        List<Account> accounts = accountRepository.findAll();
        return accounts.stream()
                .filter(account -> account.getName() != null && account.getName().contains(keyword))
                .map(this::convertToUserVO)
                .collect(Collectors.toList());
    }

    /**
     * 将Account实体转换为UserVO
     * @param account 账户实体
     * @return 用户VO
     */
    private UserVO convertToUserVO(Account account) {
        return UserVO.builder()
                .id(account.getId())
                .name(account.getName())
                .email(account.getEmail())
                .avatar(account.getAvatar())
                .status(account.getStatus())
                .build();
    }
}

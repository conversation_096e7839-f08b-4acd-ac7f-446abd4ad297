package org.springblade.modules.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.entity.Account;
import org.springblade.modules.entity.OptimizationSuggestion;
import org.springblade.modules.repository.OptimizationSuggestionRepository;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 优化建议箱服务层
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OptimizationSuggestionService {

    private final OptimizationSuggestionRepository optimizationSuggestionRepository;

    /**
     * 保存优化建议
     * @param suggestion 优化建议
     * @return 保存后的优化建议
     */
    public OptimizationSuggestion save(OptimizationSuggestion suggestion) {
        if (suggestion.getCreateTime() == null) {
            suggestion.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
        }
        return optimizationSuggestionRepository.save(suggestion);
    }

    /**
     * 根据ID查询优化建议
     * @param id 优化建议ID
     * @return 优化建议
     */
    public OptimizationSuggestion findById(String id) {
        return optimizationSuggestionRepository.findById(id).orElse(null);
    }

    /**
     * 根据用户ID查询优化建议列表
     * @param userId 用户ID
     * @return 优化建议列表
     */
    public List<OptimizationSuggestion> findByUserId(String userId) {
        return optimizationSuggestionRepository.findByUserIdOrderByCreateTimeDesc(userId);
    }

    /**
     * 查询所有优化建议列表
     * @return 优化建议列表
     */
    public List<OptimizationSuggestion> findAll() {
        return optimizationSuggestionRepository.findAllByOrderByCreateTimeDesc();
    }

    /**
     * 删除优化建议
     * @param id 优化建议ID
     */
    public void delete(String id) {
        optimizationSuggestionRepository.deleteById(id);
    }

}

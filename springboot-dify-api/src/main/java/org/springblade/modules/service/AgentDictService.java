package org.springblade.modules.service;

import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.entity.AgentDict;
import org.springblade.modules.repository.AgentDictRepository;
import org.springblade.modules.vo.RoleVO;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-03-17 23:54
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AgentDictService {

    private final AgentDictRepository agentDictRepository;
    private final HttpServletRequest request;
    private final UserRoleService userRoleService;
    private final RoleService roleService;

    /**
     * 是否启用基于用户组的权限控制
     * 可以通过配置文件控制，默认为true（启用）
     */
    @Value("${permission.role-based.enabled:true}")
    private boolean roleBasedPermissionEnabled;

    public AgentDict findById(String id) {
        return agentDictRepository.findById(id).orElse(null);
    }

    public AgentDict findByApiKey(String apiKey) {
        return agentDictRepository.findByApiKey(apiKey);
    }

    public AgentDict findByApiKeyAndApiServerHost(String apiKey, String apiServerHost) {
        return agentDictRepository.findFirstByApiKeyAndApiServerHost(apiKey, apiServerHost);
    }

    public List<AgentDict> findAll() {
        return agentDictRepository.findAll();
    }

    public AgentDict getAgentDictByIsDefaultTrue() {
        return agentDictRepository.getAgentDictByIsDefaultTrue();
    }

    public List<AgentDict> findByIsDefaultFalse() {
        return agentDictRepository.findByIsDefaultFalseAndIsDeletedFalseOrderBySortOrderAsc();
    }

    public AgentDict save(AgentDict agentDict) {
        if (agentDict.getId() == null) {
            Integer maxOrder = agentDictRepository.findOrderMax();
            agentDict.setIsDefault(false);
            agentDict.setSortOrder(maxOrder == null ? 1 : maxOrder + 1);
        }
        agentDict.setIsDeleted(false);
        AgentDict save = agentDictRepository.save(agentDict);
        return save;
    }

    public void delete(String id) {
        AgentDict agentDict = agentDictRepository.findById(id).orElse(null);
        if (agentDict != null) {
            agentDict.setIsDeleted(true);
            agentDictRepository.save(agentDict);
        }
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
            if (userPrincipal != null) {
                CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
                return principal.getAttributes().getId();
            }
        } catch (Exception e) {
            log.error("获取当前用户ID失败", e);
        }
        return null;
    }

    /**
     * 判断当前用户是否为管理员
     * @return 是否为管理员
     */
    private boolean isCurrentUserAdmin() {
        try {
            UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
            if (userPrincipal != null) {
                CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
                // 硬编码管理员判断（向后兼容）
                if ("1044010100000218".equals(principal.getAttributes().getName())) {
                    return true;
                }

                // 基于用户组的管理员判断
                String userId = principal.getAttributes().getId();
                if (userId != null && roleBasedPermissionEnabled) {
                    return userRoleService.isUserInRoleByRoleName(userId, "gzyc");
                }
            }
        } catch (Exception e) {
            log.error("判断用户是否为管理员失败", e);
        }
        return false;
    }

    /**
     * 查询当前用户可见的所有AgentDict
     * 如果是管理员，则返回所有非默认且未删除的AgentDict
     * 支持基于用户组的权限控制
     */
    public List<AgentDict> findAllVisibleToCurrentUser() {
        // 判断是否为管理员
        if (isCurrentUserAdmin()) {
            // 管理员可以看到所有非默认且未删除的AgentDict
            return agentDictRepository.findByIsDefaultFalseAndIsDeletedFalseOrderBySortOrderAsc();
        }

        String userId = getCurrentUserId();
        if (userId == null) {
            // 如果无法获取用户ID，只返回公开的AgentDict
            return agentDictRepository.findPublicAgentDicts();
        }

        // 根据配置选择权限控制方式
        if (roleBasedPermissionEnabled) {
            // 使用基于用户组的权限控制
            return agentDictRepository.findAllAgentDictsVisibleToUserByRoles(userId);
        } else {
            // 使用传统的基于用户的权限控制
            String userIdQuoted = '"' + userId + '"';
            return agentDictRepository.findAllAgentDictsVisibleToUser(userId, userIdQuoted);
        }
    }

    /**
     * 设置AgentDict的可见性
     * @param id AgentDict ID
     * @param isPublic 是否公开可见
     * @param visibleUserIds 可见用户ID列表
     * @return 更新后的AgentDict
     */
    public AgentDict updateVisibility(String id, Boolean isPublic, List<String> visibleUserIds) {
        AgentDict agentDict = agentDictRepository.findById(id).orElse(null);
        if (agentDict != null) {
            agentDict.setIsPublic(isPublic);
            if (visibleUserIds != null) {
                agentDict.setVisibleToUsers(JSONUtil.toJsonStr(visibleUserIds));
            } else if (!isPublic) {
                // 如果设置为非公开但没有提供可见用户列表，初始化为空数组
                agentDict.setVisibleToUsers(JSONUtil.toJsonStr(new ArrayList<String>()));
            }
            return agentDictRepository.save(agentDict);
        }
        return null;
    }

    /**
     * 设置AgentDict的用户组可见性
     * @param id AgentDict ID
     * @param isPublic 是否公开可见
     * @param visibleRoleIds 可见用户组ID列表
     * @return 更新后的AgentDict
     */
    public AgentDict updateRoleVisibility(String id, Boolean isPublic, List<String> visibleRoleIds) {
        AgentDict agentDict = agentDictRepository.findById(id).orElse(null);
        if (agentDict != null) {
            agentDict.setIsPublic(isPublic);
            if (visibleRoleIds != null) {
                agentDict.setVisibleToRoles(JSONUtil.toJsonStr(visibleRoleIds));
            } else if (!isPublic) {
                // 如果设置为非公开但没有提供可见用户组列表，初始化为空数组
                agentDict.setVisibleToRoles(JSONUtil.toJsonStr(new ArrayList<String>()));
            }
            return agentDictRepository.save(agentDict);
        }
        return null;
    }

    /**
     * 获取AgentDict的用户组可见性设置
     * @param id AgentDict ID
     * @return 包含isPublic和visibleToRoles的信息
     */
    public AgentDict getRoleVisibility(String id) {
        return agentDictRepository.findById(id).orElse(null);
    }

    /**
     * 数据迁移：将基于用户的权限转换为基于用户组的权限
     * 将所有用户分配到USER用户组，然后将Agent的用户权限转换为USER用户组权限
     */
    public void migrateUserPermissionsToRolePermissions() {
        log.info("开始迁移用户权限到用户组权限");

        try {
            // 获取所有非公开的AgentDict
            List<AgentDict> privateAgents = agentDictRepository.findAll().stream()
                    .filter(agent -> !agent.getIsDeleted() &&
                            agent.getIsPublic() != null && !agent.getIsPublic() &&
                            agent.getVisibleToUsers() != null && !agent.getVisibleToUsers().isEmpty())
                    .toList();

            for (AgentDict agent : privateAgents) {
                try {
                    // 解析可见用户列表
                    List<String> userIds = JSONUtil.toList(agent.getVisibleToUsers(), String.class);

                    // 为这些用户初始化默认用户组（如果还没有）
                    for (String userId : userIds) {
                        userRoleService.initializeUserDefaultRole(userId);
                    }

                    // 将Agent的权限设置为USER用户组可见
                    // 这里假设所有有权限的用户都属于USER用户组
                    List<String> roleIds = new ArrayList<>();
                    roleIds.add(getUserRoleId()); // 获取USER用户组ID

                    agent.setVisibleToRoles(JSONUtil.toJsonStr(roleIds));
                    agentDictRepository.save(agent);

                    log.info("迁移Agent {} 的权限成功", agent.getName());
                } catch (Exception e) {
                    log.error("迁移Agent {} 的权限失败: {}", agent.getName(), e.getMessage());
                }
            }

            log.info("用户权限迁移完成");
        } catch (Exception e) {
            log.error("用户权限迁移失败", e);
        }
    }

    /**
     * 获取USER用户组的ID
     */
    private String getUserRoleId() {
        try {
            RoleVO userRole = roleService.findByName("USER");
            return userRole != null ? userRole.getId() : null;
        } catch (Exception e) {
            log.error("获取USER用户组ID失败", e);
            return null;
        }
    }
}

package org.springblade.modules.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.config.MinioConfig;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Minio 服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MinioService {

    private final MinioClient minioClient;
    private final MinioConfig minioConfig;

    /**
     * 检查存储桶是否存在，不存在则创建
     *
     * @param bucketName 存储桶名称
     */
    public void checkBucket(String bucketName) throws Exception {
        boolean exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (!exists) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        }
    }

    /**
     * 上传文件
     *
     * @param file 文件
     * @return 文件信息
     */
    public JSONObject uploadFile(MultipartFile file) {
        JSONObject result = JSONUtil.createObj();
        String bucketName = minioConfig.getBucketName();
        
        try {
            // 检查存储桶是否存在
            checkBucket(bucketName);
            
            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String objectName = "images/" + IdUtil.simpleUUID() + fileExtension;
            
            // 设置文件元数据
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", file.getContentType());
            
            // 上传文件
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .contentType(file.getContentType())
                            .stream(file.getInputStream(), file.getSize(), -1)
                            .build()
            );
            
            // 生成访问URL
//            String url = minioClient.getPresignedObjectUrl(
//                    GetPresignedObjectUrlArgs.builder()
//                            .bucket(bucketName)
//                            .object(objectName)
//                            .method(Method.GET)
//                            .expiry(7, TimeUnit.DAYS)
//                            .build()
//            );

            String url = minioConfig.getTransformEndpoint() + "/" + bucketName + "/" + objectName;

            // 返回结果
            result.set("id", objectName);
            result.set("url", url);
            result.set("name", originalFilename);
            result.set("size", file.getSize());
            result.set("type", file.getContentType());
            
        } catch (Exception e) {
            log.error("Minio上传文件失败", e);
            result.set("code", 500);
            result.set("message", "文件上传失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取文件
     *
     * @param objectName 对象名称
     * @return 文件流
     */
    public InputStream getObject(String objectName) throws Exception {
        return minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .build()
        );
    }

    /**
     * 删除文件
     *
     * @param objectName 对象名称
     */
    public void removeObject(String objectName) throws Exception {
        minioClient.removeObject(
                RemoveObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .build()
        );
    }

    /**
     * 获取文件的临时访问URL
     *
     * @param objectName 对象名称
     * @param expiry     过期时间（以秒为单位）
     * @return 临时访问URL
     */
    public String getPresignedObjectUrl(String objectName, int expiry) throws Exception {
        return minioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .method(Method.GET)
                        .expiry(expiry, TimeUnit.SECONDS)
                        .build()
        );
    }
}

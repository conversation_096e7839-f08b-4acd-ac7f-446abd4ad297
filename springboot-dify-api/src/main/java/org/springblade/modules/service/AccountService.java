package org.springblade.modules.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.entity.Account;
import org.springblade.modules.repository.AccountRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-03-15 22:19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AccountService {

    private final AccountRepository accountRepository;

    public Account save(Account account) {
        return accountRepository.save(account);
    }

    public Account findById(String id) {
        return accountRepository.findById(id).orElse(null);
    }

    public Account findByName(String name) {
        return accountRepository.findByName(name);
    }

    public Account findByEmail(String email) {
        return accountRepository.findByEmail(email);
    }

    public List<Account> findAll() {
        return accountRepository.findAll();
    }

}

package org.springblade.modules.service;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.entity.AgentDict;
import org.springblade.modules.entity.UserAgentList;
import org.springblade.modules.repository.UserAgentListRepository;
import org.springblade.modules.vo.UserAgentListVO;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-04-01 20:47
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserAgentListService {

    private final HttpServletRequest request;

    private final UserAgentListRepository userAgentListRepository;

    private final AgentDictService agentDictService;

    public List<UserAgentListVO> findAllByCreateByOrderByOrder() {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        String userId = principal.getAttributes().getId();
        List<UserAgentList> userAgentLists = userAgentListRepository.findAllByCreateByOrderByCreateAt(userId);

        List<UserAgentListVO> list = new ArrayList<>();
        if (IterUtil.isNotEmpty(userAgentLists)) {
            // 判断是否为管理员
            boolean isAdmin = isCurrentUserAdmin();

            if (isAdmin) {
                // 管理员可以看到所有AgentDict
                for (UserAgentList userAgentList : userAgentLists) {
                    UserAgentListVO userAgentListVO = new UserAgentListVO(userAgentList);
                    AgentDict agentDict = agentDictService.findById(userAgentList.getAgentDictId());
                    if (agentDict != null && !agentDict.getIsDeleted()) {
                        userAgentListVO.setAgentDict(agentDict);
                        list.add(userAgentListVO);
                    }
                }
            } else {
                // 普通用户只能看到有权限的AgentDict
                // 获取用户可见的所有AgentDict
                List<AgentDict> visibleAgentDicts = agentDictService.findAllVisibleToCurrentUser();
                // 创建一个可见AgentDict的ID集合，用于快速查找
                List<String> visibleAgentDictIds = new ArrayList<>();
                for (AgentDict agentDict : visibleAgentDicts) {
                    visibleAgentDictIds.add(agentDict.getId());
                }

                for (UserAgentList userAgentList : userAgentLists) {
                    // 只展示用户有权限查看的AgentDict
                    if (visibleAgentDictIds.contains(userAgentList.getAgentDictId())) {
                        UserAgentListVO userAgentListVO = new UserAgentListVO(userAgentList);
                        AgentDict agentDict = agentDictService.findById(userAgentList.getAgentDictId());
                        userAgentListVO.setAgentDict(agentDict);
                        list.add(userAgentListVO);
                    }
                }
            }
        }

        return list;
    }

    public JSONObject save(UserAgentList userAgentList) {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        String createBy = principal.getAttributes().getId();

        // 保存前先判断一下该用户是否已经存在该agent，如果存在则不保存
        UserAgentList userAgentListByAgentDictIdAndCreateBy = userAgentListRepository.findByAgentDictIdAndCreateBy(userAgentList.getAgentDictId(), createBy);
        if (userAgentListByAgentDictIdAndCreateBy != null) {
            JSONObject obj = JSONUtil.createObj();
            obj.set("code", 401);
            obj.set("msg", "已经存在该agent，请不要重复添加。");
            return obj;
        }

        // 每一次保存都要重新刷新用户列表的所有order，新的永远是最前一个
        userAgentList.setCreateAt(new Timestamp(System.currentTimeMillis()));
        userAgentList.setCreateBy(createBy);
        userAgentList.setIsTop(false);
        userAgentListRepository.save(userAgentList);
        JSONObject obj = JSONUtil.createObj();
        obj.set("code", 200);
        obj.set("msg", "保存成功");
        return obj;
    }

    public void delete(String id) {
        userAgentListRepository.deleteById(id);
    }

    public void updateIsTopById(Boolean isTop, String id) {
        UserAgentList userAgentList = userAgentListRepository.findById(id).orElse(null);
        if (userAgentList != null) {
            userAgentList.setIsTop(isTop);
            userAgentList.setCreateAt(new Timestamp(System.currentTimeMillis()));
            userAgentListRepository.save(userAgentList);
        }
    }

    @Transactional
    public void removeByAgentDictId(String agentDictId) {
        try {
            userAgentListRepository.removeByAgentDictId(agentDictId);
        } catch (Exception e) {
            log.error("删除失败", e);
        }
    }

    /**
     * 判断当前用户是否为管理员
     * @return 是否为管理员
     */
    private boolean isCurrentUserAdmin() {
        try {
            UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
            if (userPrincipal != null) {
                CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
                // 判断用户名是否为"gzyc"
                return "1044010100000218".equals(principal.getAttributes().getName());
            }
        } catch (Exception e) {
            log.error("判断用户是否为管理员失败", e);
        }
        return false;
    }

//    public void updateOrderById(Integer order, String id) {
//        // 将String转为UUID对象
//        UUID targetId = UUID.fromString(id);
//
//        userAgentListRepository.updateSortOrderById(order, targetId);
//
//        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
//        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
//
//        // 获取列表中的所有数据，并进行order的重新编排，确保order是连续的，如果数组前后的order相同，就判断id是否和入参的id一致，如果是就排前面
//        List<UserAgentList> allByCreateByOrderByOrder = userAgentListRepository.findAllByCreateByOrderBySortOrder(principal.getAttributes().getId());
//        allByCreateByOrderByOrder.sort((a, b) -> {
//            int orderCompare = Integer.compare(a.getSortOrder(), b.getSortOrder());
//            if (orderCompare != 0) {
//                return orderCompare;
//            }
//            // 当order相同时，当前用户排前面
//            if (a.getId().equals(targetId) && !b.getId().equals(targetId)) {
//                return -1;
//            }
//            if (!a.getId().equals(targetId) && b.getId().equals(targetId)) {
//                return 1;
//            }
//            return 0;
//        });
//
//        // 重新分配连续的order值
//        int startOrder = 1;
//        for (UserAgentList item : allByCreateByOrderByOrder) {
//            item.setSortOrder(order++);
//        }
//
//        userAgentListRepository.saveAll(allByCreateByOrderByOrder);
//    }

}

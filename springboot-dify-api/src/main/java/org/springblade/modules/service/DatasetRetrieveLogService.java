package org.springblade.modules.service;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.entity.DatasetRetrieveLog;
import org.springblade.modules.repository.DatasetRetrieveLogRepository;
import org.springblade.modules.vo.DifyDatasetSearchVO;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识库检索日志服务类
 * <AUTHOR> [sijun.zeng]
 * @date 2025-05-21 10:40
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DatasetRetrieveLogService {

    private final DatasetRetrieveLogRepository datasetRetrieveLogRepository;
    private final HttpServletRequest request;

    /**
     * 保存检索日志
     * @param searchVO 检索参数
     * @param datasetIds 检索的知识库ID列表
     * @param resultCount 检索结果数量
     * @param userDetails 用户信息
     * @return 保存后的检索日志
     */
    public DatasetRetrieveLog saveLog(DifyDatasetSearchVO searchVO, List<String> datasetIds, 
                                      int resultCount, CustomUserDetails userDetails) {
        try {
            DatasetRetrieveLog log = new DatasetRetrieveLog();
            log.setUserId(userDetails.getAttributes().getId());
            log.setUserName(userDetails.getAttributes().getName());
            log.setQuery(searchVO.getQuery());
            
            // 设置检索方法
            if (searchVO.getRetrievalModel() != null && searchVO.getRetrievalModel().getSearchMethod() != null) {
                log.setSearchMethod(searchVO.getRetrievalModel().getSearchMethod());
            }
            
            // 设置知识库ID列表
            if (datasetIds != null && !datasetIds.isEmpty()) {
                log.setDatasetIds(String.join(",", datasetIds));
            }
            
            log.setResultCount(resultCount);
            log.setRetrieveTime(Timestamp.valueOf(LocalDateTime.now()));
            
            // 获取客户端IP地址
            String ipAddress = getClientIpAddress();
            log.setIpAddress(ipAddress);
            
            return datasetRetrieveLogRepository.save(log);
        } catch (Exception e) {
            log.error("保存检索日志失败", e);
            return null;
        }
    }

    /**
     * 获取用户的检索日志
     * @param userId 用户ID
     * @return 检索日志列表
     */
    public List<DatasetRetrieveLog> getUserLogs(String userId) {
        return datasetRetrieveLogRepository.findByUserIdOrderByRetrieveTimeDesc(userId);
    }

    /**
     * 获取当前用户的检索日志
     * @return 检索日志列表
     */
    public List<DatasetRetrieveLog> getCurrentUserLogs() {
        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        if (userPrincipal != null) {
            CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
            return getUserLogs(principal.getAttributes().getId());
        }
        return List.of();
    }

    /**
     * 获取所有检索日志
     * @return 检索日志列表
     */
    public List<DatasetRetrieveLog> getAllLogs() {
        return datasetRetrieveLogRepository.findAllByOrderByRetrieveTimeDesc();
    }

    /**
     * 根据关键词搜索检索日志
     * @param query 关键词
     * @return 检索日志列表
     */
    public List<DatasetRetrieveLog> searchLogs(String query) {
        return datasetRetrieveLogRepository.findByQueryContainingOrderByRetrieveTimeDesc(query);
    }

    /**
     * 获取客户端IP地址
     * @return IP地址
     */
    private String getClientIpAddress() {
        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }
        // 对于通过多个代理的情况，第一个IP为客户端真实IP，多个IP按照','分割
        if (ipAddress != null && ipAddress.contains(",")) {
            ipAddress = ipAddress.split(",")[0].trim();
        }
        return ipAddress;
    }
}

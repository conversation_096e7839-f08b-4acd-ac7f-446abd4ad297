package org.springblade.modules.service;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Redis服务类
 * 用于处理Redis相关操作
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RedisService {

    private final RedisTemplate<String, Object> redisTemplate;

    // WPS文档前缀
    private static final String WPS_DOCUMENT_PREFIX = "wps:document:";

    /**
     * 保存WPS文档信息到Redis
     * @param documentName 文档名称
     * @param documentContent 文档内容
     * @return 生成的文档ID
     */
    public String saveWpsDocument(String documentName, String documentContent) {
        // 生成唯一ID
        String documentId = UUID.randomUUID().toString();

        // 构建Redis键
        String redisKey = WPS_DOCUMENT_PREFIX + documentId;

        // 如果文档内容过大，分片存储
        if (documentContent != null && documentContent.length() > 10000) {
            // 分片存储大文档
            int chunkSize = 10000; // 每片大小
            int totalChunks = (int) Math.ceil((double) documentContent.length() / chunkSize);

            // 保存文档名称和分片信息
            redisTemplate.opsForHash().put(redisKey, "name", documentName);
            redisTemplate.opsForHash().put(redisKey, "chunked", "true");
            redisTemplate.opsForHash().put(redisKey, "totalChunks", String.valueOf(totalChunks));

            // 分片存储文档内容
            for (int i = 0; i < totalChunks; i++) {
                int start = i * chunkSize;
                int end = Math.min(start + chunkSize, documentContent.length());
                String chunk = documentContent.substring(start, end);
                redisTemplate.opsForHash().put(redisKey, "chunk:" + i, chunk);
            }

            log.info("分片保存WPS文档到Redis，ID: {}, 名称: {}, 总分片数: {}", documentId, documentName, totalChunks);
        } else {
            // 正常存储小文档
            redisTemplate.opsForHash().put(redisKey, "name", documentName);
            redisTemplate.opsForHash().put(redisKey, "chunked", "false");
            redisTemplate.opsForHash().put(redisKey, "content", documentContent != null ? documentContent : "");

            log.info("保存WPS文档到Redis，ID: {}, 名称: {}", documentId, documentName);
        }

        // 设置过期时间为8小时
        redisTemplate.expire(redisKey, 8, TimeUnit.HOURS);

        return documentId;
    }

    /**
     * 从Redis获取WPS文档信息
     * @param documentId 文档ID
     * @return 包含文档名称和内容的Map，如果不存在则返回null
     */
    public Map<Object, Object> getWpsDocument(String documentId) {
        // 构建Redis键
        String redisKey = WPS_DOCUMENT_PREFIX + documentId;

        // 检查键是否存在
        if (Boolean.FALSE.equals(redisTemplate.hasKey(redisKey))) {
            log.warn("WPS文档不存在，ID: {}", documentId);
            return null;
        }

        // 获取文档信息
        Map<Object, Object> documentInfo = redisTemplate.opsForHash().entries(redisKey);

        // 检查是否是分片存储的文档
        String chunked = (String) documentInfo.get("chunked");
        if ("true".equals(chunked)) {
            // 重新组装分片文档
            String totalChunksStr = (String) documentInfo.get("totalChunks");
            if (StrUtil.isNotBlank(totalChunksStr)) {
                int totalChunks = Integer.parseInt(totalChunksStr);
                StringBuilder contentBuilder = new StringBuilder();

                // 按顺序读取并组装所有分片
                for (int i = 0; i < totalChunks; i++) {
                    String chunk = (String) documentInfo.get("chunk:" + i);
                    if (chunk != null) {
                        contentBuilder.append(chunk);
                    }
                }

                // 将组装好的内容放入结果中
                Map<Object, Object> result = new HashMap<>();
                result.put("name", documentInfo.get("name"));
                result.put("content", contentBuilder.toString());

                log.info("从Redis获取分片WPS文档，ID: {}, 总分片数: {}", documentId, totalChunks);
                return result;
            }
        }

        log.info("从Redis获取WPS文档，ID: {}", documentId);
        return documentInfo;
    }
}

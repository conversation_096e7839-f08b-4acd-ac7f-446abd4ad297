package org.springblade.modules.util;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springblade.modules.entity.OptimizationSuggestion;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * Excel导出工具类
 */
@Slf4j
public class ExcelExportUtil {

    private ExcelExportUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 导出优化建议列表到Excel
     * @param response HTTP响应
     * @param suggestions 优化建议列表
     * @param fileName 文件名
     */
    public static void exportOptimizationSuggestions(HttpServletResponse response, List<OptimizationSuggestion> suggestions, String fileName) {
        try (Workbook workbook = new XSSFWorkbook()) {
            // 创建工作表
            Sheet sheet = workbook.createSheet("优化建议列表");
            
            // 设置列宽
            sheet.setColumnWidth(0, 20 * 256);
            sheet.setColumnWidth(1, 40 * 256);
            sheet.setColumnWidth(2, 60 * 256);
            sheet.setColumnWidth(3, 20 * 256);
            sheet.setColumnWidth(4, 20 * 256);
            sheet.setColumnWidth(5, 20 * 256);
            sheet.setColumnWidth(6, 40 * 256);
            
            // 创建标题行样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"ID", "标题", "详细描述", "提交用户", "提交时间"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 创建数据行
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (int i = 0; i < suggestions.size(); i++) {
                OptimizationSuggestion suggestion = suggestions.get(i);
                Row row = sheet.createRow(i + 1);
                
                row.createCell(0).setCellValue(suggestion.getId());
                row.createCell(1).setCellValue(suggestion.getTitle());
                row.createCell(2).setCellValue(suggestion.getDescription());
                row.createCell(3).setCellValue(suggestion.getUserName());
                row.createCell(4).setCellValue(suggestion.getCreateTime() != null ? sdf.format(suggestion.getCreateTime()) : "");
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName + ".xlsx");
            
            // 写入响应输出流
            try (OutputStream outputStream = response.getOutputStream()) {
                workbook.write(outputStream);
                outputStream.flush();
            }
        } catch (IOException e) {
            log.error("导出Excel失败", e);
        }
    }
}

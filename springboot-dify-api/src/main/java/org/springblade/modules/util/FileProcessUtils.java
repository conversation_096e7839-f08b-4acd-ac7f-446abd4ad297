package org.springblade.modules.util;

import lombok.extern.slf4j.Slf4j;
import java.util.Arrays;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;

/**
 * 文件处理工具类
 * 支持Excel、Word、PDF、TXT等格式文件的内容提取
 */
@Slf4j
public class FileProcessUtils {

    private FileProcessUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 处理Excel文件
     *
     * @param file Excel文件
     * @return 处理后的文本内容
     */
    public static List<String> processExcel(MultipartFile file) throws IOException {
        List<String> content = new ArrayList<>();
        try (Workbook workbook = file.getOriginalFilename().endsWith(".xlsx") ? new XSSFWorkbook(file.getInputStream())
                : new HSSFWorkbook(file.getInputStream())) {

            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                for (Row row : sheet) {
                    List<String> cellValues = new ArrayList<>();
                    for (Cell cell : row) {
                        cellValues.add(getCellValue(cell));
                    }
                    if (!cellValues.isEmpty()) {
                        content.add(String.join(",", cellValues));
                    }
                }
            }
        }
        return content;
    }

    /**
     * 获取单元格的值
     */
    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getLocalDateTimeCellValue().toString();
                }
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 处理Word文件
     *
     * @param file Word文件
     * @return 处理后的文本内容
     */
    public static List<String> processWord(MultipartFile file) throws IOException {
        List<String> content = new ArrayList<>();
        String fileName = file.getOriginalFilename().toLowerCase();

        if (fileName.endsWith(".docx")) {
            try (XWPFDocument doc = new XWPFDocument(file.getInputStream())) {
                // 获取所有段落
                doc.getParagraphs().stream()
                        .map(paragraph -> paragraph.getText().trim())
                        .filter(text -> !text.isEmpty())
                        .forEach(content::add);

                // 获取所有表格中的文本
                doc.getTables().forEach(table -> table.getRows().forEach(row -> row.getTableCells().forEach(cell -> {
                    String text = cell.getText().trim();
                    if (!text.isEmpty()) {
                        content.add(text);
                    }
                })));
            }
        } else if (fileName.endsWith(".doc")) {
            try (HWPFDocument doc = new HWPFDocument(file.getInputStream())) {
                WordExtractor extractor = new WordExtractor(doc);
                Arrays.stream(extractor.getParagraphText())
                        .map(String::trim)
                        .filter(line -> !line.isEmpty())
                        .forEach(content::add);
            }
        }
        return content;
    }

    /**
     * 处理PDF文件
     *
     * @param file PDF文件
     * @return 处理后的文本内容
     */
    public static List<String> processPdf(MultipartFile file) throws IOException {
        List<String> content = new ArrayList<>();
        try (PDDocument document = PDDocument.load(file.getInputStream())) {
            PDFTextStripper stripper = new PDFTextStripper();
            // 按页读取
            for (int i = 1; i <= document.getNumberOfPages(); i++) {
                stripper.setStartPage(i);
                stripper.setEndPage(i);
                String text = stripper.getText(document);
                // 按行分割并添加到列表
                Arrays.stream(text.split("\\r?\\n"))
                        .map(String::trim)
                        .filter(line -> !line.isEmpty())
                        .forEach(content::add);
            }
        }
        return content;
    }

    /**
     * 处理TXT文件
     *
     * @param file TXT文件
     * @return 处理后的文本内容
     */
    public static List<String> processTxt(MultipartFile file) throws IOException {
        List<String> content = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    content.add(line.trim());
                }
            }
        }
        return content;
    }

    /**
     * 根据文件类型处理文件内容
     *
     * @param file 文件
     * @return 处理后的文本内容
     */
    public static List<String> processFile(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            return new ArrayList<>();
        }

        String fileName = file.getOriginalFilename().toLowerCase();
        if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
            return processExcel(file);
        } else if (fileName.endsWith(".doc") || fileName.endsWith(".docx")) {
            return processWord(file);
        } else if (fileName.endsWith(".pdf")) {
            return processPdf(file);
        } else if (fileName.endsWith(".txt")) {
            return processTxt(file);
        } else if (fileName.endsWith(".json")) {
            return processTxt(file);
        } else {
            throw new IllegalArgumentException("不支持的文件格式：" + fileName);
        }
    }

    /**
     * 根据文件类型处理文件内容 (File 版本)
     *
     * @param file 文件
     * @return 处理后的文本内容
     */
    public static List<String> processFile(File file) throws IOException {
        if (file == null || !file.exists() || !file.isFile()) {
            return new ArrayList<>();
        }

        String fileName = file.getName().toLowerCase();
        if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
            return processExcel(file);
        } else if (fileName.endsWith(".doc") || fileName.endsWith(".docx")) {
            return processWord(file);
        } else if (fileName.endsWith(".pdf")) {
            return processPdf(file);
        } else if (fileName.endsWith(".txt") || fileName.endsWith(".json")) {
            return processTxt(file);
        } else {
            throw new IllegalArgumentException("不支持的文件格式：" + fileName);
        }
    }

    /**
     * 处理Excel文件 (File 版本)
     *
     * @param file Excel文件
     * @return 处理后的文本内容
     */
    public static List<String> processExcel(File file) throws IOException {
        List<String> content = new ArrayList<>();
        try (InputStream is = new FileInputStream(file);
             Workbook workbook = file.getName().endsWith(".xlsx") ? new XSSFWorkbook(is) : new HSSFWorkbook(is)) {

            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                for (Row row : sheet) {
                    List<String> cellValues = new ArrayList<>();
                    for (Cell cell : row) {
                        cellValues.add(getCellValue(cell));
                    }
                    if (!cellValues.isEmpty()) {
                        content.add(String.join(",", cellValues));
                    }
                }
            }
        }
        return content;
    }

    /**
     * 处理Word文件 (File 版本)
     *
     * @param file Word文件
     * @return 处理后的文本内容
     */
    public static List<String> processWord(File file) throws IOException {
        List<String> content = new ArrayList<>();
        String fileName = file.getName().toLowerCase();

        if (fileName.endsWith(".docx")) {
            try (InputStream is = new FileInputStream(file);
                 XWPFDocument doc = new XWPFDocument(is)) {
                // 获取所有段落
                doc.getParagraphs().stream()
                        .map(paragraph -> paragraph.getText().trim())
                        .filter(text -> !text.isEmpty())
                        .forEach(content::add);

                // 获取所有表格中的文本
                doc.getTables().forEach(table -> table.getRows().forEach(row -> row.getTableCells().forEach(cell -> {
                    String text = cell.getText().trim();
                    if (!text.isEmpty()) {
                        content.add(text);
                    }
                })));
            }
        } else if (fileName.endsWith(".doc")) {
            try (InputStream is = new FileInputStream(file);
                 HWPFDocument doc = new HWPFDocument(is)) {
                WordExtractor extractor = new WordExtractor(doc);
                Arrays.stream(extractor.getParagraphText())
                        .map(String::trim)
                        .filter(line -> !line.isEmpty())
                        .forEach(content::add);
            }
        }
        return content;
    }

    /**
     * 处理PDF文件 (File 版本)
     *
     * @param file PDF文件
     * @return 处理后的文本内容
     */
    public static List<String> processPdf(File file) throws IOException {
        List<String> content = new ArrayList<>();
        try (InputStream is = new FileInputStream(file);
             PDDocument document = PDDocument.load(is)) {
            PDFTextStripper stripper = new PDFTextStripper();
            // 按页读取
            for (int i = 1; i <= document.getNumberOfPages(); i++) {
                stripper.setStartPage(i);
                stripper.setEndPage(i);
                String text = stripper.getText(document);
                // 按行分割并添加到列表
                Arrays.stream(text.split("\\r?\\n"))
                        .map(String::trim)
                        .filter(line -> !line.isEmpty())
                        .forEach(content::add);
            }
        }
        return content;
    }

    /**
     * 处理TXT文件 (File 版本)
     *
     * @param file TXT文件
     * @return 处理后的文本内容
     */
    public static List<String> processTxt(File file) throws IOException {
        List<String> content = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    content.add(line.trim());
                }
            }
        }
        return content;
    }
}
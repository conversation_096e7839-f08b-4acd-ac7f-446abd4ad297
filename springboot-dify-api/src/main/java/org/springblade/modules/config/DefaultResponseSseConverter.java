package org.springblade.modules.config;

import cn.hutool.json.JSONUtil;
import com.feiniaojin.gracefulresponse.defaults.DefaultResponseImplStyle0;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.AbstractHttpMessageConverter;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-03-21 23:23
 */
public class DefaultResponseSseConverter extends AbstractHttpMessageConverter<DefaultResponseImplStyle0> {

    public DefaultResponseSseConverter() {
        super(MediaType.TEXT_EVENT_STREAM);
    }

    @Override
    protected boolean supports(Class<?> clazz) {
        return DefaultResponseImplStyle0.class.isAssignableFrom(clazz);
    }

    @Override
    protected DefaultResponseImplStyle0 readInternal(Class<? extends DefaultResponseImplStyle0> clazz, HttpInputMessage inputMessage) throws IOException, HttpMessageNotReadableException {
        throw new UnsupportedOperationException("SSE converter does not support reading.");
    }

    @Override
    protected void writeInternal(DefaultResponseImplStyle0 response, HttpOutputMessage outputMessage) throws IOException, HttpMessageNotWritableException {
        try (OutputStream outputStream = outputMessage.getBody()) {
            String data;
            if (response.getStatus().getCode() == "1") {
                data = "data: " + JSONUtil.toJsonStr(response) + "\n\n"; // SSE 格式
            } else {
                data = "data: " + response.toString() + "\n\n"; // SSE 格式
            }
            outputStream.write(data.getBytes(StandardCharsets.UTF_8));
        }
    }

}
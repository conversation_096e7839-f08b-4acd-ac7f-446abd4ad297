package org.springblade.modules.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * Web配置类
 * 处理跨域请求配置
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * 作为模块开发导入时需重新修改跨域策略
     *
     * 配置跨域请求
     * 允许所有域的请求访问所有API端点
     * @param registry CORS注册表
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**") // 应用到所有路径
                .allowedOriginPatterns("*") // 允许所有来源
                .allowedMethods("*") // 允许所有HTTP方法
                .allowedHeaders("*") // 允许所有请求头
                .allowCredentials(true) // 不允许发送凭证（当allowedOrigins为*时，必须为false）
                .maxAge(3600); // 预检请求的有效期，单位为秒
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
//        registry.addResourceHandler("/**").addResourceLocations("classpath:/static/");
//        registry.addResourceHandler("/assets").addResourceLocations("classpath:/static/home");
        registry.addResourceHandler("/assets").addResourceLocations("classpath:/static/assets");
    }

//    @Override
//    public void addViewControllers(ViewControllerRegistry registry) {
//        registry.addViewController("/{spring:\\w+}").setViewName("forward:/");
//        registry.addViewController("/**").setViewName("forward:/index.html");
//    }

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(new DefaultResponseSseConverter());
    }
}
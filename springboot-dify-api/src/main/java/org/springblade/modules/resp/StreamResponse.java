package org.springblade.modules.resp;

import java.io.Serializable;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;


/**
 * Dify流式调用响应.
 */
@Data
public class StreamResponse implements Serializable {

    /**
     * 不同模式下的事件类型.
     */
    private String event;

    /**
     * agent_thought id.
     */
    private String id;

    /**
     * 任务ID.
     */
    private String task_id;

    /**
     * 消息唯一ID.
     */
    private String message_id;

    /**
     * LLM 返回文本块内容.
     */
    private String answer;

    /**
     * 创建时间戳.
     */
    private Long created_at;

    /**
     * 会话 ID.
     */
    private String conversation_id;

    private JSONObject metadata;
}

package org.springblade.modules.security;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.security.jwt.JwtTokenProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * Spring Security配置类
 * 支持用户名密码登录，用户名和密码固定为admin/admin
 */
@Configuration
@Slf4j
public class UsernamePasswordSecurityConfig {

    @Resource
    UsernamePasswordUserDetailsService usernamePasswordUserDetailsService;

    @Resource
    JwtTokenProvider jwtTokenProvider;

    String loginUrl = "/enclosure/user/login";

    @Value("${base.url}")
    String baseUri;

    @Bean
    public UsernamePasswordAuthenticationFilter usernamePasswordAuthenticationFilter() {
        UsernamePasswordOAuth2Filter filter = new UsernamePasswordOAuth2Filter();
        filter.setAuthenticationManager(userNameAuthenticationManager());
        filter.setFilterProcessesUrl(loginUrl);
        filter.setJwtTokenProvider(jwtTokenProvider); // 设置JWT提供者
        filter.setAuthenticationSuccessHandler((request, response, authentication) -> {
            log.info("登录成功");
            // 注意：成功处理器已经在filter中实现，这里只是为了兼容性
            // JWT令牌已经在UsernamePasswordOAuth2Filter的successfulAuthentication方法中生成
        });
        return filter;
    }

    @Bean
    AuthenticationManager userNameAuthenticationManager() {
        DaoAuthenticationProvider authenticationProvider = new DaoAuthenticationProvider();
        authenticationProvider.setUserDetailsService(usernamePasswordUserDetailsService);
        authenticationProvider.setPasswordEncoder(new BCryptPasswordEncoder());
        return new ProviderManager(authenticationProvider);
    }
}
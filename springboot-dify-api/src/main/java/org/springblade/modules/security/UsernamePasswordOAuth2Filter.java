package org.springblade.modules.security;

import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.security.jwt.JwtTokenProvider;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;

import java.io.IOException;

/**
 * 用户名密码登录过滤器
 * 处理表单登录请求
 */
@Slf4j
public class UsernamePasswordOAuth2Filter extends UsernamePasswordAuthenticationFilter {

    private JwtTokenProvider jwtTokenProvider;

    public void setJwtTokenProvider(JwtTokenProvider jwtTokenProvider) {
        this.jwtTokenProvider = jwtTokenProvider;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) {
        String username = obtainUsername(request);
        String password = obtainPassword(request);

        log.info("尝试使用用户名密码登录: {}", username);

        UsernamePasswordAuthenticationToken authRequest =
                new UsernamePasswordAuthenticationToken(username, password);

        setDetails(request, authRequest);

        // 如果使用JWT，则不需要设置会话安全上下文存储库
        if (jwtTokenProvider == null) {
            this.setSecurityContextRepository(new HttpSessionSecurityContextRepository());
        }

        return this.getAuthenticationManager().authenticate(authRequest);
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response,
                                           FilterChain chain, Authentication authResult) throws IOException, ServletException {
        // 如果配置了JWT提供者，则生成JWT令牌
        if (jwtTokenProvider != null) {
            String token = jwtTokenProvider.createToken(authResult);

            // 构建响应
            JSONObject jsonResponse = new JSONObject();
            jsonResponse.put("status", "success");
            jsonResponse.put("message", "登录成功");
            jsonResponse.put("token", token);
            jsonResponse.put("tokenType", "Bearer");

            // 添加用户信息
            CustomUserDetails userDetails = (CustomUserDetails) authResult.getPrincipal();
            jsonResponse.put("user", userDetails.getAttributes());

            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(jsonResponse.toString());
        } else {
            // 如果没有配置JWT，则使用默认的会话行为
            super.successfulAuthentication(request, response, chain, authResult);
        }
    }
}
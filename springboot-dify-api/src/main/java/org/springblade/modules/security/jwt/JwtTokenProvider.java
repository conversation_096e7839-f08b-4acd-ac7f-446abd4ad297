package org.springblade.modules.security.jwt;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class JwtTokenProvider {

    @Value("${jwt.secret:defaultSecretKeyThatShouldBeChangedInProduction}")
    private String secretKey;

    @Value("${jwt.expiration:86400000}") // 默认24小时
    private long validityInMilliseconds;

    private SecretKey key;

    @PostConstruct
    protected void init() {
        this.key = Keys.hmacShaKeyFor(secretKey.getBytes(StandardCharsets.UTF_8));
    }

    public String createToken(Authentication authentication) {
        CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();

        Map<String, Object> claims = new HashMap<>();
        claims.put("id", userDetails.getAttributes().getId());
        claims.put("email", userDetails.getAttributes().getEmail());
        claims.put("name", userDetails.getAttributes().getName());

        if (userDetails.getAttributes().getWorkNo() != null) {
            claims.put("workNo", userDetails.getAttributes().getWorkNo());
        }

        if (userDetails.getIntegrate() != null) {
            claims.put("integrate", userDetails.getIntegrate());
        }

        // 添加权限信息
        List<String> roles = userDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());
        claims.put("roles", roles);

        Date now = new Date();

        // 创建JWT构建器
        JwtBuilder jwtBuilder = Jwts.builder()
                .setClaims(claims)
                .setSubject(userDetails.getUsername())
                .setIssuedAt(now)
                .signWith(key);

        // 如果过期时间大于0，则设置过期时间
        // 如果过期时间小于等于0，则不设置过期时间，表示永不过期
        if (validityInMilliseconds > 0) {
            Date validity = new Date(now.getTime() + validityInMilliseconds);
            jwtBuilder.setExpiration(validity);
        }

        return jwtBuilder.compact();
    }

    public Authentication getAuthentication(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();

        // 从claims中提取用户信息
        String id = claims.get("id", String.class);
        String email = claims.get("email", String.class);
        String name = claims.get("name", String.class);

        // 构建Account对象
        org.springblade.modules.entity.Account account = new org.springblade.modules.entity.Account();
        account.setId(id);
        account.setEmail(email);
        account.setName(name);

        // 可选字段
        if (claims.containsKey("workNo")) {
            account.setWorkNo(claims.get("workNo", String.class));
        }

        // 提取权限信息
        List<String> roles = claims.get("roles", List.class);
        Set<GrantedAuthority> authorities = new HashSet<>();
        if (roles != null) {
            authorities = roles.stream()
                    .map(SimpleGrantedAuthority::new)
                    .collect(Collectors.toSet());
        }

        // 创建CustomUserDetails
        CustomUserDetails userDetails = new CustomUserDetails(account, authorities);
        if (claims.containsKey("integrate")) {
            userDetails.setIntegrate(claims.get("integrate", String.class));
        }

        return new UsernamePasswordAuthenticationToken(userDetails, "", authorities);
    }

    public boolean validateToken(String token) {
        try {
            // 解析令牌
            Jws<Claims> claimsJws = Jwts.parserBuilder().setSigningKey(key).build().parseClaimsJws(token);

            // 检查令牌是否过期
            // 如果令牌没有过期时间（永不过期），则始终有效
            Claims claims = claimsJws.getBody();
            Date expiration = claims.getExpiration();
            if (expiration == null) {
                return true; // 没有过期时间，表示永不过期
            }

            // 如果有过期时间，则检查是否过期
            // 这个检查在parseClaimsJws中已经完成，如果过期会抛出异常
            return true;

        } catch (JwtException | IllegalArgumentException e) {
            log.error("Invalid JWT token: {}", e.getMessage());
            return false;
        }
    }
}

# 项目说明
基于SpringBoot封装了调用Dify服务的API

## 功能特性
- 封装了Dify API的调用接口
- 支持阻塞式和流式响应
- 配置了全局跨域访问，允许所有域的请求

## 接口说明
- `/api/test/block`: 阻塞式调用Dify API，返回完整回答
- `/api/test/stream`: 流式调用Dify API，返回流式响应

## 跨域配置
项目已配置允许所有域的跨域请求访问，配置如下：
- 允许所有来源 (`allowedOrigins("*")`)
- 允许所有HTTP方法 (`allowedMethods("*")`)
- 允许所有请求头 (`allowedHeaders("*")`)
- 预检请求有效期为3600秒

## 使用方法
### 本地运行
1. 配置`application.yml`中的Dify API密钥和URL
2. 启动应用
3. 通过API接口调用Dify服务

### Docker部署
项目提供了Docker部署支持，可以通过以下方式部署：

#### 使用Dockerfile构建镜像
```bash
# 构建Docker镜像
docker build -t dify-api .

# 运行容器
docker run -p 8080:8080 dify-api
```

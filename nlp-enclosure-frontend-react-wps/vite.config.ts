import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';
import vitePluginImp from 'vite-plugin-imp';
import { copyFile, traitJsAsJsx } from "wpsjs/vite_plugins"


// const ORIGIN_SERVER = import.meta.env.VITE_ORIGIN_SERVER;
// https://vitejs.dev/config/
// Vite 默认是不加载 .env 文件的，因为这些文件需要在执行完 Vite 配置后才能确定加载哪一个
export default defineConfig(({ command, mode }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  // 设置第三个参数为 '' 来加载所有环境变量，而不管是否有 `VITE_` 前缀。
  const env = loadEnv(mode, process.cwd(), '');
  return {
    // 设置为相对路径，确保构建后的资源使用相对路径引用
    // './' - 使用相对路径，适用于任何路径部署
    // '/nlp-portal/' - 如果部署在特定子路径下，使用这种格式
    base: './',
    esbuild: {
      loader: "tsx",
      include: /src\/.*\.[jt]sx?$/,
      exclude: [],
    },
    optimizeDeps: {
      esbuildOptions: {
        loader: { ".js": "jsx", ".ts": "tsx" },
        plugins: [traitJsAsJsx()],
      },
    },
    plugins: [
      copyFile({
        src: 'manifest.xml',
        dest: 'manifest.xml',
      }),
      react(),
      vitePluginImp({
        libList: [
          // 按需引入 nutui
          {
            libName: '@nutui/nutui-react',
            style: (name) => {
              return `@nutui/nutui-react/dist/esm/${name}/style/css`;
            },
            replaceOldImport: false,
            camel2DashComponentName: false
          },
          // 按需引入 antd
          {
            libName: 'antd',
            style(name) {
              // use less
              return `antd/es/${name}/style/index.js`;
            }
          }
        ]
      })
    ],
    resolve: {
      alias: {
        react: 'preact/compat',
        'react-dom/test-utils': 'preact/test-utils',
        'react-dom': 'preact/compat',
        'react/jsx-runtime': 'preact/jsx-runtime'
      }
    },
    server: {
      host: '0.0.0.0',
    },
  };
});

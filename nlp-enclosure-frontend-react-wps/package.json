{"name": "enclosure-wps", "addonType": "wps", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite --port 3889", "build": "vite build --mode pro", "lint": "eslint src --ext ts,tsx --fix --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --host"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/x": "^1.1.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.6", "@nutui/nutui-react": "^2.3.5", "@wecom/jssdk": "^2.2.6", "antd": "^5.24.6", "antd-style": "^3.7.1", "autoprefixer": "^10.4.14", "axios": "^1.4.0", "dayjs": "^1.11.10", "event-source-polyfill": "^1.0.31", "js-cookie": "^3.0.5", "markdown-it": "^14.1.0", "preact": "^10.21.0", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-query": "^3.39.3", "react-router-dom": "^6.21.1"}, "devDependencies": {"@types/node": "^20.3.1", "@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^4.0.0", "postcss": "^8.4.24", "prettier": "^3.2.5", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.3.2", "typescript": "^5.0.2", "vite": "^4.3.9", "vite-plugin-imp": "^2.4.0", "wps-jsapi-declare": "latest", "wpsjs": "latest"}}
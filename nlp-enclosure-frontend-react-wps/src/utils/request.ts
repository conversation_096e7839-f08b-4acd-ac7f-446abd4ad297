import axios from 'axios';
import {clearUserInfoFormLocalStorage, wpsLogin} from "../services/user";
import { getApiBaseUrl } from './env';

// 存储认证令牌
export const setAuthToken = (token: string) => {
    localStorage.setItem('authToken', token);
};

// 获取认证令牌
export const getAuthToken = (): string | null => {
    return localStorage.getItem('authToken');
};

// 清除认证令牌
export const clearAuthToken = () => {
    localStorage.removeItem('authToken');
};


// Get the API base URL from environment configuration
const baseUrl = getApiBaseUrl();

const http = axios.create({
  baseURL: baseUrl,
  timeout: 30000,
  withCredentials: true, // 允许跨域请求携带凭证（cookies）
});

// 请求拦截器
http.interceptors.request.use(
    (config) => {
      // 在发送请求之前对请求配置进行处理
      // 可以添加请求头、验证等操作

        const token = getAuthToken();
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
        }

      // 确保跨域请求携带凭证
      config.withCredentials = true;

      return config;
    },
    (error) => {
      // 请求错误处理
      console.error('Request interceptor error:', error);

      return Promise.reject(error);
    }
);

// 响应拦截器
http.interceptors.response.use(
    (response) => {
      // 对响应数据进行处理
      // 可以进行数据转换、错误处理等操作
      return response.data;
    },
    (error) => {
      // 响应错误处理
      console.error('Response interceptor error:', error);

      // 处理401错误，如果是401就清除用户信息并跳转到登录页
      if (error.response && error.response.status === 401) {
        clearUserInfoFormLocalStorage();

        // 获取当前路径，如果不是登录页，则跳转到登录页
        const currentPath = window.location.hash;
        if (!currentPath.includes('#/login')) {
          window.location.href = '#/login';
        }
      }

      return Promise.reject(error);
    }
);

export default http;

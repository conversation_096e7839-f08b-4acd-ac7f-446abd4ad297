/**
 * 机器唯一标识工具
 * 提供获取当前WPS插件运行机器的唯一标识功能
 */

/**
 * 生成GUID
 * @returns 生成的GUID字符串
 */
function generateGuid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 尝试从WPS API获取机器信息
 * @returns 机器信息字符串或null
 */
function getMachineInfoFromWpsApi(): string | null {
  try {
    // 尝试获取WPS系统信息
    if (window.Application && window.Application.System) {
      // 尝试获取计算机名称
      const computerName = (window.Application.System as any).ComputerName;
      if (computerName) {
        return `wps_${computerName}`;
      }
      
      // 如果没有ComputerName，尝试获取其他系统信息
      const username = (window.Application.System as any).Username;
      if (username) {
        return `wps_user_${username}`;
      }
    }
    
    // 尝试获取WPS环境信息
    if (window.Application && window.Application.Env) {
      const rootPath = window.Application.Env.GetRootPath();
      if (rootPath) {
        // 使用安装路径的哈希作为机器标识的一部分
        return `wps_path_${hashString(rootPath)}`;
      }
    }
    
    return null;
  } catch (error) {
    console.error('获取WPS机器信息失败:', error);
    return null;
  }
}

/**
 * 从浏览器环境获取机器指纹信息
 * @returns 机器指纹信息
 */
function getBrowserFingerprint(): string {
  const screenInfo = `${window.screen.height}_${window.screen.width}_${window.screen.colorDepth}`;
  const timeZone = new Date().getTimezoneOffset();
  const language = navigator.language;
  const platform = navigator.platform;
  const userAgent = navigator.userAgent;
  
  // 组合信息并生成哈希
  const fingerprint = `${screenInfo}_${timeZone}_${language}_${platform}_${hashString(userAgent)}`;
  return `browser_${hashString(fingerprint)}`;
}

/**
 * 简单的字符串哈希函数
 * @param str 要哈希的字符串
 * @returns 哈希值
 */
function hashString(str: string): string {
  let hash = 0;
  if (str.length === 0) return hash.toString(16);
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  return Math.abs(hash).toString(16);
}

/**
 * 获取或生成机器唯一标识
 * @returns 机器唯一标识字符串
 */
export function getMachineId(): string {
  // 首先尝试从localStorage获取已存储的机器ID
  const storedMachineId = localStorage.getItem('wps_machine_id');
  if (storedMachineId) {
    return storedMachineId;
  }
  
  // 尝试从WPS API获取机器信息
  const wpsApiMachineId = getMachineInfoFromWpsApi();
  if (wpsApiMachineId) {
    // 存储到localStorage以便下次使用
    localStorage.setItem('wps_machine_id', wpsApiMachineId);
    return wpsApiMachineId;
  }
  
  // 尝试使用浏览器指纹
  const browserFingerprint = getBrowserFingerprint();
  
  // 组合生成最终的机器ID
  const machineId = browserFingerprint;
  
  // 存储到localStorage以便下次使用
  localStorage.setItem('wps_machine_id', machineId);
  
  return machineId;
}

/**
 * 重置机器ID（用于测试或特殊情况）
 */
export function resetMachineId(): void {
  localStorage.removeItem('wps_machine_id');
}

export default {
  getMachineId,
  resetMachineId
};

/**
 * Environment utility functions
 * This file provides access to environment variables and configuration
 */

const devUrl = 'http://127.0.0.1:8080';
const prodUrl = "http://172.16.219.190:32682/"

const env = "dev"

// Get the current environment (dev, test, pro)
export const getEnv = (): string => {
  return env;
};

// Get the API base URL
export const getApiBaseUrl = (): string => {
  return getEnv() === 'dev' ? devUrl : prodUrl;
};


export default {
  getEnv,
  getApiBaseUrl,
};

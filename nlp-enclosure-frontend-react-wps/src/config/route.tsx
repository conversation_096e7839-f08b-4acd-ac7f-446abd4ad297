import App from '../App.tsx';
import Independent from "../pages/home";
import Login from "../pages/login";
import IframeHome from '../pages/iframeHome';
import IdaasLogin from '../pages/idaasLogin';
import { Navigate } from 'react-router-dom';

export const DefaultRoutes = [
  {
    path: '/',
    element: <App />,
    children: [
      {
        index: true,
        element: <Navigate to="home" replace />
      },
      {
        path: 'home',
        name: '主页',
        element: <Independent/>
      },
      {
        path: 'login',
        name: '登录',
        element: <Login/>
      },
      {
        path: 'idaasLogin',
        name: 'idaas登录',
        element: <IdaasLogin/>
      },
      {
        path: 'iframeHome',
        name: 'iframeHome',
        element: <IframeHome/>
      }
    ]
  }
];

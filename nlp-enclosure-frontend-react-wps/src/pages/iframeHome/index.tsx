import { FC, useEffect } from 'react';
import {useLocation} from "react-router-dom";

const IFrameHome: FC = () => {
  const location = useLocation();
  const llmDefaultSelect = new URLSearchParams(location.search).get('agent') || 'qwen';

  // const url = "http://127.0.0.1:5173/#/wpsApp";
  const url = "https://ai.gz-tobacco.net/index.html#/wpsApp";

  useEffect(() => {
    // 向window对象添加一个属性，用于在iframe中获取
    window.Application.PluginStorage.setItem("llmDefaultSelect", llmDefaultSelect)
  }, []);

  return (
    <>
      <iframe style={{width: '100%', height: '100%'}} src={url}/>
    </>
  )

}

export default IFrameHome;

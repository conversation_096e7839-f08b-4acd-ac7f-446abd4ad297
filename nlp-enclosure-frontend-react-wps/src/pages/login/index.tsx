import { FC, useEffect, useState } from 'react';
import { Spin } from 'antd';
import { useNavigate } from 'react-router-dom';
import { clearUserInfoFormLocalStorage, wpsLogin } from '../../services/user';
import './style.css';

const Login: FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const handleLogin = async () => {
      try {
        // 清除本地存储中的用户信息
        clearUserInfoFormLocalStorage();

        // 调用 wpsLogin 方法进行登录
        const userInfo = await wpsLogin();

        // 登录成功后，跳转到 /home 页面
        if (userInfo) {
          navigate('/home');
        } else {
          console.error('登录失败: 未获取到用户信息');
          setLoading(false);
        }
      } catch (error) {
        console.error('登录过程中发生错误:', error);
        setLoading(false);
      }
    };

    // 执行登录流程
    handleLogin();
  }, [navigate]);

  return (
    <div className="login-container">
      <Spin spinning={loading} size="large" tip="正在登录中..." />
      {!loading && (
        <div className="login-error">
          登录失败，请刷新页面重试
        </div>
      )}
    </div>
  );
};

export default Login;

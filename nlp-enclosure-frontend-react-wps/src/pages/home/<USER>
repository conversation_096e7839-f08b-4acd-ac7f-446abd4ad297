.ant-conversations-menu-icon {
    color: black !important;
}

.llm-dropdown {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.llm-dropdown .title {
    font-size: 14px;
}

.llm-dropdown .desc {
    font-size: 12px;
    color: rgb(175, 177, 196);
}

.ant-menu-title-content {
    width: 100%;
}

.sender-input {
    font-size: 16px;
    height: auto;
}

.ai-content-class {
    :is(details) {
        border-radius: 12px !important;
    }

    :is(table) {
        border-collapse: collapse;
        width: 100%;
        margin: 16px 0;
        background-color: white;
        border: 1px solid #f0f0f0;
    }

    :is(th), :is(td) {
        padding: 12px;
        text-align: left;
        border: 1px solid #f0f0f0;
    }

    :is(th) {
        background-color: #fafafa;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
    }

    :is(tr):hover {
        background-color: #fafafa;
    }

    :is(tr):nth-child(even) {
        background-color: #fafcff;
    }

    &.table-container {
        overflow-x: auto;
        max-width: 100%;
    }
}

/* 输入框动画效果 */
.sender-bottom {
    transform: translateY(0);
    transition: transform 0.5s ease-in-out;
}

.sender-center {
    transform: translateY(25vh);
    transition: transform 0.5s ease-in-out;
}

.ant-sender-footer {
    padding-block-end: 8px !important;
}

.qwen-think-icon {
    width: 1.5rem;
    height: 1.5rem;
    margin-right: 5px;
}

.ant-radio-button-wrapper:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color:  rgba(0, 0, 0, 0.85);
    fill: rgba(0, 0, 0, 0.85);
}

.ant-radio-button-wrapper-checked {
    fill: #ffffff;

    &:hover {
        fill: #ffffff;
    }
}

.ant-radio-button-wrapper-disabled {
    fill: rgba(0, 0, 0, 0.25);
}

.ant-radio-button-wrapper-disabled:hover {
    color: rgba(0, 0, 0, 0.25);
    fill: rgba(0, 0, 0, 0.25);
}

.ant-radio-button-wrapper-checked.ant-radio-button-wrapper-disabled {
    background-color: #1677ff;
    color: #fff;
    fill: #fff;
}
.ant-radio-button-wrapper-checked.ant-radio-button-wrapper-disabled:hover {
    background-color: #1677ff;
    color: #fff;
}

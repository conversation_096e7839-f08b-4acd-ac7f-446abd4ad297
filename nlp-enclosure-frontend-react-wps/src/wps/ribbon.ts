import Util from '../utils/util';
import SystemDemo from '../utils/systemdemo';

interface Control {
    Id: string;
}

interface RibbonUI {
    InvalidateControl: (controlId: string) => void;
    Invalidate: () => void;
}

let WebNotifycount = 0;

/**
 * Opens or refreshes the home panel with the specified agent type
 * @param agentType - The type of agent to use (qwen, qwq, deepseek)
 */
function openHomePanel(agentType: string): void {
    // Check if home panel already exists
    let homePanelId = window.Application.PluginStorage.getItem("home_panel_id");

    if (!homePanelId) {
        // If panel doesn't exist, create a new one
        const webPanel = window.Application.CreateTaskPane(Util.GetUrlPath() + `/home?agent=${agentType}`);
        const id = webPanel.ID;
        window.Application.PluginStorage.setItem("home_panel_id", id);
        webPanel.Visible = true;
    } else {
        try {
            // If panel exists, get it
            const webPanel = window.Application.GetTaskPane(homePanelId);

            // Toggle visibility if it's already visible
            if (webPanel.Visible) {
                (webPanel as any).Navigate(Util.GetUrlPath() + `/home?agent=${agentType}`);
            } else {
                // If it's not visible, make it visible
                webPanel.Visible = true;

                // Since we can't directly navigate, we'll close this panel and create a new one
                // This is a workaround since the WPSTaskPane interface doesn't have a Navigate method
                try {
                    // Try to use Navigate method if it exists (not in type definitions but might exist in runtime)
                    (webPanel as any).Navigate(Util.GetUrlPath() + `/home?agent=${agentType}`);
                } catch (e) {
                    // If Navigate method doesn't exist, close the current panel and create a new one
                    webPanel.Visible = false;
                    window.Application.PluginStorage.setItem("home_panel_id", ""); // Clear the ID by setting to empty string

                    // Create a new panel with the specified agent
                    const newWebPanel = window.Application.CreateTaskPane(Util.GetUrlPath() + `/home?agent=${agentType}`);
                    const newId = newWebPanel.ID;
                    window.Application.PluginStorage.setItem("home_panel_id", newId);
                    newWebPanel.Visible = true;
                }
            }
        } catch (e) {
            // If there's an error getting the panel (it might have been closed externally),
            // create a new one
            window.Application.PluginStorage.setItem("home_panel_id", ""); // Clear the ID by setting to empty string
            const webPanel = window.Application.CreateTaskPane(Util.GetUrlPath() + `/home?agent=${agentType}`);
            const id = webPanel.ID;
            window.Application.PluginStorage.setItem("home_panel_id", id);
            webPanel.Visible = true;
        }
    }
}

const ribbon = {
    // 这个函数在整个wps加载项中是第一个执行的
    OnAddinLoad: function(ribbonUI: RibbonUI): boolean {
        if (typeof (window.Application.ribbonUI) !== "object") {
            window.Application.ribbonUI = ribbonUI;
        }

        if (typeof (window.Application.Enum) !== "object") { // 如果没有内置枚举值
            window.Application.Enum = Util.WPS_Enum;
        }

        // 这几个导出函数是给外部业务系统调用的
        window.openOfficeFileFromSystemDemo = SystemDemo.openOfficeFileFromSystemDemo;
        window.InvokeFromSystemDemo = SystemDemo.InvokeFromSystemDemo;

        window.Application.PluginStorage.setItem("EnableFlag", false); // 往PluginStorage中设置一个标记，用于控制两个按钮的置灰
        window.Application.PluginStorage.setItem("ApiEventFlag", false); // 往PluginStorage中设置一个标记，用于控制ApiEvent的按钮label

        // Clear old storage keys for individual agent panels (migration)
        window.Application.PluginStorage.setItem("qwen_web_id", "");
        window.Application.PluginStorage.setItem("qwq_web_id", "");
        window.Application.PluginStorage.setItem("deepseek_web_id", "");
        return true;
    },

    OnAction: function(control: Control): boolean {
        const eleId = control.Id;
        console.log(Util.GetUrlPath())
        switch (eleId) {
            case "btnQwen": {
                openHomePanel("qwen");
                break;
            }
            case "btnQwQ": {
                openHomePanel("qwq");
                break;
            }
            case "btnDeepseek": {
                openHomePanel("deepseek");
                break;
            }
            case "btnShowMsg": {
                const doc = window.Application.ActiveDocument;
                if (!doc) {
                    alert("当前没有打开任何文档");
                    return true;
                }
                alert(doc.Name);
                break;
            }
            case "btnIsEnbable": {
                const bFlag = window.Application.PluginStorage.getItem("EnableFlag") === "true";
                window.Application.PluginStorage.setItem("EnableFlag", (!bFlag).toString());

                // 通知wps刷新以下几个按饰的状态
                window.Application.ribbonUI.InvalidateControl("btnIsEnbable");
                window.Application.ribbonUI.InvalidateControl("btnShowDialog");
                window.Application.ribbonUI.InvalidateControl("btnShowTaskPane");
                // window.Application.ribbonUI.Invalidate(); 这行代码打开则是刷新所有的按钮状态
                break;
            }
            case "btnShowDialog":
                window.Application.ShowDialog(Util.GetUrlPath() + "dialog", "这是一个对话框网页", 400 * window.devicePixelRatio, 400 * window.devicePixelRatio, false);
                break;
            case "btnShowTaskPane": {
                const tsId = window.Application.PluginStorage.getItem("taskpane_id");
                if (!tsId) {
                    const tskpane = window.Application.CreateTaskPane(Util.GetUrlPath() + "taskpane");
                    const id = tskpane.ID;
                    window.Application.PluginStorage.setItem("taskpane_id", id);
                    tskpane.Visible = true;
                } else {
                    const tskpane = window.Application.GetTaskPane(tsId);
                    tskpane.Visible = !tskpane.Visible;
                }
                break;
            }
            case "btnApiEvent": {
                const bFlag = window.Application.PluginStorage.getItem("ApiEventFlag") === "true";
                const bRegister = !bFlag;
                window.Application.PluginStorage.setItem("ApiEventFlag", bRegister.toString());
                if (bRegister) {
                    (window.Application as any).ApiEvent.AddApiEventListener('DocumentNew', 'ribbon.OnNewDocumentApiEvent');
                } else {
                    (window.Application as any).ApiEvent.RemoveApiEventListener('DocumentNew', 'ribbon.OnNewDocumentApiEvent');
                }

                window.Application.ribbonUI.InvalidateControl("btnApiEvent");
                break;
            }
            case "btnWebNotify": {
                const currentTime = new Date();
                const timeStr = currentTime.getHours() + ':' + currentTime.getMinutes() + ":" + currentTime.getSeconds();
                (window.Application as any).OAAssist.WebNotify("这行内容由wps加载项主动送达给业务系统，可以任意自定义, 比如时间值:" + timeStr + "，次数：" + (++WebNotifycount), true);
                break;
            }
            default:
                break;
        }
        return true;
    },

    GetImage: function(control: Control): string {
        const eleId = control.Id;
        switch (eleId) {
            case "btnShowMsg":
                return "images/1.svg";
            case "btnShowDialog":
                return "images/2.svg";
            case "btnShowTaskPane":
                return "images/3.svg";
            case "btnQwen":
                return "images/Qwen.svg";
            case "btnQwQ":
                return "images/QWQ.svg";
            case "btnDeepseek":
                return "images/deepseek.svg";
            case "btnLogin":
                return "images/newFromTemp.svg";
            default:
                break;
        }
        return "images/newFromTemp.svg";
    },

    OnGetEnabled: function(control: Control): boolean {
        const eleId = control.Id;
        switch (eleId) {
            case "btnShowMsg":
                return true;
            case "btnShowDialog": {
                const bFlag = window.Application.PluginStorage.getItem("EnableFlag") === "true";
                return bFlag;
            }
            case "btnShowTaskPane": {
                const bFlag = window.Application.PluginStorage.getItem("EnableFlag") === "true";
                return bFlag;
            }
            default:
                break;
        }
        return true;
    },

    OnGetVisible: function(control: Control): boolean {
        const eleId = control.Id;
        console.log(eleId);
        return true;
    },

    OnGetLabel: function(control: Control): string {
        const eleId = control.Id;
        switch (eleId) {
            case "btnIsEnbable": {
                const bFlag = window.Application.PluginStorage.getItem("EnableFlag") === "true";
                return bFlag ? "按钮Disable" : "按钮Enable";
            }
            case "btnApiEvent": {
                const bFlag = window.Application.PluginStorage.getItem("ApiEventFlag") === "true";
                return bFlag ? "清除新建文件事件" : "注册新建文件事件";
            }
            default:
                break;
        }
        return "";
    },

    OnNewDocumentApiEvent: function(doc: any): void {
        alert("新建文件事件响应，取文件名: " + doc.Name);
    }
};

export default ribbon;

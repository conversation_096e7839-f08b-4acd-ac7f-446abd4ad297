/**
 * WPS文档操作工具函数
 * 提供与WPS文档交互的常用功能
 */

/**
 * 将内容插入到当前文档的末尾
 * @param content 要插入的内容
 * @returns 是否插入成功
 */
export function insertContentToEnd(content: string): boolean {
  try {
    // 获取当前活动文档
    const doc = window.Application.ActiveDocument;
    if (!doc) {
      console.error('没有打开的文档');
      return false;
    }

    // 获取文档内容范围并移动到末尾
    const range = doc.Content;
    range.Collapse(0); // 0 表示折叠到末尾

    // 插入内容
    range.InsertAfter(content);
    return true;
  } catch (error) {
    console.error('插入内容到文档末尾失败:', error);
    return false;
  }
}

/**
 * 将内容替换当前文档的选中内容
 * @param content 要替换的内容
 * @returns 是否替换成功
 */
export function replaceSelectedContent(content: string): boolean {
  try {
    // 获取当前活动文档
    const doc = window.Application.ActiveDocument;
    if (!doc) {
      console.error('没有打开的文档');
      return false;
    }

    // 获取当前选中内容
    const selection = window.Application.Selection;
    if (!selection || selection.Type === 0) { // 0 表示没有选中内容
      console.error('没有选中内容');
      return false;
    }

    // 替换选中内容
    selection.TypeText(content);
    return true;
  } catch (error) {
    console.error('替换选中内容失败:', error);
    return false;
  }
}

/**
 * 创建新的临时文档并插入内容，并保存在系统临时目录
 * @param content 要插入的内容
 * @param fileName 可选的文件名，不包含路径和扩展名。如果不提供，将生成随机文件名
 * @returns 是否创建并插入成功，成功时返回保存的文件路径，失败时返回空字符串
 */
export function createTempDocWithContent(content: string, fileName?: string): string {
  try {
    // 创建新文档
    const newDoc = window.Application.Documents.Add();
    if (!newDoc) {
      console.error('创建新文档失败');
      return '';
    }

    // 获取文档内容范围
    const range = newDoc.Content;

    // 插入内容
    range.Text = content;

    // 生成临时文件名
    const timestamp = new Date().getTime();
    const randomStr = Math.random().toString(36).substring(2, 8);
    const tempFileName = fileName || `temp_doc_${timestamp}_${randomStr}`;

    // 获取系统临时目录
    // 在WPS环境中，可以使用SpecialFolders获取特殊文件夹路径
    let tempPath = '';
    try {
      // 尝试获取临时文件夹路径
      if (window.Application.System && window.Application.System.SpecialFolders) {
        // 2 代表临时文件夹
        tempPath = window.Application.System.SpecialFolders(2);
      } else {
        // 如果无法获取临时文件夹，则使用默认的文档路径
        tempPath = window.Application.Options.DefaultFilePath(0); // 0 代表文档路径
      }
    } catch (e) {
      console.warn('无法获取系统临时目录，将使用默认路径:', e);
      // 如果上述方法都失败，使用一个固定路径
      tempPath = 'C:\\Temp';
    }

    // 确保路径末尾有分隔符
    if (tempPath && !tempPath.endsWith('\\')) {
      tempPath += '\\';
    }

    // 完整的文件路径
    const fullPath = `${tempPath}${tempFileName}.docx`;

    // 保存文档
    try {
      newDoc.SaveAs2(fullPath);
      console.log('文档已保存到临时目录:', fullPath);
    } catch (saveError) {
      console.error('保存文档到临时目录失败:', saveError);
      // 如果保存失败，尝试使用另一种方法
      try {
        newDoc.SaveAs(fullPath);
        console.log('使用备选方法保存文档到临时目录:', fullPath);
      } catch (altSaveError) {
        console.error('备选保存方法也失败:', altSaveError);
        // 如果保存失败，至少文档已经创建并打开
      }
    }

    // 激活新文档
    newDoc.Activate();
    return fullPath;
  } catch (error) {
    console.error('创建临时文档并插入内容失败:', error);
    return '';
  }
}

/**
 * 读取当前打开的文档内容
 * @returns 文档内容字符串，如果没有打开的文档则返回空字符串
 */
export function readCurrentDocumentContent(): string {
  try {
    // 获取当前活动文档
    const doc = window.Application.ActiveDocument;
    if (!doc) {
      console.error('没有打开的文档');
      return '';
    }

    // 获取文档内容
    return doc.Content.Text;
  } catch (error) {
    console.error('读取文档内容失败:', error);
    return '';
  }
}

/**
 * 获取当前打开的文档信息
 * @returns 包含文档名称、内容和最后修改时间的对象，如果没有打开的文档则返回null
 */
export function getCurrentDocumentInfo(): { name: string; content: string; lastModified: number } | null {
  try {
    // 获取当前活动文档
    const doc = window.Application.ActiveDocument;
    if (!doc) {
      console.error('没有打开的文档');
      return null;
    }

    console.log('doc:', doc);

    // 获取文档名称
    const name = doc.Name || '未命名文档';

    // 获取文档内容
    const content = doc.Content.Text;

    // 获取当前时间作为最后修改时间
    const lastModified = new Date().getTime();

    return { name, content, lastModified };
  } catch (error) {
    console.error('获取文档信息失败:', error);
    return null;
  }
}

/**
 * 使用外部浏览器打开URL
 * @param url 要打开的URL地址
 * @returns 是否成功打开
 */
export function openUrlInExternalBrowser(url: string): boolean {
  try {
    // 使用WPS内置的ShellExecute方法打开外部浏览器
    // ShellExecute方法通常在WPS的OA助手或系统集成中提供
    if (window.Application.OAAssist && typeof window.Application.OAAssist.ShellExecute === 'function') {
      window.Application.OAAssist.ShellExecute(url);
      return true;
    }

    // 备选方案：使用ActiveDocument.FollowHyperlink方法
    const doc = window.Application.ActiveDocument;
    if (doc && typeof doc.FollowHyperlink === 'function') {
      doc.FollowHyperlink(url, true); // 第二个参数为true表示在新窗口中打开
      return true;
    }

    // 如果上述方法都不可用，尝试使用window.open
    window.open(url, '_blank');
    return true;
  } catch (error) {
    console.error('打开外部浏览器失败:', error);

    // 最后的尝试：使用window.open
    try {
      window.open(url, '_blank');
      return true;
    } catch (e) {
      console.error('使用window.open打开URL失败:', e);
      return false;
    }
  }
}

// 为了TypeScript类型定义，扩展Window接口
declare global {
  interface Window {
    Application: {
      ActiveDocument: any;
      Selection: any;
      Documents: {
        Add: () => any;
      };
      System?: {
        SpecialFolders: (folderType: number) => string;
        ComputerName?: string; // 计算机名称
        Username?: string;     // 用户名
        ProcessorType?: string; // 处理器类型
        ComputerType?: string;  // 计算机类型
        OperatingSystem?: string; // 操作系统
        PrivateProfileString?: (filePath: string, section: string, key: string) => string; // 获取配置文件信息
      };
      Env?: {
        GetRootPath: () => string;
        GetHomePath: () => string;
        GetAppDataPath: () => string;
        GetTempPath: () => string;
        GetDesktopDpi: () => number;
      };
      Options?: {
        DefaultFilePath: (pathType: number) => string;
      };
      OAAssist?: {
        ShellExecute?: (command: string) => void;
        WebNotify?: (message: string, showToast: boolean) => void;
      };
      PluginStorage: {
        getItem: (key: string) => string;
        setItem: (key: string, value: string | boolean) => void;
      };
      CreateTaskPane: (url: string) => any;
      GetTaskPane: (id: string) => any;
      ShowDialog: (url: string, title: string, width: number, height: number, modal: boolean) => void;
      ribbonUI: any;
      Enum: any;
      ApiEvent?: any;
    };
    openOfficeFileFromSystemDemo?: any;
    InvokeFromSystemDemo?: any;
  }
}

export default {
  insertContentToEnd,
  replaceSelectedContent,
  createTempDocWithContent,
  readCurrentDocumentContent,
  getCurrentDocumentInfo,
  openUrlInExternalBrowser
};

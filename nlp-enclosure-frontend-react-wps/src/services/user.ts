import http, {clearAuthToken, setAuthToken} from '../utils/request';
import {Account} from "../types/account.ts";
import { getMachineId } from '../utils/machineId';

export const wpsLogin = async () => {
    // 强制使用wps作为用户名密码登录
    const res = await userNameLogin({
        username: 'wps',
        password: 'wps',
    })

    console.log('Login successful:', res);
    const userInfo = await getUserInfo();
    return userInfo;
}

/**
 * 获取当前机器的唯一标识
 * @returns 机器唯一标识字符串
 */
export const getMachineUniqueId = (): string => {
    return getMachineId();
}

export const userNameLogin = async (form: any) => {
    try {
        // 获取机器唯一标识
        const machineId = getMachineId();
        console.log('Machine ID:', machineId);

        const response = await http.post('./enclosure/user/login', null, {
            params: {
                username: form.username,
                password: form.password,
                machineId: machineId // 添加机器ID作为登录参数
            }
        });

        // 如果登录成功且返回了token，存储token到localStorage
        if (response && response.token) {
            setAuthToken(response.token);
            console.log('Token stored successfully');
        }

        return response;
    } catch (error) {
        console.error('Login error:', error);
        throw error;
    }
}

export const getUserInfo = async ():Promise<Account> => {
    const userInfoFromLocalStorage = getUserInfoFromLocalStorage();
    if (userInfoFromLocalStorage) {
        return userInfoFromLocalStorage
    }

    const res = await http.get('./enclosure/dify/user/info')
    console.log(res)
    const userInfo = res.payload.principal.attributes;
    console.log(userInfo)
    if (userInfo) {
        setUserInfoToLocalStorage(userInfo)
    }
    return userInfo
}

export const setUserInfoToLocalStorage = (userInfo: Account) => {
    localStorage.setItem('userInfo', JSON.stringify(userInfo))
}

export const clearUserInfoFormLocalStorage = () => {
    localStorage.removeItem('userInfo')
    clearAuthToken();
}

export const getUserInfoFromLocalStorage = (): Account | null => {
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
        return JSON.parse(userInfo)
    }
    return null
}

export const logout = () => {
    clearUserInfoFormLocalStorage();
    http.post('./enclosure/logout').then(res => {
        if (res) {
            // 如果在 WPS 环境中，使用任务窗格打开登录页
            window.location.href = "#/login";
        }
    })
}

import http, {getAuthToken} from '../utils/request';
import {BlockResponse} from "../types/blockResponse.ts";
import {AxiosResponse} from "axios";
import {DifyFile, DifyRequestFile} from "../types/difyFile.ts";
import {AgentInfo} from "../types/agentParam.ts";
import {getApiBaseUrl} from "../utils/env.ts"; // 假设你有一个 types 文件来定义这些接口

const baseUrl = getApiBaseUrl();

// 跟踪所有活跃的 EventSource 连接
const activeEventSources: EventSource[] = [];

// 关闭所有活跃的 EventSource 连接
export const closeAllEventSources = () => {
    console.log(`关闭所有活跃的 EventSource 连接，数量: ${activeEventSources.length}`);
    while (activeEventSources.length > 0) {
        const eventSource = activeEventSources.pop();
        if (eventSource) {
            console.log('关闭 EventSource 连接');
            eventSource.close();
        }
    }
};

// 阻塞式调用
export const blockApi = async (query: string, key: string, conversationId: string): Promise<string> => {
    const response: AxiosResponse<BlockResponse> = await http.get('./enclosure/dify/block', {
        params: {query, key, conversationId},
    });
    return response.data.answer;
};

// 流式调用
export const streamApi = (
    query: string,
    agentId: string,
    conversationId?: string | undefined,
    fileList?: DifyFile[] | DifyRequestFile[] | undefined,
    inputs?: any,
): ReadableStream<any> => {
    // 判断最后一个字符是不是 / ，如果不是就添加一个/
    let burl = baseUrl;
    if (burl.slice(-1) !== '/') {
        burl += '/';
    }

    const token = getAuthToken();

    let url = `${burl}enclosure/dify/stream?query=${encodeURIComponent(query)}&key=${encodeURIComponent(agentId)}&token=${encodeURIComponent(token)}`;
    if (conversationId) {
        url += `&conversationId=${encodeURIComponent(conversationId)}`;
    }
    if (fileList && fileList.length > 0) {
        url += `&fileList=${encodeURIComponent(JSON.stringify(fileList))}`;
    }
    if (inputs && Object.keys(inputs).length > 0) {
        url += `&inputs=${encodeURIComponent(JSON.stringify(inputs))}`;
    }

    // 创建 EventSource 实例
    let eventSource: EventSource | null = null;

    return new ReadableStream({
        start(controller) {
            console.log("ReadableStream created", url)
            eventSource = new EventSource(url, {withCredentials: true});

            // 将新创建的 EventSource 添加到活跃连接列表中
            if (eventSource) {
                activeEventSources.push(eventSource);
                console.log(`添加 EventSource 连接，当前活跃连接数: ${activeEventSources.length}`);
            }

            eventSource.onmessage = (event) => {
                console.log('Received event:', event)
                // 读取data的内容，并转换json
                const data = JSON.parse(event.data);
                // 读取event属性，并把除了event之外的属性保存到新对象中
                const eventData = {
                    ...data,
                    event: data.event,
                };

                const text = `event:${data.event}\ndata: ${JSON.stringify(eventData)}\n\n`

                controller.enqueue(new TextEncoder().encode(text));
                // 判断是否包含 \"event\":\"message_end\"
                if (eventData.event === 'message_end') {
                    console.log("EventSource end")
                    controller.close();
                    if (eventSource) {
                        // 从活跃连接列表中移除
                        const index = activeEventSources.indexOf(eventSource);
                        if (index !== -1) {
                            activeEventSources.splice(index, 1);
                            console.log(`移除 EventSource 连接，当前活跃连接数: ${activeEventSources.length}`);
                        }
                        eventSource.close();
                        eventSource = null;
                    }
                }
            };

            eventSource.onerror = (error) => {
                console.error('EventSource failed:', error);
                controller.error(error);
                // 发生错误时也要关闭连接并从列表中移除
                if (eventSource) {
                    const index = activeEventSources.indexOf(eventSource);
                    if (index !== -1) {
                        activeEventSources.splice(index, 1);
                        console.log(`错误移除 EventSource 连接，当前活跃连接数: ${activeEventSources.length}`);
                    }
                    eventSource.close();
                    eventSource = null;
                }
            };

            eventSource.onopen = (event) => {
                console.log('Connection to server opened.', event);
            };
        },

        cancel() {
            // 取消时关闭 EventSource
            console.log("ReadableStream canceled");
            if (eventSource) {
                // 从活跃连接列表中移除
                const index = activeEventSources.indexOf(eventSource);
                if (index !== -1) {
                    activeEventSources.splice(index, 1);
                    console.log(`取消移除 EventSource 连接，当前活跃连接数: ${activeEventSources.length}`);
                }
                eventSource.close();
                eventSource = null;
            }
        },

    });

};

// 文件上传
export const upload = async (file: File, agentId: string): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);

    try {
        const response: AxiosResponse<any> = await http.post('./enclosure/dify/upload?agentId=' + agentId, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });

        if (response.status.code !== '0') {
            return '';
        }

        return response.payload;
    } catch (error) {
        console.error('Upload error:', error);
    }
};

// 获取建议
export const suggested = async (messageId: string, key: string): Promise<any> => {
    try {
        const response: AxiosResponse<any> = await http.get('./enclosure/dify/suggested', {
            params: {messageId, key},
        });

        if (response.status.code !== '0') {
            return '';
        }

        return response.payload;
    } catch (error) {
        console.error('Suggested error:', error);
    }
};

// 停止任务
export const stop = async (key: string, taskId: string): Promise<number> => {
    const response: AxiosResponse<number> = await http.get('./enclosure/dify/stop', {
        params: {key, taskId},
    });

    if (response.status.code !== '0') {
        return '';
    }

    return response.payload;
};

// 获取会话
export const conversations = async (key: string): Promise<any> => {
    const response: AxiosResponse<any> = await http.get('./enclosure/dify/conversations', {
        params: {key},
    });

    if (response.status.code !== '0') {
        return null;
    }

    return response.payload;
};

// 获取消息
export const message = async (key: string, conversationId: string): Promise<any> => {
    const response: AxiosResponse<any> = await http.get('./enclosure/dify/message', {
        params: {
            key,
            conversationId,
        },
    });

    console.log("message response", response)

    return response;
};

// 获取参数
export const parameters = async (key: string): Promise<any> => {
    const response: AxiosResponse<any> = await http.get('./enclosure/dify/parameters', {
        params: {key},
    });

    if (response.status.code !== '0') {
        return null;
    }

    return response.payload;
};

export const deleteConversation = async (key: string, conversationId: string): Promise<any> => {
    try {
        const response: AxiosResponse<string> = await http.get(`./enclosure/dify/deleteConversation`, {
            params: { key, conversationId },
        });

        if (response.result !== 'success') {
            return null;
        }

        return response.result;
    } catch (error) {
        console.error('Delete conversation error:', error);
        throw error;
    }
};

export const renameConversation = async (agentId: string, conversationId: string, newName: string): Promise<any> => {
    const data = {
        agentId,
        conversationId,
        newName,
    };

    try {
        const response: AxiosResponse<any> = await http.post(`./enclosure/dify/renameConversation`, data);

        if (response.status.code !== '0') {
            return null;
        }

        return response.payload;
    } catch (error) {
        console.error('Rename conversation error:', error);
        throw error;
    }
};

export const getInfo = async (host: string, apiKey: string): Promise<AgentInfo> => {
    try {
        const response: AxiosResponse<AgentInfo> = await http.get(`./enclosure/dify/info`, {
            params: {
                host,
                apiKey
            },
        });

        if (response.status.code !== '0') {
            return null;
        }

        return response.payload;
    } catch (error) {
        console.error('Get info error:', error);
        throw error;
    }
}

export const feedback = async (agentId: string, messageId: string, rating?: string) => {
    try {
        const response: AxiosResponse<any> = await http.get(`./enclosure/dify/feedback`, {
            params: {
                agentId,
                messageId,
                rating
            },
        });

        if (response.status.code !== '0') {
            return null;
        }

        return response.payload;
    } catch (error) {
        console.error('Feedback error:', error);
        throw error;
    }
}

// 上传WPS文档文件
export const wpsDocumentUpload = async (fileName: string, fileContent: string, agentId: string): Promise<any> => {
    try {
        // 创建文件对象
        const blob = new Blob([fileContent], { type: 'application/octet-stream' });
        const file = new File([blob], fileName, { type: 'application/octet-stream' });

        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);

        // 发送请求
        const response: AxiosResponse<any> = await http.post('./enclosure/dify/wpsDocumentUpload?agentId=' + agentId, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });

      if (response.code !== 200) {
        return '';
      }

        let data = JSON.parse(response.data)

        data['file'] = file;

        return data;
    } catch (error) {
        console.error('WPS文档上传失败:', error);
        throw error;
    }
}

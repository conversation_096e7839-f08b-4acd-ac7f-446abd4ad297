import http from '../utils/request';
import {ChatHistory} from "../types/chatHistory.ts";

// 获取聊天历史列表
export const getChatHistoryList = async (conversationId?: string, agentId?: string): Promise<ChatHistory[]> => {
    try {
        const response = await http.get(`./enclosure/chatHistory/list`, {
            params: { conversationId, agentId }
        });
        console.log("getChatHistoryList response", response)
        if (response.status.code !== '0') {
            return [];
        }

        return response.payload;
    } catch (error) {
        console.error('Error fetching chat history list:', error);
        throw error;
    }
};

// 删除聊天历史
export const deleteChatHistory = async (id: string) => {
    try {
        await http.get(`./enclosure/chatHistory/delete`, {
            params: { id }
        });
        return true;
    } catch (error) {
        console.error('Error deleting chat history:', error);
        throw error;
    }
};

// 重命名聊天历史
export const renameChatHistory = async (id: string, name: string) => {
    try {
        await http.get(`./enclosure/chatHistory/rename`, {
            params: { id, name }
        });
        return true;
    } catch (error) {
        console.error('Error renaming chat history:', error);
        throw error;
    }
};

// 置顶聊天历史
export const topChatHistory = async (id: string, isTop: boolean, newName?: string) => {
    try {
        await http.get(`./enclosure/chatHistory/top`, {
            params: { id, isTop, newName }
        });
        return true;
    } catch (error) {
        console.error('Error topping chat history:', error);
        throw error;
    }
};

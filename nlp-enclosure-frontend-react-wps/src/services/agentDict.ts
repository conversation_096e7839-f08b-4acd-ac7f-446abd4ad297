import http from "../utils/request.ts";
import {MobileAgentParam} from "../types/agentDict.ts";

export const findAllAgent = (): Promise<any> => {
    return http.get('./enclosure/agentDict/findAll');
};

export const getAgentDictByIsDefaultTrue = (): Promise<any> => {
    return http.get('./enclosure/agentDict/getAgentDictByIsDefaultTrue');
};

export const findByIsDefaultFalse = (): Promise<any> => {
    return http.get('./enclosure/agentDict/findByIsDefaultFalse');
};

export const save = (agentDict: any): Promise<any> => {
    return http.post('./enclosure/agentDict/save', agentDict);
};

export const deleteAgentDict = (id: string): Promise<any> => {
    return http.get('./enclosure/agentDict/delete', { params: { id }});
};

export const getMobileAgentParam = async (): Promise<MobileAgentParam> => {
    try {
        const response = await http.get('./enclosure/agentDict/getMobileAgentParam');

        if (response.status.code !== '0') {
            return null;
        }

        return response.payload;
    } catch (error) {
        console.error('Get mobile agent param error:', error);
    }

}



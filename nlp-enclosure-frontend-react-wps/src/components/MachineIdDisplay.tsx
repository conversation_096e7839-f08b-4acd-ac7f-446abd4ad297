import React, { useState, useEffect } from 'react';
import { Button, Card, Typography, Space, message } from 'antd';
import { getMachineUniqueId } from '../services/user';
import { resetMachineId } from '../utils/machineId';

const { Title, Paragraph, Text } = Typography;

/**
 * 机器唯一标识展示组件
 * 用于展示当前机器的唯一标识，并提供重置功能
 */
const MachineIdDisplay: React.FC = () => {
  const [machineId, setMachineId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  // 获取机器ID
  const fetchMachineId = () => {
    setLoading(true);
    try {
      const id = getMachineUniqueId();
      setMachineId(id);
    } catch (error) {
      console.error('获取机器ID失败:', error);
      message.error('获取机器ID失败');
    } finally {
      setLoading(false);
    }
  };

  // 重置机器ID
  const handleReset = () => {
    try {
      resetMachineId();
      message.success('机器ID已重置');
      fetchMachineId(); // 重新获取
    } catch (error) {
      console.error('重置机器ID失败:', error);
      message.error('重置机器ID失败');
    }
  };

  // 复制到剪贴板
  const handleCopy = () => {
    navigator.clipboard.writeText(machineId)
      .then(() => {
        message.success('已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制失败');
      });
  };

  // 组件挂载时获取机器ID
  useEffect(() => {
    fetchMachineId();
  }, []);

  return (
    <Card title="机器唯一标识" style={{ width: '100%', maxWidth: 600, margin: '0 auto' }}>
      <Typography>
        <Title level={4}>当前机器ID</Title>
        <Paragraph>
          <Text code copyable style={{ wordBreak: 'break-all' }}>
            {machineId || '加载中...'}
          </Text>
        </Paragraph>
        <Paragraph>
          <Text type="secondary">
            此ID用于唯一标识当前机器，用于登录验证和设备管理。
          </Text>
        </Paragraph>
      </Typography>
      
      <Space>
        <Button type="primary" onClick={fetchMachineId} loading={loading}>
          刷新
        </Button>
        <Button onClick={handleCopy}>
          复制
        </Button>
        <Button danger onClick={handleReset}>
          重置ID
        </Button>
      </Space>
    </Card>
  );
};

export default MachineIdDisplay;

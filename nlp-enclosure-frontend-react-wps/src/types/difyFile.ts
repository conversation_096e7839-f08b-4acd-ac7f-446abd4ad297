export interface DifyFile {

    /**
     * 支持类型
     * document 具体类型包含：'TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'
     * image 具体类型包含：'JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'
     * audio 具体类型包含：'MP3', 'M4A', 'WAV', 'WEBM', 'AMR'
     * video 具体类型包含：'MP4', 'MOV', 'MPEG', 'MPGA'
     * custom 具体类型包含：其他文件类型
     */
    type: string;

    /**
     * 传递方式
     * remote_url: 图片地址。
     * local_file: 上传文件。
     */
    transfer_method: string;

    /**
     * 图片地址。（仅当传递方式为 remote_url 时）。
     */
    url?: string;

    /**
     * 上传文件 ID。（仅当传递方式为 local_file 时）。
     */
    upload_file_id?: string;

    file: File;
}

export interface DifyRequestFile {
    /**
     * ID
     */
    id: string;

    /**
     * 文件类型，例如 image 图片
     */
    type: string;

    /**
     * 预览图片地址
     */
    url: string;

    /**
     * 文件归属方，例如 user 或 assistant
     */
    belongs_to: string;
}

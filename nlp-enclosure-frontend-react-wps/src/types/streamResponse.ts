import {DifyFile, DifyRequestFile} from "./difyFile.ts";

export interface StreamResponse {
    /**
     * 不同模式下的事件类型.
     */
    event: string;

    /**
     * agent_thought id.
     */
    id: string;

    /**
     * 任务ID.
     */
    task_id: string;

    /**
     * 消息唯一ID.
     */
    message_id: string;

    /**
     * LLM 返回文本块内容.
     */
    answer: string;

    /**
     * 创建时间戳.
     */
    created_at: number;

    /**
     * 会话 ID.
     */
    conversation_id: string;

    metadata: any; // 根据实际情况调整类型，这里假设为 any
}

export interface AgentMessage {
    id?: string;
    type: string;
    content: string;
    suggested: boolean;
    conversationId?: string;
    taskId?: string;
    messageId?: string;
    agentId?: string;
    list?: [];
    retrieverResources?: [];
    fileList?: DifyFile[] | DifyRequestFile[] | undefined;
    inputs?: any;
    feedback?: any;
}

export interface RetrieverResource {
    position: number;
    dataset_id: string;
    dataset_name: string;
    document_id: string;
    document_name: string;
    data_source_type: string;
    segment_id: string;
    retriever_from: string;
    score: number;
    hit_count: number;
    word_count: number;
    segment_position: number;
    index_node_hash: string;
    content: string;
    page: null | number;
}

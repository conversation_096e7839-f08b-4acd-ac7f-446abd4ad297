import { red } from '@mui/material/colors';
import { createTheme } from '@mui/material/styles';

import { ThemeConfig } from 'antd';

// A custom theme for this app
export const theme = createTheme({
  palette: {
    primary: {
      main: '#556cd6'
    },
    secondary: {
      main: '#19857b'
    },
    error: {
      main: red.A400
    }
  }
});

export const buttonTheme: ThemeConfig = {
  components: {
    Button: {
      defaultColor: 'rgba(0, 0, 0, 0.5)',
      defaultHoverBg: 'rgba(0, 0, 0, 0.01)',
      defaultHoverColor: 'rgba(0, 0, 0, 0.85)',
      defaultHoverBorderColor: 'rgb(217, 217, 217)',
    },
  },
};
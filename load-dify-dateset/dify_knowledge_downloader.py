#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Dify知识库文档下载工具

该脚本用于从Dify平台获取知识库列表、文档列表，并下载所有文档。
"""

import os
import json
import time
import requests
import logging
from urllib.parse import urljoin
from pathlib import Path
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('dify_downloader.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('dify_downloader')

class DifyKnowledgeDownloader:
    """Dify知识库文档下载器"""
    
    def __init__(self, base_url, api_key, output_dir='./downloads'):
        """
        初始化下载器
        
        Args:
            base_url: Dify API的基础URL
            api_key: Dify API的访问密钥
            output_dir: 文档下载保存目录
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.output_dir = Path(output_dir)
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        # 创建下载目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def get_datasets(self):
        """
        获取所有知识库列表
        
        Returns:
            list: 知识库信息列表
        """
        logger.info("开始获取知识库列表...")
        datasets = []
        page = 1
        limit = 20
        
        while True:
            url = f"{self.base_url}/v1/datasets?page={page}&limit={limit}&include_all=true"
            try:
                response = requests.get(url, headers=self.headers)
                response.raise_for_status()
                data = response.json()
                
                if not data.get('data'):
                    break
                    
                datasets.extend(data['data'])
                logger.info(f"已获取第{page}页知识库，共{len(data['data'])}个")
                
                if not data.get('has_more', False):
                    break
                    
                page += 1
                
            except requests.exceptions.RequestException as e:
                logger.error(f"获取知识库列表失败: {e}")
                break
                
        logger.info(f"知识库列表获取完成，共{len(datasets)}个知识库")
        return datasets
    
    def get_documents(self, dataset_id):
        """
        获取指定知识库的所有文档
        
        Args:
            dataset_id: 知识库ID
            
        Returns:
            list: 文档信息列表
        """
        logger.info(f"开始获取知识库 {dataset_id} 的文档列表...")
        documents = []
        page = 1
        limit = 100
        
        while True:
            url = f"{self.base_url}/v1/datasets/{dataset_id}/documents?page={page}&limit={limit}"
            try:
                response = requests.get(url, headers=self.headers)
                response.raise_for_status()
                data = response.json()
                
                if not data.get('data'):
                    break
                    
                documents.extend(data['data'])
                logger.info(f"已获取知识库 {dataset_id} 第{page}页文档，共{len(data['data'])}个")
                
                if not data.get('has_more', False):
                    break
                    
                page += 1
                
            except requests.exceptions.RequestException as e:
                logger.error(f"获取知识库 {dataset_id} 的文档列表失败: {e}")
                break
                
        logger.info(f"知识库 {dataset_id} 的文档列表获取完成，共{len(documents)}个文档")
        return documents
    
    def get_document_download_url(self, dataset_id, document_id):
        """
        获取文档的下载URL
        
        Args:
            dataset_id: 知识库ID
            document_id: 文档ID
            
        Returns:
            dict: 包含下载URL的文档信息
        """
        url = f"{self.base_url}/v1/datasets/{dataset_id}/documents/{document_id}/upload-file"
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"获取文档 {document_id} 的下载URL失败: {e}")
            return None
    
    def download_document(self, download_url, file_name, dataset_name):
        """
        下载文档并保存到本地
        
        Args:
            download_url: 文档下载URL
            file_name: 文件名
            dataset_name: 知识库名称
            
        Returns:
            bool: 下载是否成功
        """
        # 创建知识库目录
        dataset_dir = self.output_dir / dataset_name
        dataset_dir.mkdir(exist_ok=True)
        
        # 构建保存路径
        file_path = dataset_dir / file_name
        
        # 下载文件
        try:
            response = requests.get(download_url, stream=True)
            response.raise_for_status()
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
                    
            logger.info(f"文档 {file_name} 下载成功，保存至 {file_path}")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"下载文档 {file_name} 失败: {e}")
            return False
        except IOError as e:
            logger.error(f"保存文档 {file_name} 失败: {e}")
            return False
    
    def run(self):
        """执行完整的下载流程"""
        # 获取所有知识库
        datasets = self.get_datasets()
        
        # 记录统计信息
        total_documents = 0
        downloaded_documents = 0
        
        # 遍历知识库
        for dataset in datasets:
            dataset_id = dataset['id']
            dataset_name = dataset['name']
            logger.info(f"处理知识库: {dataset_name} (ID: {dataset_id})")
            
            # 获取知识库的所有文档
            documents = self.get_documents(dataset_id)
            total_documents += len(documents)
            
            # 遍历文档
            for document in documents:
                document_id = document['id']
                document_name = document['name']
                
                # 获取文档下载信息
                file_info = self.get_document_download_url(dataset_id, document_id)
                
                if not file_info or 'download_url' not in file_info:
                    logger.warning(f"无法获取文档 {document_name} (ID: {document_id}) 的下载URL")
                    continue
                
                # 构建文件名
                file_name = file_info.get('name', f"{document_name}.{file_info.get('extension', 'txt')}")

                download_url = self.base_url + file_info['download_url']

                # 下载文档
                if self.download_document(download_url, file_name, dataset_name):
                    downloaded_documents += 1
                
                # 添加延迟，避免请求过于频繁
                time.sleep(0.5)
        
        logger.info(f"下载完成! 共处理 {len(datasets)} 个知识库，{total_documents} 个文档，成功下载 {downloaded_documents} 个文档")
        return {
            'datasets_count': len(datasets),
            'total_documents': total_documents,
            'downloaded_documents': downloaded_documents
        }

def main():
    """主函数"""

    base_url = ""
    api_key = ""
    output_dir = ""

    downloader = DifyKnowledgeDownloader(
        base_url=base_url,
        api_key=api_key,
        output_dir=output_dir
    )
    
    result = downloader.run()
    print(f"\n下载统计:")
    print(f"- 知识库数量: {result['datasets_count']}")
    print(f"- 文档总数: {result['total_documents']}")
    print(f"- 成功下载: {result['downloaded_documents']}")

if __name__ == "__main__":
    main()

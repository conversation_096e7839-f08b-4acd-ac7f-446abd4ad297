FROM m.daocloud.io/docker.io/nvidia/cuda:12.1.1-cudnn8-runtime-ubuntu22.04

ARG VENV_NAME="chatvoice"
ENV VENV=$VENV_NAME
ENV LANG=C.UTF-8 LC_ALL=C.UTF-8

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    curl \
    wget \
    ffmpeg \
    libopus-dev \
    libopusfile-dev \
    opus-tools \
    unzip \
    sox \
    libsox-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装 conda
RUN wget --quiet https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh -O ~/miniconda.sh && \
    /bin/bash ~/miniforge.sh -b -p /opt/conda && \
    rm ~/miniforge.sh && \
    ln -s /opt/conda/etc/profile.d/conda.sh /etc/profile.d/conda.sh && \
    echo "source /opt/conda/etc/profile.d/conda.sh" >> /opt/nvidia/entrypoint.d/100.conda.sh && \
    echo "source /opt/conda/etc/profile.d/conda.sh" >> ~/.bashrc && \
    echo "conda activate ${VENV}" >> /opt/nvidia/entrypoint.d/110.conda_default_env.sh && \
    echo "conda activate ${VENV}" >> $HOME/.bashrc

ENV PATH /opt/conda/bin:$PATH

# 配置conda使用国内镜像源
RUN conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/ && \
    conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/ && \
    conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/ && \
    conda config --add channels conda-forge && \
    conda config --set show_channel_urls yes && \
    conda config --set channel_priority strict

# 执行 conda 安装
RUN conda create -y -n ${VENV} python=3.11
ENV CONDA_DEFAULT_ENV=${VENV}
ENV PATH /opt/conda/bin:/opt/conda/envs/${VENV}/bin:$PATH

# 复制依赖文件
COPY requirements.txt .

RUN conda activate ${VENV} && conda install -y -c conda-forge pynini==2.1.5

# 安装Python依赖
RUN conda activate ${VENV} && pip install --no-cache-dir -r requirements.txt  -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host=mirrors.aliyun.com

# 复制应用程序代码
COPY app.py .
COPY templates/ templates/
COPY third_party/ third_party/
COPY PyOgg-0.6.14a1.tar.gz .
COPY CosyVoice2-0.5B.zip .
COPY SenseVoiceSmall.zip .

# 创建必要的目录
RUN mkdir -p models/CosyVoice2-0.5B models/SenseVoiceSmall output static

# 设置环境变量
ENV PYTHONPATH=/app:$PYTHONPATH
ENV CUDA_VISIBLE_DEVICES=0

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/ || exit 1

# 启动命令 - 使用bash进入交互式shell而不是直接启动应用
CMD ["/bin/bash"]

# 注意：
# 1. 用户需要自行下载模型文件并挂载到容器的以下目录：
#    - /app/models/CosyVoice2-0.5B
#    - /app/models/SenseVoiceSmall
# 2. 示例运行命令（CPU）进入交互式shell：
#    docker run -it -p 8000:8000 \
#      -v /path/to/models/CosyVoice2-0.5B:/app/models/CosyVoice2-0.5B \
#      -v /path/to/models/SenseVoiceSmall:/app/models/SenseVoiceSmall \
#      chatvoice
# 3. 示例运行命令（GPU）进入交互式shell：
#    docker run -it --gpus all -p 8000:8000 \
#      -v /path/to/models/CosyVoice2-0.5B:/app/models/CosyVoice2-0.5B \
#      -v /path/to/models/SenseVoiceSmall:/app/models/SenseVoiceSmall \
#      chatvoice
#
# 4. 进入容器后，可以手动安装所需的Python包：
#    pip install <package_name>
#
# 5. 然后可以手动启动应用：
#    uvicorn app:app --host 0.0.0.0 --port 8000 --workers 1
#
# 6. 本应用支持Opus音频格式，提供更高效的音频传输和更好的音质

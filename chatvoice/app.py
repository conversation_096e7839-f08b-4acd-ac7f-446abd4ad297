#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
语音转换应用程序后端
提供两个主要功能：
1. 语音转文字（Speech-to-Text）
2. 文字转语音（Text-to-Speech）- 流式输出
"""

import os
import sys
import asyncio
import base64
import json
import logging
import time
import numpy as np
import torch
import torchaudio
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request, File, UploadFile, Form, Body
from fastapi.responses import HTMLResponse, StreamingResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import io
import wave
import tempfile
from pathlib import Path

# 定义语音转文字请求模型
class STTRequest(BaseModel):
    sample_rate: int = 16000  # 默认采样率为16000Hz
import pyogg
import subprocess

# 配置日志
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加必要的路径
sys.path.append('third_party/CosyVoice/third_party/Matcha-TTS')
sys.path.append('third_party/CosyVoice')

# 导入CosyVoice相关模块
try:
    from cosyvoice.cli.cosyvoice import CosyVoice2
    from cosyvoice.utils.file_utils import load_wav
    logger.info("成功导入CosyVoice模块")
except ImportError as e:
    logger.error(f"导入CosyVoice模块时出错: {e}")
    logger.error("请确保已正确安装CosyVoice及其依赖")
    sys.exit(1)

# 导入语音识别模块
try:
    from funasr import AutoModel
    logger.info("成功导入FunASR模块")
except ImportError as e:
    logger.error(f"导入FunASR模块时出错: {e}")
    logger.warning("语音转文字功能将不可用")

# 定义lifespan上下文管理器
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    logger.info("正在启动应用...")
    load_models()
    yield
    # 关闭时执行
    logger.info("应用正在关闭...")

# 创建FastAPI应用
app = FastAPI(
    title="语音转换应用",
    description="提供语音转文字和文字转语音功能",
    lifespan=lifespan
)

# 创建模板目录
templates = Jinja2Templates(directory="templates")

# 创建静态文件目录
os.makedirs("static", exist_ok=True)
app.mount("/static", StaticFiles(directory="static"), name="static")

# 模型路径
COSYVOICE2_MODEL_PATH = "models/CosyVoice2-0.5B"
SENSEVOICE_MODEL_PATH = "models/SenseVoiceSmall"

# 全局变量，用于存储模型实例
cosyvoice_model = None
asr_model = None

# 连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.last_heartbeat: Dict[WebSocket, float] = {}  # 存储每个连接的最后心跳时间

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        # 记录初始心跳时间
        import time
        self.last_heartbeat[websocket] = time.time()

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        # 移除心跳记录
        if websocket in self.last_heartbeat:
            del self.last_heartbeat[websocket]

    async def send_text(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def send_bytes(self, data: bytes, websocket: WebSocket):
        await websocket.send_bytes(data)

    async def send_json(self, data: Dict[str, Any], websocket: WebSocket):
        await websocket.send_json(data)

    async def send_heartbeat(self, websocket: WebSocket):
        """发送心跳消息"""
        try:
            await self.send_json({"type": "heartbeat", "timestamp": time.time()}, websocket)
            logger.debug(f"已发送心跳到 {websocket.client.host}")
        except Exception as e:
            logger.error(f"发送心跳时出错: {e}")

    def update_heartbeat(self, websocket: WebSocket):
        """更新最后心跳时间"""
        import time
        self.last_heartbeat[websocket] = time.time()

    def check_connection_timeout(self, websocket: WebSocket, timeout_seconds: int = 30) -> bool:
        """检查连接是否超时"""
        import time
        if websocket not in self.last_heartbeat:
            return True

        current_time = time.time()
        last_time = self.last_heartbeat[websocket]
        return (current_time - last_time) > timeout_seconds

manager = ConnectionManager()

# 加载模型
def load_models():
    global cosyvoice_model, asr_model

    # 加载CosyVoice2模型
    if os.path.exists(COSYVOICE2_MODEL_PATH):
        try:
            logger.info(f"正在加载CosyVoice2模型，路径: {COSYVOICE2_MODEL_PATH}")
            cosyvoice_model = CosyVoice2(COSYVOICE2_MODEL_PATH, load_jit=False, load_trt=False, fp16=False, use_flow_cache=True)
            logger.info(f"CosyVoice2模型加载成功，采样率: {cosyvoice_model.sample_rate}")
        except Exception as e:
            logger.error(f"加载CosyVoice2模型时出错: {e}")
            import traceback
            traceback.print_exc()
    else:
        logger.warning(f"CosyVoice2模型路径不存在: {COSYVOICE2_MODEL_PATH}")

    # 加载语音识别模型
    if os.path.exists(SENSEVOICE_MODEL_PATH):
        try:
            logger.info(f"正在加载SenseVoice模型，路径: {SENSEVOICE_MODEL_PATH}")
            device = "cuda:0" if torch.cuda.is_available() else "cpu"
            logger.info(f"使用设备: {device}")

            asr_model = AutoModel(
                model=SENSEVOICE_MODEL_PATH,
                trust_remote_code=True,
                vad_model="fsmn-vad",
                vad_kwargs={"max_single_segment_time": 30000},
                device=device,
                disable_update=True,
            )
            logger.info("SenseVoice模型加载成功")
        except Exception as e:
            logger.error(f"加载SenseVoice模型时出错: {e}")
            import traceback
            traceback.print_exc()
    else:
        logger.warning(f"SenseVoice模型路径不存在: {SENSEVOICE_MODEL_PATH}")

# 将音频数据编码为Opus格式
def encode_audio_to_opus(audio_numpy, sample_rate):
    """
    将音频数据编码为Opus格式，如果Opus编码不可用则回退到WAV格式

    Args:
        audio_numpy: 音频数据（numpy数组）
        sample_rate: 采样率

    Returns:
        Opus编码的音频数据（字节）或WAV格式的音频数据（字节）
    """
    # 将浮点音频数据转换为16位整数
    audio_int16 = (audio_numpy * 32767).astype(np.int16)

    # 检查PyOgg的Opus编码器是否可用
    if not hasattr(pyogg, 'PYOGG_OPUS_ENC_AVAIL') or not pyogg.PYOGG_OPUS_ENC_AVAIL:
        logger.warning("PyOgg的Opus编码器不可用，直接使用WAV格式")
        # 直接返回WAV格式
        with io.BytesIO() as buffer:
            with wave.open(buffer, 'wb') as wf:
                wf.setnchannels(1)  # 单声道
                wf.setsampwidth(2)  # 16-bit
                wf.setframerate(sample_rate)
                wf.writeframes(audio_int16.tobytes())
            wav_data = buffer.getvalue()
        return wav_data

    # 创建临时WAV文件
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_wav_file:
        temp_wav_path = temp_wav_file.name

        # 写入WAV文件
        with wave.open(temp_wav_path, 'wb') as wf:
            wf.setnchannels(1)  # 单声道
            wf.setsampwidth(2)  # 16-bit
            wf.setframerate(sample_rate)
            wf.writeframes(audio_int16.tobytes())

    try:
        # 创建临时Opus文件
        with tempfile.NamedTemporaryFile(suffix=".opus", delete=False) as temp_opus_file:
            temp_opus_path = temp_opus_file.name

        # 使用PyOgg的OggOpusWriter将WAV转换为Opus
        try:
            # 尝试使用PyOgg的OggOpusWriter
            if hasattr(pyogg, 'OggOpusWriter'):
                writer = pyogg.OggOpusWriter(temp_opus_path, sample_rate, 1)
                writer.write(audio_int16.tobytes())
                writer.close()

                # 读取Opus文件
                with open(temp_opus_path, 'rb') as f:
                    opus_data = f.read()

                return opus_data
            else:
                raise AttributeError("PyOgg模块没有OggOpusWriter属性")
        except Exception as e:
            logger.error(f"使用PyOgg编码Opus时出错: {e}")

            # 如果PyOgg失败，回退到使用WAV格式
            with open(temp_wav_path, 'rb') as f:
                wav_data = f.read()

            return wav_data
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_wav_path)
            if 'temp_opus_path' in locals():
                os.unlink(temp_opus_path)
        except Exception as e:
            logger.warning(f"清理临时文件时出错: {e}")

# 查找提示音频
def find_prompt_audio():
    """查找示例音频文件作为提示音频"""
    prompt_path = "third_party/CosyVoice/asset/zero_shot_prompt.wav"
    if os.path.exists(prompt_path):
        logger.info(f"找到提示音频: {prompt_path}")
        try:
            prompt_speech_16k = load_wav(prompt_path, 16000)
            logger.info(f"提示音频加载成功，形状: {prompt_speech_16k.shape}")
            return prompt_speech_16k
        except Exception as e:
            logger.error(f"加载提示音频时出错: {e}")

    # 如果未找到或加载失败，创建一个简单的正弦波
    logger.info("创建正弦波作为提示音频...")
    sr = 16000
    t = np.linspace(0, 2, 2*sr)
    prompt_speech = 0.5*np.sin(2*np.pi*440*t)
    prompt_speech_16k = torch.FloatTensor(prompt_speech).unsqueeze(0)
    logger.info(f"正弦波创建完成，形状: {prompt_speech_16k.shape}")
    return prompt_speech_16k

# 文字转语音（流式）
async def text_to_speech_stream(text, prompt_speech_16k=None):
    """
    将文本转换为语音，并以流的形式返回

    Args:
        text: 要转换的文本
        prompt_speech_16k: 提示音频

    Yields:
        音频数据块（Opus格式）
    """
    if cosyvoice_model is None:
        logger.error("CosyVoice2模型未加载")
        return

    # 检查文本是否为空
    if not text or text.strip() == "":
        logger.warning("接收到空文本，无法进行文字转语音")
        return

    if prompt_speech_16k is None:
        prompt_speech_16k = find_prompt_audio()

    prompt_text = "希望你以后能够做的比我还好呦。"
    logger.info(f"开始文字转语音，文本: {text}")

    try:
        for i, result in enumerate(cosyvoice_model.inference_zero_shot(text, prompt_text, prompt_speech_16k, stream=True)):
            # 将音频张量转换为numpy数组
            audio_tensor = result['tts_speech']
            audio_numpy = audio_tensor.numpy()

            # 将音频数据转换为Opus格式
            try:
                audio_bytes = encode_audio_to_opus(audio_numpy, cosyvoice_model.sample_rate)
                logger.info(f"生成Opus音频片段 {i+1}，长度: {len(audio_bytes)} 字节")
                yield audio_bytes
            except Exception as e:
                logger.error(f"转换为Opus格式时出错: {e}")
                # 如果Opus编码失败，回退到WAV格式
                logger.warning("回退到WAV格式")
                with io.BytesIO() as buffer:
                    with wave.open(buffer, 'wb') as wf:
                        wf.setnchannels(1)
                        wf.setsampwidth(2)  # 16-bit
                        wf.setframerate(cosyvoice_model.sample_rate)
                        wf.writeframes((audio_numpy * 32767).astype(np.int16).tobytes())

                    audio_bytes = buffer.getvalue()
                    logger.info(f"生成WAV音频片段 {i+1}，长度: {len(audio_bytes)} 字节")
                    yield audio_bytes
    except Exception as e:
        logger.error(f"文字转语音时出错: {e}")
        import traceback
        traceback.print_exc()

# 语音转文字
# PCM数据转WAV
def pcm_to_wav(pcm_data, sample_rate=16000, channels=1, sample_width=2):
    """
    将PCM数据转换为WAV格式

    Args:
        pcm_data: PCM音频数据
        sample_rate: 采样率
        channels: 声道数
        sample_width: 采样宽度（字节）

    Returns:
        WAV格式的音频数据
    """
    with io.BytesIO() as wav_buffer:
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(channels)
            wav_file.setsampwidth(sample_width)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(pcm_data)
        return wav_buffer.getvalue()

async def speech_to_text(audio_data, sample_rate=16000):
    """
    将音频数据转换为文本

    Args:
        audio_data: 音频数据（字节）
        sample_rate: 采样率

    Returns:
        识别的文本
    """
    if asr_model is None:
        logger.error("SenseVoice模型未加载")
        return "语音识别模型未加载"

    try:
        # 创建临时WAV文件
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_wav_file:
            temp_wav_path = temp_wav_file.name

            # 检查数据是否已经是WAV格式
            if len(audio_data) > 12 and audio_data[:4] == b'RIFF' and audio_data[8:12] == b'WAVE':
                logger.info("接收到的数据已经是WAV格式")
                temp_wav_file.write(audio_data)
            else:
                logger.info("接收到的数据不是WAV格式，假设是PCM格式并转换")
                # 将PCM数据转换为WAV格式
                try:
                    wav_data = pcm_to_wav(audio_data, sample_rate=sample_rate)
                    temp_wav_file.write(wav_data)
                    logger.info(f"PCM转WAV成功，WAV大小: {len(wav_data)} 字节")
                except Exception as e:
                    logger.error(f"PCM转WAV失败: {e}")
                    # 尝试使用FFmpeg转换
                    try:
                        # 保存原始数据到临时文件
                        with tempfile.NamedTemporaryFile(suffix=".pcm", delete=False) as temp_pcm_file:
                            temp_pcm_path = temp_pcm_file.name
                            temp_pcm_file.write(audio_data)

                        # 使用FFmpeg转换
                        cmd = [
                            "ffmpeg",
                            "-y",  # 覆盖输出文件
                            "-f", "s16le",  # 16位小端序PCM数据
                            "-ar", str(sample_rate),
                            "-ac", "1",  # 单声道
                            "-i", temp_pcm_path,
                            temp_wav_path
                        ]

                        process = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                        # 删除临时PCM文件
                        os.unlink(temp_pcm_path)

                        if process.returncode != 0:
                            logger.error(f"FFmpeg转换失败: {process.stderr.decode()}")
                            return "音频格式转换失败"

                        logger.info("使用FFmpeg转换PCM到WAV成功")
                    except Exception as e:
                        logger.error(f"使用FFmpeg转换时出错: {e}")
                        return "音频处理失败"

        logger.info(f"临时WAV音频文件已保存: {temp_wav_path}")

        # 使用SenseVoice模型进行识别
        res = asr_model.generate(
            input=temp_wav_path,
            cache={},
            language="auto",  # 自动检测语言
            use_itn=True,
            batch_size_s=60,
            merge_vad=True,
            merge_length_s=15,
        )

        # 删除临时文件
        try:
            os.unlink(temp_wav_path)
        except Exception as e:
            logger.warning(f"删除临时文件时出错: {e}")

        # 提取识别结果
        if res and len(res) > 0:
            text = res[0].get("text", "")
            logger.info(f"语音识别结果: {text}")
            return text
        else:
            logger.warning("语音识别未返回结果")
            return "未能识别语音内容"
    except Exception as e:
        logger.error(f"语音识别时出错: {e}")
        import traceback
        traceback.print_exc()
        return f"语音识别错误: {str(e)}"

# 路由定义
@app.get("/", response_class=HTMLResponse)
async def get_index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

# PCM示例页面
@app.get("/pcm-demo", response_class=HTMLResponse)
async def get_pcm_demo(request: Request):
    return templates.TemplateResponse("pcm_demo.html", {"request": request})

# POST接口 - 语音转文字（文件上传方式）
@app.post("/api/stt/upload")
async def post_stt_upload(sample_rate: int = Form(16000), audio_file: UploadFile = File(...)):
    """
    通过POST请求接收PCM格式的音频文件并进行语音转文字

    Args:
        sample_rate: 音频采样率，默认为16000Hz
        audio_file: PCM格式的音频文件

    Returns:
        包含识别文本的JSON响应
    """
    logger.info(f"接收到文件上传请求，文件名: {audio_file.filename}, 采样率: {sample_rate}")

    try:
        # 读取音频文件内容
        audio_data = await audio_file.read()
        logger.info(f"读取到音频数据，大小: {len(audio_data)} 字节")

        # 处理音频数据
        text = await speech_to_text(audio_data, sample_rate=sample_rate)

        # 返回识别结果
        return JSONResponse(content={"status": "success", "text": text})
    except Exception as e:
        logger.error(f"处理文件上传请求时出错: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"处理音频时出错: {str(e)}"}
        )

# POST接口 - 语音转文字（直接发送PCM数据）
@app.post("/api/stt")
async def post_stt(request: Request, sample_rate: int = 16000):
    """
    通过POST请求直接接收PCM格式的音频数据并进行语音转文字

    Args:
        request: 包含PCM音频数据的请求
        sample_rate: 音频采样率，默认为16000Hz

    Returns:
        包含识别文本的JSON响应
    """
    logger.info(f"接收到PCM数据请求，采样率: {sample_rate}")

    try:
        # 读取请求体中的音频数据
        audio_data = await request.body()
        logger.info(f"读取到PCM音频数据，大小: {len(audio_data)} 字节")

        # 处理音频数据
        text = await speech_to_text(audio_data, sample_rate=sample_rate)

        # 返回识别结果
        return JSONResponse(content={"status": "success", "text": text})
    except Exception as e:
        logger.error(f"处理PCM数据请求时出错: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"处理音频时出错: {str(e)}"}
        )

# WebSocket端点 - 语音转文字
@app.websocket("/ws/stt")
async def websocket_stt_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        audio_data = bytearray()
        logger.info("STT WebSocket连接已建立")

        # 发送连接成功消息
        await manager.send_json({"type": "connection", "status": "connected"}, websocket)

        # 创建心跳任务
        heartbeat_task = asyncio.create_task(send_periodic_heartbeat(websocket))

        while True:
            try:
                data = await websocket.receive()

                # 处理心跳响应
                if "text" in data and data["text"] == "heartbeat_pong":
                    logger.debug("收到心跳响应")
                    manager.update_heartbeat(websocket)
                    continue

                if "bytes" in data:
                    # 接收音频数据
                    chunk = data["bytes"]
                    audio_data.extend(chunk)
                    logger.debug(f"接收到音频数据块，大小: {len(chunk)} 字节")

                    # 如果是第一个数据块，尝试检测格式
                    if len(audio_data) <= len(chunk):
                        try:
                            # 检查前几个字节以识别格式
                            header = audio_data[:min(16, len(audio_data))]
                            logger.info(f"数据头部: {header.hex()}")

                            # 检查是否是WAV格式
                            if len(audio_data) > 12 and audio_data[:4] == b'RIFF' and audio_data[8:12] == b'WAVE':
                                logger.info("检测到WAV格式数据")
                            else:
                                logger.info("未检测到WAV格式，假设是PCM格式数据")
                                # 检查PCM数据的特征
                                if len(audio_data) >= 4:
                                    # 尝试分析前几个字节，查看是否符合PCM数据特征
                                    try:
                                        # 将前几个字节转换为16位整数，检查值是否在合理范围内
                                        import struct
                                        samples = []
                                        for i in range(0, min(len(audio_data), 20), 2):
                                            if i + 1 < len(audio_data):
                                                sample = struct.unpack('<h', audio_data[i:i+2])[0]
                                                samples.append(sample)

                                        # 检查样本值是否在合理范围内
                                        if samples:
                                            min_val = min(samples)
                                            max_val = max(samples)
                                            logger.info(f"PCM样本值范围: {min_val} 到 {max_val}")

                                            # 典型的音频样本值通常在 -32768 到 32767 之间
                                            if -32768 <= min_val <= 32767 and -32768 <= max_val <= 32767:
                                                logger.info("数据符合PCM格式特征")
                                            else:
                                                logger.warning("数据可能不是PCM格式，样本值超出范围")
                                    except Exception as e:
                                        logger.error(f"分析PCM数据时出错: {e}")
                        except Exception as e:
                            logger.error(f"检查数据格式时出错: {e}")

                elif "text" in data:
                    message = data["text"]
                    logger.info(f"接收到文本消息: {message}")

                    if message == "end_recording":
                        # 结束录音，开始处理
                        logger.info(f"接收到结束录音信号，音频数据总大小: {len(audio_data)} 字节")

                        if audio_data and len(audio_data) > 100:  # 确保有足够的数据
                            # 处理音频数据
                            await manager.send_json({"type": "processing", "message": "正在处理语音..."}, websocket)
                            text = await speech_to_text(audio_data)

                            # 发送识别结果
                            await manager.send_json({"type": "stt_result", "text": text}, websocket)

                            # 清空音频数据，准备下一次录音
                            audio_data = bytearray()
                        else:
                            logger.warning(f"音频数据不足，大小: {len(audio_data)} 字节")
                            await manager.send_json({"type": "error", "message": "未接收到足够的音频数据"}, websocket)

                    elif message.startswith("config:"):
                        # 处理配置信息
                        logger.info(f"接收到配置信息: {message}")
                        # 可以在这里处理采样率等配置
                        await manager.send_json({"type": "config_received"}, websocket)
            except Exception as e:
                logger.error(f"处理WebSocket消息时出错: {e}")
                await manager.send_json({"type": "error", "message": f"处理消息时出错: {str(e)}"}, websocket)

    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info("STT WebSocket连接已断开")
        # 取消心跳任务
        if 'heartbeat_task' in locals() and heartbeat_task is not None:
            heartbeat_task.cancel()
            logger.debug("STT心跳任务已取消")
    except Exception as e:
        logger.error(f"STT WebSocket处理时出错: {e}")
        import traceback
        traceback.print_exc()
        try:
            await manager.send_json({"type": "error", "message": f"服务器错误: {str(e)}"}, websocket)
        except:
            pass
        # 取消心跳任务
        if 'heartbeat_task' in locals() and heartbeat_task is not None:
            heartbeat_task.cancel()
            logger.debug("STT心跳任务已取消")

# 定义周期性发送心跳的异步函数
async def send_periodic_heartbeat(websocket: WebSocket, interval: int = 5):
    """
    定期发送心跳消息

    Args:
        websocket: WebSocket连接
        interval: 心跳间隔（秒）
    """
    try:
        while True:
            # 检查WebSocket连接状态
            if websocket.client_state.name != "CONNECTED":
                logger.debug(f"WebSocket已断开，停止心跳: {websocket.client_state}")
                break

            # 发送心跳
            await manager.send_heartbeat(websocket)

            # 等待指定的间隔时间
            await asyncio.sleep(interval)

            # 检查是否超时
            if manager.check_connection_timeout(websocket, timeout_seconds=interval*10):
                logger.warning(f"WebSocket心跳超时，关闭连接: {websocket.client.host}")
                try:
                    await websocket.close(code=1000, reason="心跳超时")
                except Exception as e:
                    logger.error(f"关闭超时WebSocket时出错: {e}")
                break
    except asyncio.CancelledError:
        logger.debug("心跳任务被取消")
    except Exception as e:
        logger.error(f"心跳任务出错: {e}")

# WebSocket端点 - 文字转语音（流式）
@app.websocket("/ws/tts")
async def websocket_tts_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        logger.info("TTS WebSocket连接已建立")

        # 发送连接成功消息
        await manager.send_json({"type": "connection", "status": "connected"}, websocket)

        # 发送音频格式信息
        await manager.send_json({"type": "audio_format", "format": "opus"}, websocket)

        # 创建心跳任务
        heartbeat_task = asyncio.create_task(send_periodic_heartbeat(websocket))

        # 加载提示音频（只加载一次）
        prompt_speech_16k = find_prompt_audio()

        # 用于控制是否停止生成语音的标志
        stop_generation = False

        # 创建一个事件来控制语音生成
        stop_event = asyncio.Event()

        # 流式文本缓冲区
        stream_text_buffer = ""

        # 当前语音生成任务
        current_speech_task = None

        # 定义语音生成任务
        async def generate_speech_task(text_to_process):
            nonlocal stop_generation
            chunk_count = 0

            try:
                # 创建一个任务取消标志
                task_cancelled = False

                # 修改text_to_speech_stream函数的调用方式，使其能够响应取消
                text_to_speech_iterator = text_to_speech_stream(text_to_process, prompt_speech_16k)

                while True:
                    # 检查是否收到停止指令
                    if stop_generation or stop_event.is_set():
                        logger.info("语音生成任务检测到停止信号")
                        task_cancelled = True
                        break

                    # 判断 websocket 有没有断开
                    if websocket.client_state.name != "CONNECTED":
                        logger.info(f"WebSocket已断开，停止生成: {websocket.client_state}")
                        task_cancelled = True
                        break

                    try:
                        # 使用带超时的方式获取下一个音频块，这样可以更频繁地检查停止标志
                        audio_chunk = await asyncio.wait_for(text_to_speech_iterator.__anext__(), timeout=1)
                    except StopAsyncIteration:
                        # 迭代器已结束
                        logger.info("语音生成迭代器已结束")
                        break
                    except asyncio.TimeoutError:
                        # 超时，继续检查停止标志
                        continue
                    except Exception as e:
                        logger.error(f"获取音频块时出错: {e}")
                        break

                    # 发送音频数据，添加内容类型信息
                    await manager.send_json({"type": "audio_data", "content_type": "audio/wav"}, websocket)
                    await manager.send_bytes(audio_chunk, websocket)
                    chunk_count += 1
                    logger.debug(f"已发送音频块 {chunk_count}，大小: {len(audio_chunk)} 字节")

                # 不在这里发送tts_end信号，而是在所有任务完成后发送
                if task_cancelled:
                    logger.info(f"语音生成任务被取消，已发送 {chunk_count} 个音频块")
                else:
                    logger.info(f"文本转语音片段完成，共发送 {chunk_count} 个音频块")
            except asyncio.CancelledError:
                logger.info("语音生成任务被显式取消")
                # 传播取消异常，确保任务正确取消
                raise
            except Exception as e:
                logger.error(f"语音生成任务出错: {e}")
                import traceback
                logger.error(traceback.format_exc())
            finally:
                # 任务完成后清除当前任务引用
                nonlocal current_speech_task
                current_speech_task = None

        while True:
            try:
                # 使用receive()而不是receive_json()，以便能够接收非阻塞的消息
                data = await websocket.receive()

                logger.info(f"接收到的内容: {data}")

                # 处理心跳响应
                if "text" in data and data["text"] == "heartbeat_pong":
                    logger.debug("收到心跳响应")
                    manager.update_heartbeat(websocket)
                    continue

                # 处理文本消息或JSON数据
                if "text" in data:
                    try:
                        message = json.loads(data["text"])

                        # 处理停止指令
                        if isinstance(message, dict) and message.get("type") == "stop":
                            logger.info("接收到停止生成语音的指令")
                            # 设置停止标志
                            stop_generation = True
                            if stop_event:
                                stop_event.set()

                            # 清空文本队列，防止继续处理
                            if hasattr(websocket_tts_endpoint, 'text_queue'):
                                websocket_tts_endpoint.text_queue = []
                                logger.info("已清空文本队列")

                            # 如果有正在运行的任务，尝试取消它
                            if current_speech_task and not current_speech_task.done():
                                try:
                                    # 尝试取消任务
                                    current_speech_task.cancel()
                                    logger.info("已尝试取消当前语音生成任务")
                                except Exception as e:
                                    logger.error(f"取消语音生成任务时出错: {e}")

                            await manager.send_json({"type": "tts_stopped", "message": "语音生成已停止"}, websocket)
                            continue

                        # 处理流式文本输入
                        if isinstance(message, dict) and message.get("type") == "stream_text":
                            text_chunk = message.get("text", "")
                            logger.info(f"接收到流式文本片段: {text_chunk}")

                            if text_chunk:
                                # 将文本片段添加到缓冲区
                                stream_text_buffer += text_chunk

                                # 进一步优化的文本分段逻辑
                                # 检查是否有自然断点（完整的句子结束）
                                natural_break = False
                                text_to_process = ""

                                # 定义句子结束的标点符号及其权重（优先级）
                                # 句号、问号、感叹号等表示句子结束的标点符号优先级更高
                                end_punctuations_with_weights = [
                                    ("。", 10), ("？", 10), ("！", 10),  # 中文句末标点（高优先级）
                                    (".", 9), ("?", 9), ("!", 9),       # 英文句末标点（高优先级）
                                    ("；", 8), (";", 8),                 # 分号（中等优先级）
                                    # ("：", 7), (":", 7),                 # 冒号（中等优先级）
                                    ("，", 6), (",", 6),                 # 逗号（低优先级）
                                    ("\n\n", 5), ("\n", 4)              # 换行（最低优先级）
                                ]

                                # 查找最佳的句子分割点
                                best_break_pos = -1
                                best_weight = -1

                                # 首先尝试查找完整句子的结束位置
                                for punct, weight in end_punctuations_with_weights:
                                    pos = stream_text_buffer.rfind(punct)
                                    # 只考虑在缓冲区前150个字符内的标点
                                    if pos > best_break_pos and pos < 150 and weight > best_weight:
                                        # 检查这个标点是否是句子的结束（不是在引号内或其他特殊情况）
                                        is_valid_end = True

                                        # 如果是逗号或分号，确保它不是在枚举列表中间
                                        if punct in ["，", ",", "；", ";"]:
                                            # 简单检查：如果标点后面紧跟着非空白字符，可能是枚举的一部分
                                            if pos < len(stream_text_buffer) - 1 and stream_text_buffer[pos + 1] not in [" ", "\n", "\t"]:
                                                # 进一步检查：如果前后文本模式相似，可能是枚举
                                                before_text = stream_text_buffer[max(0, pos - 10):pos]
                                                after_text = stream_text_buffer[pos + 1:min(len(stream_text_buffer), pos + 11)]
                                                # 如果前后文本都包含相似的词语模式，可能是枚举
                                                if (any(x in before_text for x in ["、", "和", "与"]) and
                                                    any(x in after_text for x in ["、", "和", "与"])):
                                                    is_valid_end = False

                                        if is_valid_end:
                                            best_break_pos = pos
                                            best_weight = weight

                                # 如果找到了合适的分割点
                                if best_break_pos >= 0:
                                    # 确定结束位置（包含标点符号）
                                    end_pos = best_break_pos + 1

                                    # 如果是句末标点，可能后面还有引号、括号等需要包含
                                    if best_weight >= 9:  # 句号、问号、感叹号等
                                        # 检查后面是否有引号、括号等需要包含的符号
                                        closing_symbols = ['"', '」', '』', ')', '）', ']', '］', '}', '｝']
                                        for i in range(best_break_pos + 1, min(len(stream_text_buffer), best_break_pos + 10)):
                                            if stream_text_buffer[i] in closing_symbols:
                                                end_pos = i + 1
                                            elif stream_text_buffer[i] not in [" ", "\n", "\t"]:
                                                break

                                    # 提取要处理的文本，并更新缓冲区
                                    text_to_process = stream_text_buffer[:end_pos]
                                    stream_text_buffer = stream_text_buffer[end_pos:]
                                    natural_break = True
                                    logger.info(f"在位置 {best_break_pos} 找到自然断点: '{text_to_process[-1]}', 权重: {best_weight}")

                                # 设置缓冲区长度阈值，避免缓冲区过大
                                # 根据不同的场景动态调整阈值
                                min_buffer_threshold = 25   # 最小阈值
                                max_buffer_threshold = 75  # 最大阈值

                                # 如果文本中包含大量的专业术语或长句子，使用较大的阈值
                                has_long_terms = any(term in stream_text_buffer for term in ["技术", "科学", "研究", "分析", "理论"])
                                buffer_length_threshold = max_buffer_threshold if has_long_terms else min_buffer_threshold

                                # 如果没有找到自然断点，但缓冲区长度超过阈值
                                if not natural_break and len(stream_text_buffer) >= buffer_length_threshold:
                                    logger.info(f"缓冲区长度 {len(stream_text_buffer)} 超过阈值 {buffer_length_threshold}，尝试在词语边界分割")

                                    # 尝试在词语边界分割（优先考虑标点符号，然后是空格）
                                    delimiters = ["，", ",", "、", " ", "；", ";"]
                                    # delimiters = ["，", ",", "、", " ", "：", ":", "；", ";"]
                                    for delimiter in delimiters:
                                        pos = stream_text_buffer.rfind(delimiter, 0, buffer_length_threshold)
                                        if pos > 0:
                                            text_to_process = stream_text_buffer[:pos + 1]
                                            stream_text_buffer = stream_text_buffer[pos + 1:]
                                            natural_break = True
                                            logger.info(f"在位置 {pos} 找到分隔符 '{delimiter}'")
                                            break

                                    # 如果找不到合适的分割点，尝试在汉字边界分割
                                    if not natural_break:
                                        # 在汉字和非汉字之间分割
                                        for i in range(min(buffer_length_threshold, len(stream_text_buffer)) - 1, 0, -1):
                                            # 检查是否是汉字和非汉字的边界
                                            char1 = stream_text_buffer[i-1]
                                            char2 = stream_text_buffer[i]
                                            is_char1_chinese = '\u4e00' <= char1 <= '\u9fff'
                                            is_char2_chinese = '\u4e00' <= char2 <= '\u9fff'

                                            if is_char1_chinese != is_char2_chinese:
                                                text_to_process = stream_text_buffer[:i]
                                                stream_text_buffer = stream_text_buffer[i:]
                                                natural_break = True
                                                logger.info(f"在位置 {i} 找到汉字边界: '{char1}{char2}'")
                                                break

                                    # 如果仍然找不到合适的分割点，就按阈值分割
                                    if not natural_break:
                                        text_to_process = stream_text_buffer[:buffer_length_threshold]
                                        stream_text_buffer = stream_text_buffer[buffer_length_threshold:]
                                        natural_break = True
                                        logger.info(f"未找到合适的分割点，按阈值 {buffer_length_threshold} 分割")

                                # 如果有文本需要处理，并且文本不是空白
                                if natural_break and text_to_process and text_to_process.strip():
                                    logger.info(f"准备处理文本片段: {text_to_process}")

                                    # 进一步优化的任务管理：使用更智能的队列方式处理任务
                                    # 创建一个文本队列来存储待处理的文本
                                    if not hasattr(websocket_tts_endpoint, 'text_queue'):
                                        websocket_tts_endpoint.text_queue = []

                                    # 如果当前没有任务在运行，直接启动新任务
                                    if len(websocket_tts_endpoint.text_queue) == 0 and (not current_speech_task or current_speech_task.done()):
                                        # not current_speech_task or current_speech_task.done():
                                        # 重置停止标志和事件
                                        stop_generation = False
                                        stop_event.clear()

                                        # 启动新的语音生成任务
                                        logger.info(f"直接启动语音生成任务: {text_to_process[:20]}...")
                                        current_speech_task = asyncio.create_task(generate_speech_task(text_to_process))
                                    else:
                                        # 如果有任务在运行，将新任务添加到队列
                                        # 检查队列中是否已经有足够多的文本，避免队列过长
                                        max_queue_size = 10  # 最大队列长度

                                        if len(websocket_tts_endpoint.text_queue) >= max_queue_size:
                                            # 队列已满，合并最后一个文本块
                                            last_text = websocket_tts_endpoint.text_queue.pop()
                                            merged_text = last_text + text_to_process
                                            websocket_tts_endpoint.text_queue.append(merged_text)
                                            logger.info(f"队列已满，合并最后一个文本块: {merged_text[:20]}...")
                                        else:
                                            # 将新文本添加到队列
                                            websocket_tts_endpoint.text_queue.append(text_to_process)
                                            logger.info(f"将文本添加到队列: {text_to_process[:20]}..., 队列长度: {len(websocket_tts_endpoint.text_queue)}")

                                        # 如果队列处理任务尚未启动，启动它
                                        if not hasattr(websocket_tts_endpoint, 'queue_processor_running') or not websocket_tts_endpoint.queue_processor_running:
                                            websocket_tts_endpoint.queue_processor_running = True

                                            # 创建一个异步任务来处理队列
                                            async def process_text_queue():
                                                # 声明使用外部变量
                                                nonlocal stop_generation, stop_event, current_speech_task

                                                try:
                                                    # 等待当前任务完成
                                                    if current_speech_task and not current_speech_task.done():
                                                        await current_speech_task

                                                    # 处理队列中的所有文本
                                                    while websocket_tts_endpoint.text_queue:
                                                        # 获取下一个要处理的文本
                                                        next_text = websocket_tts_endpoint.text_queue.pop(0)
                                                        logger.info(f"从队列中处理下一个文本: {next_text[:20]}..., 剩余队列长度: {len(websocket_tts_endpoint.text_queue)}")

                                                        # 检查文本是否为空白
                                                        if not next_text or not next_text.strip():
                                                            logger.warning("跳过空白文本")
                                                            continue

                                                        # 重置停止标志和事件
                                                        stop_generation = False
                                                        stop_event.clear()

                                                        # 启动新的语音生成任务
                                                        current_speech_task = asyncio.create_task(generate_speech_task(next_text))

                                                        # 等待任务完成
                                                        await current_speech_task

                                                    # 队列处理完成
                                                    websocket_tts_endpoint.queue_processor_running = False
                                                    logger.info("队列处理完成")
                                                    await manager.send_json({"type": "tts_end"}, websocket)
                                                    logger.info("发送tts_end信号，所有语音生成任务已完全完成")
                                                except Exception as e:
                                                    logger.error(f"处理文本队列时出错: {e}")
                                                    import traceback
                                                    logger.error(traceback.format_exc())
                                                    websocket_tts_endpoint.queue_processor_running = False

                                            # 启动队列处理任务
                                            asyncio.create_task(process_text_queue())

                            continue

                        # 处理流式文本结束
                        if isinstance(message, dict) and message.get("type") == "stream_end":
                            logger.info("接收到流式文本结束信号")

                            # 优化流式文本结束时的处理逻辑
                            # 如果缓冲区中还有文本，进行最后一次转换
                            if stream_text_buffer:
                                logger.info(f"处理流式文本结束时的剩余文本: {stream_text_buffer}")

                                # 尝试在自然断点处分割最后的文本
                                # 这里我们使用更简单的逻辑，因为这是最后的文本
                                final_texts = []

                                # 如果文本较长，尝试分割成更小的片段
                                if len(stream_text_buffer) > 150:
                                    # 定义句子结束的标点符号
                                    end_punctuations = ["。", "？", "！", "；", ".", "?", "!", ";", "\n\n"]

                                    # 查找所有可能的分割点
                                    break_positions = []
                                    for punct in end_punctuations:
                                        pos = 0
                                        while True:
                                            pos = stream_text_buffer.find(punct, pos)
                                            if pos == -1:
                                                break
                                            break_positions.append(pos)
                                            pos += 1

                                    # 按位置排序
                                    break_positions.sort()

                                    # 如果找到了分割点
                                    if break_positions:
                                        # 分割文本
                                        last_pos = 0
                                        for pos in break_positions:
                                            if pos - last_pos >= 30:  # 确保每个片段不会太短
                                                final_texts.append(stream_text_buffer[last_pos:pos+1])
                                                last_pos = pos + 1

                                        # 添加最后一个片段
                                        if last_pos < len(stream_text_buffer):
                                            final_texts.append(stream_text_buffer[last_pos:])

                                # 如果没有找到合适的分割点，或者文本较短，直接处理整个文本
                                if not final_texts:
                                    final_texts = [stream_text_buffer]

                                # 清空缓冲区
                                stream_text_buffer = ""

                                # 将最终文本添加到队列
                                if not hasattr(websocket_tts_endpoint, 'text_queue'):
                                    websocket_tts_endpoint.text_queue = []

                                # 添加所有最终文本片段到队列（过滤掉空白文本）
                                for text in final_texts:
                                    if text and text.strip():
                                        websocket_tts_endpoint.text_queue.append(text)
                                        logger.info(f"将最终文本片段添加到队列: {text[:20]}...")
                                    else:
                                        logger.warning("跳过空白文本片段")

                                # 如果队列处理任务尚未启动，启动它
                                if not hasattr(websocket_tts_endpoint, 'queue_processor_running') or not websocket_tts_endpoint.queue_processor_running:
                                    websocket_tts_endpoint.queue_processor_running = True

                                    # 创建一个异步任务来处理队列
                                    async def process_final_queue():
                                        # 声明使用外部变量
                                        nonlocal stop_generation, stop_event, current_speech_task

                                        try:
                                            # 如果有任务在运行，等待它完成
                                            if current_speech_task and not current_speech_task.done():
                                                logger.info("等待当前任务完成后处理最终文本...")
                                                await current_speech_task

                                            # 处理队列中的所有文本
                                            while websocket_tts_endpoint.text_queue:
                                                # 获取下一个要处理的文本
                                                next_text = websocket_tts_endpoint.text_queue.pop(0)
                                                logger.info(f"处理最终文本片段: {next_text[:20]}..., 剩余队列长度: {len(websocket_tts_endpoint.text_queue)}")

                                                # 检查文本是否为空白
                                                if not next_text or not next_text.strip():
                                                    logger.warning("跳过空白最终文本")
                                                    continue

                                                # 重置停止标志和事件
                                                stop_generation = False
                                                stop_event.clear()

                                                # 启动新的语音生成任务
                                                current_speech_task = asyncio.create_task(generate_speech_task(next_text))

                                                # 等待任务完成
                                                await current_speech_task

                                            # 发送流式文本处理完成的信号
                                            await manager.send_json({"type": "stream_tts_completed"}, websocket)

                                            # 发送tts_end信号，表示所有语音生成任务已完全完成
                                            if websocket.client_state.name == "CONNECTED":
                                                await manager.send_json({"type": "tts_end"}, websocket)
                                                logger.info("发送tts_end信号，所有语音生成任务已完全完成")

                                            logger.info("流式文本转语音处理完成")

                                            # 队列处理完成
                                            websocket_tts_endpoint.queue_processor_running = False
                                        except Exception as e:
                                            logger.error(f"处理最终文本队列时出错: {e}")
                                            import traceback
                                            logger.error(traceback.format_exc())
                                            websocket_tts_endpoint.queue_processor_running = False

                                    # 启动队列处理任务
                                    asyncio.create_task(process_final_queue())

                            continue

                        # 处理文本转语音请求
                        if "text" in message:
                            text = message["text"]
                            logger.info(f"接收到文本: {text}")

                            if text and text.strip():
                                # 重置停止标志
                                stop_generation = False

                                # 发送处理中消息
                                await manager.send_json({"type": "processing", "message": "正在生成语音..."}, websocket)

                                # 如果有正在运行的语音生成任务，先停止它
                                if current_speech_task and not current_speech_task.done():
                                    stop_generation = True
                                    stop_event.set()
                                    # 等待一小段时间让任务有机会停止
                                    await asyncio.sleep(0.1)

                                # 重置停止标志和事件
                                stop_generation = False
                                stop_event.clear()

                                # 启动新的语音生成任务
                                current_speech_task = asyncio.create_task(generate_speech_task(text))

                                # 创建一个异步任务来等待语音生成任务完成并发送tts_end信号
                                async def wait_and_send_tts_end():
                                    try:
                                        # 等待语音生成任务完成
                                        await current_speech_task

                                        # 发送tts_end信号，表示语音生成任务已完全完成
                                        if websocket.client_state.name == "CONNECTED":
                                            await manager.send_json({"type": "tts_end"}, websocket)
                                            logger.info("发送tts_end信号，单个文本的语音生成任务已完全完成")
                                    except Exception as e:
                                        logger.error(f"等待语音生成任务并发送tts_end信号时出错: {e}")

                                # 启动等待任务
                                asyncio.create_task(wait_and_send_tts_end())
                            else:
                                await manager.send_json({"type": "error", "message": "文本为空"}, websocket)
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，尝试直接处理文本
                        text = data["text"]

                        # 处理直接的停止命令
                        if text == "stop":
                            logger.info("接收到停止生成语音的指令(文本格式)")
                            # 设置停止标志
                            stop_generation = True
                            if stop_event:
                                stop_event.set()

                            # 清空文本队列，防止继续处理
                            if hasattr(websocket_tts_endpoint, 'text_queue'):
                                websocket_tts_endpoint.text_queue = []
                                logger.info("已清空文本队列")

                            # 如果有正在运行的任务，尝试取消它
                            if current_speech_task and not current_speech_task.done():
                                try:
                                    # 尝试取消任务
                                    current_speech_task.cancel()
                                    logger.info("已尝试取消当前语音生成任务")
                                except Exception as e:
                                    logger.error(f"取消语音生成任务时出错: {e}")

                            await manager.send_json({"type": "tts_stopped", "message": "语音生成已停止"}, websocket)
                            continue

                        logger.info(f"接收到非JSON格式文本: {text}")
                        # 这里可以添加对非JSON格式文本的处理逻辑

            except json.JSONDecodeError:
                logger.error("接收到无效的JSON数据")
                await manager.send_json({"type": "error", "message": "无效的JSON格式"}, websocket)
            except Exception as e:
                logger.error(f"处理WebSocket消息时出错: {e}")
                await manager.send_json({"type": "error", "message": f"处理消息时出错: {str(e)}"}, websocket)

    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info("TTS WebSocket连接已断开")
        # 取消心跳任务
        if 'heartbeat_task' in locals() and heartbeat_task is not None:
            heartbeat_task.cancel()
            logger.debug("TTS心跳任务已取消")
    except Exception as e:
        logger.error(f"TTS WebSocket处理时出错: {e}")
        import traceback
        traceback.print_exc()
        try:
            await manager.send_json({"type": "error", "message": f"服务器错误: {str(e)}"}, websocket)
        except:
            pass
        # 取消心跳任务
        if 'heartbeat_task' in locals() and heartbeat_task is not None:
            heartbeat_task.cancel()
            logger.debug("TTS心跳任务已取消")

# 添加CORS中间件，允许跨域请求
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
    expose_headers=["*"]
)

# 使用lifespan上下文管理器加载模型（已在应用创建时定义）

# 主函数
if __name__ == "__main__":
    # 创建templates目录
    os.makedirs("templates", exist_ok=True)

    # 打印访问URL
    logger.info("="*50)
    logger.info("应用已启动！")
    logger.info("请在浏览器中访问: http://localhost:8000")
    logger.info("="*50)

    # 启动服务器
    uvicorn.run(app, host="0.0.0.0",
                port=8000,
                log_level="info",
                timeout_keep_alive=1200,
                ws_ping_interval=20,     # 20秒发送一次ping
                ws_ping_timeout=30,      # 30秒ping超时
                ws_max_size=16777216     # 16MB最大WebSocket消息大小
    )

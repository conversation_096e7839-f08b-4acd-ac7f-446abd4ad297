
help:  ## Show help
	@grep -E '^[.a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

clean: ## Clean autogenerated files
	rm -rf dist
	find . -type f -name "*.DS_Store" -ls -delete
	find . | grep -E "(__pycache__|\.pyc|\.pyo)" | xargs rm -rf
	find . | grep -E ".pytest_cache" | xargs rm -rf
	find . | grep -E ".ipynb_checkpoints" | xargs rm -rf
	rm -f .coverage

clean-logs: ## Clean logs
	rm -rf logs/**

create-package: ## Create wheel and tar gz
	rm -rf dist/
	python setup.py bdist_wheel --plat-name=manylinux1_x86_64
	python setup.py sdist
	python -m twine upload  dist/* --verbose --skip-existing

format: ## Run pre-commit hooks
	pre-commit run -a

sync: ## Merge changes from main branch to your current branch
	git pull
	git pull origin main

test: ## Run not slow tests
	pytest -k "not slow"

test-full: ## Run all tests
	pytest

train-ljspeech: ## Train the model
	python matcha/train.py experiment=ljspeech

train-ljspeech-min: ## Train the model with minimum memory
	python matcha/train.py experiment=ljspeech_min_memory

start_app: ## Start the app
	python matcha/app.py

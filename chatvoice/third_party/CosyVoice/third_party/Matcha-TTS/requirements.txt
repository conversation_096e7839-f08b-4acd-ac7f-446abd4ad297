# --------- pytorch --------- #
torch>=2.0.0
torchvision>=0.15.0
lightning>=2.0.0
torchmetrics>=0.11.4

# --------- hydra --------- #
hydra-core==1.3.2
hydra-colorlog==1.2.0
hydra-optuna-sweeper==1.2.0

# --------- loggers --------- #
# wandb
# neptune-client
# mlflow
# comet-ml
# aim>=3.16.2  # no lower than 3.16.2, see https://github.com/aimhubio/aim/issues/2550

# --------- others --------- #
rootutils       # standardizing the project root setup
pre-commit      # hooks for applying linters on commit
rich            # beautiful text formatting in terminal
pytest          # tests
# sh            # for running bash commands in some tests (linux/macos only)
phonemizer      # phonemization of text
tensorboard
librosa
Cython
numpy
einops
inflect
Unidecode
scipy
torchaudio
matplotlib
pandas
conformer==0.3.2
diffusers==0.25.0
notebook
ipywidgets
gradio==3.43.2
gdown
wget
seaborn
piper_phonemize

# 语音转换应用

这是一个基于CosyVoice2和SenseVoice的语音转换应用，提供以下功能：

1. **语音转文字**：通过麦克风输入语音，实时转换为文字
2. **文字转语音**：输入文字，流式生成自然语音

本应用支持WebSocket流式传输，可以实现一边输入一边播放的效果，提供更加自然流畅的语音交互体验。

## 系统要求

- Python 3.10+
- CUDA 12.1+（GPU加速，可选）
- 推荐使用conda环境
- 支持WebSocket的现代浏览器（Chrome、Firefox、Edge等）
- 麦克风（用于语音输入）
- 扬声器或耳机（用于语音输出）

### 硬件要求

- **CPU模式**：
  - 至少4核CPU
  - 至少8GB内存
  - 至少10GB磁盘空间（包括模型）

- **GPU模式**（推荐）：
  - NVIDIA GPU，至少6GB显存
  - CUDA 12.1及以上
  - 至少8GB系统内存
  - 至少10GB磁盘空间（包括模型）

## 安装

1. 克隆本仓库：
   ```bash
   git clone <repository-url>
   cd chatvoice
   ```

2. 创建并激活conda环境：
   ```bash
   conda create -n chatvoice python=3.10
   conda activate chatvoice
   ```

3. 安装依赖项：

   或者手动安装：
   ```bash
   pip install -r requirements.txt
   ```

4. 确保已下载所需的模型：
   - CosyVoice2-0.5B模型应位于`models/CosyVoice2-0.5B`目录
   - SenseVoiceSmall模型应位于`models/SenseVoiceSmall`目录
   - CosyVoice代码库应位于`third_party/CosyVoice`目录

## 使用方法

### 本地运行

1. 启动应用：
   ```bash
   python app.py
   ```

2. 在浏览器中访问：
   ```
   http://localhost:8000
   ```

3. 使用界面：
   - **语音转文字**：点击"开始录音"按钮，说话，然后点击"停止录音"按钮
   - **文字转语音**：在文本框中输入文字，点击"播放"按钮，或者直接输入文字（会自动播放）

### Docker部署

1. 构建Docker镜像：
   ```bash
   docker build -t chatvoice .
   ```

2. 运行Docker容器（CPU模式）：
   ```bash
   docker run -p 8000:8000 \
     -v /path/to/models/CosyVoice2-0.5B:/app/models/CosyVoice2-0.5B \
     -v /path/to/models/SenseVoiceSmall:/app/models/SenseVoiceSmall \
     chatvoice
   ```

3. 运行Docker容器（GPU模式）：
   ```bash
   docker run --gpus all -p 8000:8000 \
     -v /path/to/models/CosyVoice2-0.5B:/app/models/CosyVoice2-0.5B \
     -v /path/to/models/SenseVoiceSmall:/app/models/SenseVoiceSmall \
     chatvoice
   ```

   > 注意：使用GPU模式需要宿主机安装NVIDIA Container Toolkit。安装方法：
   > ```bash
   > distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
   > curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
   > curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
   > sudo apt-get update && sudo apt-get install -y nvidia-container-toolkit
   > sudo systemctl restart docker
   > ```

4. 在浏览器中访问：
   ```
   http://localhost:8000
   ```

## API接口说明

本应用提供以下WebSocket接口：

### 1. 语音转文字接口

- **URL**: `/ws/stt`
- **协议**: WebSocket
- **功能**: 将语音数据转换为文本
- **输入**:
  - 二进制音频数据块
  - 文本消息 `"end_recording"` 表示录音结束
- **输出**:
  - JSON格式: `{"type": "stt_result", "text": "识别的文本"}`
  - 错误信息: `{"type": "error", "message": "错误信息"}`

### 2. 文字转语音接口

- **URL**: `/ws/tts`
- **协议**: WebSocket
- **功能**: 将文本转换为语音，支持流式输出
- **输入**:
  - JSON格式: `{"text": "要转换的文本"}`
- **输出**:
  - 音频格式信息: `{"type": "audio_format", "format": "opus"}`
  - 内容类型信息: `{"type": "audio_data", "content_type": "audio/opus"}`
  - 二进制音频数据块（Opus格式，如果Opus编码失败则回退到WAV格式）
  - 结束信号: `{"type": "tts_end"}`
  - 错误信息: `{"type": "error", "message": "错误信息"}`

## 注意事项

- 首次启动时，模型加载可能需要一些时间
- 语音转文字功能需要麦克风权限，请在浏览器中允许
- 文字转语音功能使用WebSocket流式传输，确保网络连接稳定
- 如果遇到CosyVoice2模型加载错误，请确保diffusers库版本为0.29.0

## 技术栈

- **后端**：
  - FastAPI：Web框架
  - WebSockets：实时通信
  - CosyVoice2：文字转语音模型
  - SenseVoice：语音转文字模型
  - Uvicorn：ASGI服务器
  - PyOgg/Opus：高效音频编码
  - CUDA/GPU加速：提高模型推理速度

- **前端**：
  - HTML/CSS/JavaScript：用户界面
  - Web Audio API：音频处理和可视化
  - WebSockets：与后端通信
  - MediaRecorder API：录音功能
  - Opus音频支持：高质量低带宽音频

- **部署**：
  - Docker：容器化部署
  - NVIDIA Container Toolkit：GPU支持
  - 支持模型目录挂载

- **性能优化**：
  - 流式处理：减少延迟
  - Opus音频编码：高质量低带宽
  - GPU加速：提高推理速度
  - 音频缓存：平滑播放体验

## 故障排除

1. **模型加载错误**：
   - 确保已安装正确版本的依赖项
   - 检查模型文件是否完整
   - 尝试重新安装diffusers 0.29.0：`pip install diffusers==0.29.0`
   - 确保设置了`use_flow_cache=True`（对于流式输出）

2. **麦克风不工作**：
   - 确保浏览器有麦克风权限
   - 检查系统麦克风设置
   - 尝试使用不同的浏览器
   - 确保使用HTTPS或localhost（现代浏览器要求安全上下文）

3. **WebSocket连接错误**：
   - 检查网络连接
   - 确保没有防火墙阻止WebSocket连接
   - 尝试重启应用
   - 安装WebSocket依赖：`pip install websockets uvicorn[standard]`

4. **Docker相关问题**：
   - 确保正确挂载模型目录
   - 检查容器日志：`docker logs <container_id>`
   - 确保宿主机有足够的资源（内存、CPU）

## 许可证

本项目基于Apache 2.0许可证开源。

## 模型下载

本应用需要以下模型：

1. **CosyVoice2-0.5B**：
   - 下载地址：[CosyVoice2-0.5B](https://huggingface.co/FunAudioLLM/CosyVoice2-0.5B)
   - 放置路径：`models/CosyVoice2-0.5B`

2. **SenseVoiceSmall**：
   - 下载地址：[SenseVoiceSmall](https://huggingface.co/FunAudioLLM/SenseVoiceSmall)
   - 放置路径：`models/SenseVoiceSmall`

## 致谢

- [CosyVoice](https://github.com/FunAudioLLM/CosyVoice)：提供文字转语音功能
- [SenseVoice](https://github.com/FunAudioLLM/SenseVoice)：提供语音转文字功能
- [FastAPI](https://fastapi.tiangolo.com/)：提供Web框架
- [Uvicorn](https://www.uvicorn.org/)：提供ASGI服务器

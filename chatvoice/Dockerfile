FROM m.daocloud.io/docker.io/nvidia/cuda:12.1.1-cudnn8-runtime-ubuntu22.04

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    curl \
    wget \
    software-properties-common \
    ffmpeg \
    libopus-dev \
    libopusfile-dev \
    opus-tools \
    unzip \
    sox \
    libsox-dev \
    && add-apt-repository ppa:deadsnakes/ppa \
    && apt-get update \
    && apt-get install -y python3.11 python3.11-dev python3.11-distutils \
    && curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py \
    && python3.11 get-pip.py \
    && rm get-pip.py \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置Python别名
RUN ln -sf /usr/bin/python3 /usr/bin/python && \
    ln -sf /usr/bin/pip3 /usr/bin/pip

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用程序代码
COPY app.py .
COPY templates/ templates/
COPY third_party/ third_party/
COPY PyOgg-0.6.14a1.tar.gz .
COPY CosyVoice2-0.5B.zip .
COPY SenseVoiceSmall.zip .

# 创建必要的目录
RUN mkdir -p models/CosyVoice2-0.5B models/SenseVoiceSmall output static

# 设置环境变量
ENV PYTHONPATH=/app:$PYTHONPATH
ENV CUDA_VISIBLE_DEVICES=0

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/ || exit 1

# 启动命令 - 使用bash进入交互式shell而不是直接启动应用
CMD ["/bin/bash"]

# 注意：
# 1. 用户需要自行下载模型文件并挂载到容器的以下目录：
#    - /app/models/CosyVoice2-0.5B
#    - /app/models/SenseVoiceSmall
# 2. 示例运行命令（CPU）进入交互式shell：
#    docker run -it -p 8000:8000 \
#      -v /path/to/models/CosyVoice2-0.5B:/app/models/CosyVoice2-0.5B \
#      -v /path/to/models/SenseVoiceSmall:/app/models/SenseVoiceSmall \
#      chatvoice
# 3. 示例运行命令（GPU）进入交互式shell：
#    docker run -it --gpus all -p 8000:8000 \
#      -v /path/to/models/CosyVoice2-0.5B:/app/models/CosyVoice2-0.5B \
#      -v /path/to/models/SenseVoiceSmall:/app/models/SenseVoiceSmall \
#      chatvoice
#
# 4. 进入容器后，可以手动安装所需的Python包：
#    pip install <package_name>
#
# 5. 然后可以手动启动应用：
#    uvicorn app:app --host 0.0.0.0 --port 8000 --workers 1
#
# 6. 本应用支持Opus音频格式，提供更高效的音频传输和更好的音质

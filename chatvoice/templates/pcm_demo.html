<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PCM语音转文字示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .status.recording {
            background-color: #ffecb3;
        }
        .status.success {
            background-color: #e8f5e9;
        }
        .status.error {
            background-color: #ffebee;
        }
        .visualizer {
            height: 60px;
            display: flex;
            align-items: flex-end;
            gap: 2px;
            margin: 10px 0;
        }
        .visualizer .bar {
            width: 4px;
            background-color: #4CAF50;
            height: 0;
            transition: height 0.1s ease;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 60px;
        }
        .code-block {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>PCM语音转文字示例</h1>
    
    <div class="container">
        <div class="card">
            <h2>录音并转换为文字</h2>
            <div class="controls">
                <button id="startRecordingBtn">开始录音</button>
                <button id="stopRecordingBtn" disabled>停止录音</button>
            </div>
            <div id="visualizer" class="visualizer">
                <!-- 可视化条将在这里动态生成 -->
            </div>
            <div id="status" class="status">准备就绪，点击"开始录音"按钮开始。</div>
            <div>
                <h3>识别结果：</h3>
                <div id="result" class="result">等待录音...</div>
            </div>
        </div>
        
        <div class="card">
            <h2>使用方法</h2>
            <p>本示例展示了如何使用PCM格式通过POST请求进行语音转文字：</p>
            <ol>
                <li>点击"开始录音"按钮开始录音</li>
                <li>说话（确保麦克风已连接并授权）</li>
                <li>点击"停止录音"按钮结束录音</li>
                <li>系统将自动将录音转换为PCM格式并发送到服务器</li>
                <li>识别结果将显示在上方的结果框中</li>
            </ol>
            
            <h3>技术说明</h3>
            <p>本示例使用以下技术：</p>
            <ul>
                <li>Web Audio API进行录音</li>
                <li>将音频转换为PCM格式（16位，16kHz，单声道）</li>
                <li>通过POST请求发送PCM数据到服务器</li>
                <li>服务器返回识别结果</li>
            </ul>
            
            <h3>API调用示例</h3>
            <div class="code-block">
                <pre>// 发送PCM数据
fetch('/api/stt?sample_rate=16000', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/octet-stream'
    },
    body: pcmData // ArrayBuffer或Blob
})
.then(response => response.json())
.then(data => {
    console.log('识别结果:', data.text);
});</pre>
            </div>
        </div>
    </div>

    <script>
        // 获取DOM元素
        const startRecordingBtn = document.getElementById('startRecordingBtn');
        const stopRecordingBtn = document.getElementById('stopRecordingBtn');
        const statusDiv = document.getElementById('status');
        const resultDiv = document.getElementById('result');
        const visualizerDiv = document.getElementById('visualizer');
        
        // 创建可视化条
        for (let i = 0; i < 50; i++) {
            const bar = document.createElement('div');
            bar.className = 'bar';
            visualizerDiv.appendChild(bar);
        }
        
        // 录音相关变量
        let audioContext;
        let mediaRecorder;
        let audioStream;
        let audioProcessor;
        let isRecording = false;
        let pcmData = [];
        let visualizerInterval;
        
        // 开始录音
        async function startRecording() {
            try {
                // 检查浏览器支持
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('您的浏览器不支持录音功能');
                }
                
                // 获取音频流
                audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                // 创建音频上下文
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                
                // 创建音频源
                const source = audioContext.createMediaStreamSource(audioStream);
                
                // 创建处理器节点
                audioProcessor = audioContext.createScriptProcessor(4096, 1, 1);
                
                // 连接节点
                source.connect(audioProcessor);
                audioProcessor.connect(audioContext.destination);
                
                // 处理音频数据
                audioProcessor.onaudioprocess = function(e) {
                    if (!isRecording) return;
                    
                    // 获取音频数据
                    const inputData = e.inputBuffer.getChannelData(0);
                    
                    // 转换为16位PCM
                    const pcmBuffer = new Int16Array(inputData.length);
                    for (let i = 0; i < inputData.length; i++) {
                        // 将-1.0 ~ 1.0的浮点数转换为-32768 ~ 32767的整数
                        pcmBuffer[i] = Math.max(-1, Math.min(1, inputData[i])) * 0x7FFF;
                    }
                    
                    // 存储PCM数据
                    pcmData.push(pcmBuffer);
                    
                    // 更新可视化
                    updateVisualizer(inputData);
                };
                
                // 更新状态
                isRecording = true;
                startRecordingBtn.disabled = true;
                stopRecordingBtn.disabled = false;
                statusDiv.textContent = '正在录音...';
                statusDiv.className = 'status recording';
                resultDiv.textContent = '录音中...';
                
                // 启动可视化更新
                visualizerInterval = setInterval(function() {
                    // 如果没有新数据，逐渐降低所有条的高度
                    const bars = visualizerDiv.children;
                    for (let i = 0; i < bars.length; i++) {
                        const height = parseInt(bars[i].style.height);
                        if (height > 0) {
                            bars[i].style.height = Math.max(0, height - 2) + 'px';
                        }
                    }
                }, 100);
                
            } catch (error) {
                console.error('录音失败:', error);
                statusDiv.textContent = '录音失败: ' + error.message;
                statusDiv.className = 'status error';
            }
        }
        
        // 停止录音
        function stopRecording() {
            if (!isRecording) return;
            
            // 更新状态
            isRecording = false;
            startRecordingBtn.disabled = false;
            stopRecordingBtn.disabled = true;
            statusDiv.textContent = '正在处理...';
            statusDiv.className = 'status';
            
            // 停止可视化更新
            clearInterval(visualizerInterval);
            
            // 停止音频流
            if (audioStream) {
                audioStream.getTracks().forEach(track => track.stop());
            }
            
            // 断开音频处理器
            if (audioProcessor) {
                audioProcessor.disconnect();
            }
            
            // 合并PCM数据
            let totalLength = 0;
            pcmData.forEach(buffer => {
                totalLength += buffer.length;
            });
            
            const mergedPCM = new Int16Array(totalLength);
            let offset = 0;
            
            pcmData.forEach(buffer => {
                mergedPCM.set(buffer, offset);
                offset += buffer.length;
            });
            
            // 发送PCM数据到服务器
            sendPCMData(mergedPCM.buffer);
            
            // 重置PCM数据
            pcmData = [];
        }
        
        // 发送PCM数据到服务器
        function sendPCMData(pcmBuffer) {
            // 设置采样率
            const sampleRate = 16000;
            
            // 创建请求
            fetch(`/api/stt?sample_rate=${sampleRate}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/octet-stream'
                },
                body: pcmBuffer
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('服务器响应错误: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('识别结果:', data);
                if (data.status === 'success') {
                    statusDiv.textContent = '识别成功';
                    statusDiv.className = 'status success';
                    resultDiv.textContent = data.text || '(未识别到文字)';
                } else {
                    throw new Error(data.message || '未知错误');
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                statusDiv.textContent = '识别失败: ' + error.message;
                statusDiv.className = 'status error';
            });
        }
        
        // 更新可视化
        function updateVisualizer(audioData) {
            // 计算音量
            let sum = 0;
            for (let i = 0; i < audioData.length; i++) {
                sum += Math.abs(audioData[i]);
            }
            const average = sum / audioData.length;
            
            // 更新可视化条
            const bars = visualizerDiv.children;
            for (let i = 0; i < bars.length; i++) {
                // 生成随机高度，但与平均音量相关
                const height = Math.min(60, Math.max(2, average * 500 + Math.random() * 20));
                bars[i].style.height = height + 'px';
            }
        }
        
        // 添加事件监听器
        startRecordingBtn.addEventListener('click', startRecording);
        stopRecordingBtn.addEventListener('click', stopRecording);
    </script>
</body>
</html>

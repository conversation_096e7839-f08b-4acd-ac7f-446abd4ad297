<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音转换应用</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .section-title {
            margin-top: 0;
            color: #3498db;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .record-button {
            background-color: #e74c3c;
        }
        .record-button:hover {
            background-color: #c0392b;
        }
        .record-button.recording {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { background-color: #e74c3c; }
            50% { background-color: #c0392b; }
            100% { background-color: #e74c3c; }
        }
        textarea {
            width: 100%;
            min-height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            resize: vertical;
            font-family: inherit;
            font-size: 16px;
            box-sizing: border-box;
        }
        .status {
            margin-top: 10px;
            font-style: italic;
            color: #7f8c8d;
        }
        .error {
            color: #e74c3c;
        }
        .success {
            color: #27ae60;
        }
        .audio-visualizer {
            width: 100%;
            height: 60px;
            background-color: #2c3e50;
            border-radius: 5px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }
        .audio-bars {
            display: flex;
            height: 100%;
            align-items: flex-end;
            justify-content: space-between;
            padding: 0 5px;
        }
        .audio-bar {
            width: 3px;
            background-color: #3498db;
            margin: 0 1px;
            transition: height 0.1s ease;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>语音转换应用</h1>

        <div style="text-align: center; margin-bottom: 20px;">
            <a href="/pcm-demo" style="color: #3498db; text-decoration: none; font-weight: bold;">
                查看PCM格式语音转文字示例 &raquo;
            </a>
        </div>

        <div class="section">
            <h2 class="section-title">语音转文字</h2>
            <div class="audio-visualizer">
                <div class="audio-bars" id="sttVisualizer"></div>
            </div>
            <div class="controls">
                <button id="startRecording" class="record-button">开始录音</button>
                <button id="stopRecording" disabled>停止录音</button>
                <button id="clearStt">清空</button>
            </div>
            <textarea id="sttResult" placeholder="语音识别结果将显示在这里..." readonly></textarea>
            <div id="sttStatus" class="status">准备就绪，点击"开始录音"按钮开始。</div>
        </div>

        <div class="section">
            <h2 class="section-title">文字转语音</h2>
            <div class="audio-visualizer">
                <div class="audio-bars" id="ttsVisualizer"></div>
            </div>
            <div class="controls">
                <button id="speakText">播放</button>
                <button id="stopSpeaking">停止</button>
                <button id="clearTts">清空</button>
            </div>
            <textarea id="ttsInput" placeholder="在这里输入要转换为语音的文字..."></textarea>
            <div id="ttsStatus" class="status">输入文字并点击"播放"按钮。</div>
        </div>
    </div>

    <script>
        // 全局变量
        let sttWebSocket = null;
        let ttsWebSocket = null;
        let mediaRecorder = null;
        let audioContext = null;
        let audioAnalyser = null;
        let recordedChunks = [];
        let isRecording = false;
        let isSpeaking = false;
        let audioQueue = [];
        let currentAudio = null;
        let visualizerIntervalStt = null;
        let visualizerIntervalTts = null;

        // DOM元素
        const startRecordingBtn = document.getElementById('startRecording');
        const stopRecordingBtn = document.getElementById('stopRecording');
        const clearSttBtn = document.getElementById('clearStt');
        const sttResultTextarea = document.getElementById('sttResult');
        const sttStatusDiv = document.getElementById('sttStatus');
        const sttVisualizer = document.getElementById('sttVisualizer');

        const speakTextBtn = document.getElementById('speakText');
        const stopSpeakingBtn = document.getElementById('stopSpeaking');
        const clearTtsBtn = document.getElementById('clearTts');
        const ttsInputTextarea = document.getElementById('ttsInput');
        const ttsStatusDiv = document.getElementById('ttsStatus');
        const ttsVisualizer = document.getElementById('ttsVisualizer');

        // 初始化
        function init() {
            // 创建音频可视化器的条形
            createVisualizer(sttVisualizer);
            createVisualizer(ttsVisualizer);

            // 初始化WebSocket连接
            initWebSockets();

            // 添加事件监听器
            startRecordingBtn.addEventListener('click', startRecording);
            stopRecordingBtn.addEventListener('click', stopRecording);
            clearSttBtn.addEventListener('click', () => {
                sttResultTextarea.value = '';
                sttStatusDiv.textContent = '已清空。';
            });

            speakTextBtn.addEventListener('click', speakText);
            stopSpeakingBtn.addEventListener('click', stopSpeaking);
            clearTtsBtn.addEventListener('click', () => {
                ttsInputTextarea.value = '';
                ttsStatusDiv.textContent = '已清空。';
            });

            // 实时输入转语音
            ttsInputTextarea.addEventListener('input', debounce(handleTextInput, 500));
        }

        // 创建可视化器
        function createVisualizer(container) {
            // 创建30个条形
            for (let i = 0; i < 30; i++) {
                const bar = document.createElement('div');
                bar.className = 'audio-bar';
                bar.style.height = '0px';
                container.appendChild(bar);
            }
        }

        // 更新可视化器
        function updateVisualizer(container, dataArray) {
            const bars = container.children;
            const step = Math.floor(dataArray.length / bars.length);

            for (let i = 0; i < bars.length; i++) {
                const value = dataArray[i * step] / 255;
                const height = value * 100;
                bars[i].style.height = `${height}%`;
            }
        }

        // 初始化WebSocket连接
        function initWebSockets() {
            try {
                // 获取WebSocket协议（如果是HTTPS则使用WSS，否则使用WS）
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const host = window.location.host;

                // 语音转文字WebSocket
                const sttWsUrl = `${protocol}//${host}/ws/stt`;
                console.log(`正在连接STT WebSocket: ${sttWsUrl}`);

                sttWebSocket = new WebSocket(sttWsUrl);

                sttWebSocket.onopen = () => {
                    console.log('STT WebSocket连接已建立');
                    sttStatusDiv.textContent = 'WebSocket连接已建立，准备就绪。';
                };

                sttWebSocket.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'stt_result') {
                            sttResultTextarea.value = data.text;
                            sttStatusDiv.textContent = '语音识别完成。';
                            sttStatusDiv.className = 'status success';
                        } else if (data.type === 'error') {
                            sttStatusDiv.textContent = `错误: ${data.message}`;
                            sttStatusDiv.className = 'status error';
                        }
                    } catch (e) {
                        console.error('处理STT WebSocket消息时出错:', e);
                    }
                };

                sttWebSocket.onerror = (error) => {
                    console.error('STT WebSocket错误:', error);
                    sttStatusDiv.textContent = 'WebSocket连接错误。请确保服务器正在运行。';
                    sttStatusDiv.className = 'status error';
                };

                sttWebSocket.onclose = () => {
                    console.log('STT WebSocket连接已关闭');
                    sttStatusDiv.textContent = 'WebSocket连接已关闭。';
                };

                // 文字转语音WebSocket
                const ttsWsUrl = `${protocol}//${host}/ws/tts`;
                console.log(`正在连接TTS WebSocket: ${ttsWsUrl}`);

                ttsWebSocket = new WebSocket(ttsWsUrl);

                ttsWebSocket.onopen = () => {
                    console.log('TTS WebSocket连接已建立');
                    ttsStatusDiv.textContent = 'WebSocket连接已建立，准备就绪。';
                };

                // 用于存储音频格式和内容类型
                let audioFormat = 'wav';
                let audioContentType = 'audio/wav';
                let pendingAudioBlob = null;

                ttsWebSocket.onmessage = (event) => {
                    if (event.data instanceof Blob) {
                        // 接收音频数据
                        const audioBlob = new Blob([event.data], { type: audioContentType });

                        // 如果有待处理的音频块，先处理它
                        if (pendingAudioBlob) {
                            playAudioChunk(pendingAudioBlob);
                            pendingAudioBlob = null;
                        }

                        // 处理当前音频块
                        playAudioChunk(audioBlob);
                    } else {
                        try {
                            const data = JSON.parse(event.data);
                            if (data.type === 'audio_format') {
                                // 设置音频格式
                                audioFormat = data.format;
                                console.log(`音频格式设置为: ${audioFormat}`);
                            } else if (data.type === 'audio_data') {
                                // 设置内容类型
                                audioContentType = data.content_type || 'audio/wav';
                                console.log(`音频内容类型: ${audioContentType}`);

                                // 下一个二进制消息将是音频数据
                                // 我们不需要做任何事情，因为下一个消息会触发Blob处理
                            } else if (data.type === 'tts_end') {
                                ttsStatusDiv.textContent = '语音生成完成。';
                                ttsStatusDiv.className = 'status success';
                                isSpeaking = false;
                            } else if (data.type === 'error') {
                                ttsStatusDiv.textContent = `错误: ${data.message}`;
                                ttsStatusDiv.className = 'status error';
                            }
                        } catch (e) {
                            console.error('处理TTS WebSocket消息时出错:', e);
                        }
                    }
                };

                ttsWebSocket.onerror = (error) => {
                    console.error('TTS WebSocket错误:', error);
                    ttsStatusDiv.textContent = 'WebSocket连接错误。请确保服务器正在运行。';
                    ttsStatusDiv.className = 'status error';
                };

                ttsWebSocket.onclose = () => {
                    console.log('TTS WebSocket连接已关闭');
                    ttsStatusDiv.textContent = 'WebSocket连接已关闭。';
                };
            } catch (error) {
                console.error('初始化WebSocket时出错:', error);
                sttStatusDiv.textContent = `WebSocket初始化错误: ${error.message}`;
                sttStatusDiv.className = 'status error';
                ttsStatusDiv.textContent = `WebSocket初始化错误: ${error.message}`;
                ttsStatusDiv.className = 'status error';
            }
        }

        // 开始录音
        async function startRecording() {
            try {
                // 检查navigator.mediaDevices是否存在
                if (!navigator.mediaDevices) {
                    // 对于较旧的浏览器，尝试使用适配器
                    navigator.mediaDevices = {};
                }

                // 检查getUserMedia是否存在
                if (!navigator.mediaDevices.getUserMedia) {
                    navigator.mediaDevices.getUserMedia = function(constraints) {
                        // 首先尝试使用较旧的API
                        const getUserMedia = navigator.webkitGetUserMedia || navigator.mozGetUserMedia;

                        // 如果浏览器不支持，则抛出错误
                        if (!getUserMedia) {
                            sttStatusDiv.textContent = '您的浏览器不支持音频录制功能。请使用Chrome、Firefox或Edge的最新版本。';
                            sttStatusDiv.className = 'status error';
                            return Promise.reject(new Error('getUserMedia is not implemented in this browser'));
                        }

                        // 使用旧API的Promise包装
                        return new Promise(function(resolve, reject) {
                            getUserMedia.call(navigator, constraints, resolve, reject);
                        });
                    }
                }

                console.log('正在请求麦克风权限...');
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                console.log('麦克风权限已获取');

                // 设置音频分析器
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const source = audioContext.createMediaStreamSource(stream);
                audioAnalyser = audioContext.createAnalyser();
                audioAnalyser.fftSize = 256;
                source.connect(audioAnalyser);

                // 开始可视化
                const dataArray = new Uint8Array(audioAnalyser.frequencyBinCount);
                visualizerIntervalStt = setInterval(() => {
                    audioAnalyser.getByteFrequencyData(dataArray);
                    updateVisualizer(sttVisualizer, dataArray);
                }, 50);

                // 设置MediaRecorder
                // 尝试使用WAV格式，如果不支持则回退到其他格式
                try {
                    mediaRecorder = new MediaRecorder(stream, { mimeType: 'audio/wav' });
                    console.log('使用audio/wav格式录音');
                } catch (e) {
                    try {
                        mediaRecorder = new MediaRecorder(stream, { mimeType: 'audio/webm' });
                        console.log('使用audio/webm格式录音');
                    } catch (e2) {
                        console.log('不支持audio/wav和audio/webm格式，使用默认格式');
                        mediaRecorder = new MediaRecorder(stream);
                    }
                }

                recordedChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);

                        // 将数据发送到WebSocket
                        if (sttWebSocket && sttWebSocket.readyState === WebSocket.OPEN) {
                            const reader = new FileReader();
                            reader.onload = () => {
                                console.log('发送音频数据，类型:', event.data.type, '大小:', event.data.size);
                                sttWebSocket.send(reader.result);
                            };
                            reader.readAsArrayBuffer(event.data);
                        }
                    }
                };

                mediaRecorder.onstop = () => {
                    // 停止录音后的处理
                    if (sttWebSocket && sttWebSocket.readyState === WebSocket.OPEN) {
                        sttWebSocket.send('end_recording');
                    }

                    // 停止可视化
                    clearInterval(visualizerIntervalStt);
                    const bars = sttVisualizer.children;
                    for (let i = 0; i < bars.length; i++) {
                        bars[i].style.height = '0px';
                    }

                    // 停止音频流
                    stream.getTracks().forEach(track => track.stop());
                };

                // 开始录音
                mediaRecorder.start(100); // 每100ms发送一次数据
                isRecording = true;

                // 更新UI
                startRecordingBtn.disabled = true;
                stopRecordingBtn.disabled = false;
                startRecordingBtn.classList.add('recording');
                sttStatusDiv.textContent = '正在录音...';
                sttStatusDiv.className = 'status';

            } catch (error) {
                console.error('获取麦克风权限时出错:', error);
                sttStatusDiv.textContent = `无法访问麦克风: ${error.message}`;
                sttStatusDiv.className = 'status error';

                // 提供更多帮助信息
                if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
                    sttStatusDiv.textContent += ' 请允许浏览器访问麦克风。';
                } else if (error.name === 'NotFoundError' || error.name === 'DevicesNotFoundError') {
                    sttStatusDiv.textContent += ' 未找到麦克风设备，请确保麦克风已连接。';
                } else if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {
                    sttStatusDiv.textContent += ' 无法读取麦克风，可能被其他应用程序占用。';
                } else if (error.name === 'OverconstrainedError' || error.name === 'ConstraintNotSatisfiedError') {
                    sttStatusDiv.textContent += ' 麦克风不满足要求的限制条件。';
                } else if (error.name === 'TypeError') {
                    sttStatusDiv.textContent += ' 浏览器不支持指定的媒体类型。';
                }
            }
        }

        // 停止录音
        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;

                // 更新UI
                startRecordingBtn.disabled = false;
                stopRecordingBtn.disabled = true;
                startRecordingBtn.classList.remove('recording');
                sttStatusDiv.textContent = '正在处理语音...';

                // 添加加载指示器
                const loader = document.createElement('span');
                loader.className = 'loading';
                sttStatusDiv.appendChild(loader);
            }
        }

        // 播放文字
        function speakText() {
            const text = ttsInputTextarea.value.trim();
            if (!text) {
                ttsStatusDiv.textContent = '请输入要转换为语音的文字。';
                ttsStatusDiv.className = 'status error';
                return;
            }

            if (ttsWebSocket && ttsWebSocket.readyState === WebSocket.OPEN) {
                // 清空之前的音频队列
                stopSpeaking();

                // 发送文本到WebSocket
                ttsWebSocket.send(JSON.stringify({ text }));
                isSpeaking = true;

                // 更新UI
                ttsStatusDiv.textContent = '正在生成语音...';
                ttsStatusDiv.className = 'status';

                // 添加加载指示器
                const loader = document.createElement('span');
                loader.className = 'loading';
                ttsStatusDiv.appendChild(loader);

                // 模拟音频可视化
                startTtsVisualizer();
            } else {
                ttsStatusDiv.textContent = 'WebSocket连接未建立。';
                ttsStatusDiv.className = 'status error';
            }
        }

        // 停止播放
        function stopSpeaking() {
            // 停止当前播放的音频
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
            }

            // 清空音频队列
            audioQueue = [];
            isSpeaking = false;

            // 停止可视化
            clearInterval(visualizerIntervalTts);
            const bars = ttsVisualizer.children;
            for (let i = 0; i < bars.length; i++) {
                bars[i].style.height = '0px';
            }

            // 更新UI
            ttsStatusDiv.textContent = '已停止播放。';
            ttsStatusDiv.className = 'status';
        }

        // 播放音频块
        function playAudioChunk(audioBlob) {
            const audioUrl = URL.createObjectURL(audioBlob);
            const audio = new Audio(audioUrl);

            // 将音频添加到队列
            audioQueue.push(audio);

            // 如果当前没有播放音频，开始播放
            if (!currentAudio) {
                playNextAudio();
            }
        }

        // 播放下一个音频
        function playNextAudio() {
            if (audioQueue.length > 0) {
                currentAudio = audioQueue.shift();
                currentAudio.onended = () => {
                    URL.revokeObjectURL(currentAudio.src);
                    currentAudio = null;
                    playNextAudio();
                };
                currentAudio.play().catch(error => {
                    console.error('播放音频时出错:', error);
                    currentAudio = null;
                    playNextAudio();
                });
            }
        }

        // 开始TTS可视化器
        function startTtsVisualizer() {
            clearInterval(visualizerIntervalTts);

            visualizerIntervalTts = setInterval(() => {
                if (isSpeaking) {
                    const bars = ttsVisualizer.children;
                    for (let i = 0; i < bars.length; i++) {
                        // 随机高度模拟音频波形
                        const height = Math.random() * 80 + 20;
                        bars[i].style.height = `${height}%`;
                    }
                } else {
                    clearInterval(visualizerIntervalTts);
                    const bars = ttsVisualizer.children;
                    for (let i = 0; i < bars.length; i++) {
                        bars[i].style.height = '0px';
                    }
                }
            }, 100);
        }

        // 处理文本输入（实时转语音）
        function handleTextInput() {
            const text = ttsInputTextarea.value.trim();
            if (text && ttsWebSocket && ttsWebSocket.readyState === WebSocket.OPEN) {
                // 发送文本到WebSocket
                ttsWebSocket.send(JSON.stringify({ text }));
                isSpeaking = true;

                // 更新UI
                ttsStatusDiv.textContent = '正在生成语音...';

                // 模拟音频可视化
                startTtsVisualizer();
            }
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    func.apply(context, args);
                }, wait);
            };
        }

        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>

import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { getUserInfo } from '../../services/user.ts';
import { setAuthToken } from '../../utils/request.ts';
import { replaceAiPortalDomain } from '../../services/configService.ts';


const QywxLogin: React.FC = () => {
  const location = useLocation();
  const uid = new URLSearchParams(location.search).get('uid') || '';

  useEffect(() => {
    console.log('uid', uid);
    if (uid) {
      // 获取URL中的token参数
      const token = new URLSearchParams(location.search).get('token');
      if (token) {
        // 如果有token，存储到localStorage
        setAuthToken(token);
      }
      // 这里可以发起请求，但为了示例，我们只设置loading状态
      getUserInfo().then((res) => {
        if (res) {
            window.location.href = replaceAiPortalDomain('/qy_wechat_h5/ai-portal/index.html#/home');
        }
      });
    } else {
      // 延迟 10秒跳转
      // setTimeout(() => {
      //     window.location.href = '/api/qywx/login';
      // }, 20000);
      // console.log(replaceAiPortalDomain('/qy_wechat_h5/ai-portal/enclosure/qywx/login'))
      window.location.href = replaceAiPortalDomain('/qy_wechat_h5/ai-portal/enclosure/qywx/login');
    }
  }, [uid]);

  return (
    <div className="w-full h-full flex justify-center items-center">
      <h1>正在登录。。。</h1>
    </div>
  );
};

export default QywxLogin;

import {FC, useEffect, useRef, useState} from "react";
import {<PERSON><PERSON>, Button, ConfigProvider, Divider, Drawer, Dropdown, Form, GetProp, Image, Input, Layout, MenuProps, Modal, Space, Spin, Tooltip, Typography, message as antdMessage, Badge, Popover} from "antd";
import {createStyles} from "antd-style";
import {Content, Header} from "antd/lib/layout/layout";
import {AlignLeftOutlined, ArrowRightOutlined, CloudUploadOutlined, CopyOutlined, DeleteOutlined, DislikeFilled, DislikeOutlined, DownOutlined, EditOutlined, FileAddOutlined, LikeFilled, LikeOutlined, MenuUnfoldOutlined, MoreOutlined, PaperClipOutlined, PlusOutlined, PushpinOutlined} from "@ant-design/icons";
import {Attachments, Bubble, BubbleProps, Conversations, ConversationsProps, PromptProps, Prompts, Sender, useXAgent, useXChat, Welcome, XStream} from "@ant-design/x";
import {getUserInfo, logout} from "../../services/user.ts";
import {Account} from "../../types/account.ts";

import markdownit from 'markdown-it';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import welcomeIcon from './img/fmt.webp';
import logoIcon from './img/logo.png';
import {AgentDict, getColorPairByBgColor} from "../../types/agentDict.ts";
import {Conversation} from "@ant-design/x/lib/conversations";
import {AgentMessage, RetrieverResource, StreamResponse} from "../../types/streamResponse.ts";
import {streamApi, upload, message as getAgentMessage, parameters, suggested, stop, feedback, closeAllEventSources} from "../../services/difyApi.ts";
import {AgentParam} from "../../types/agentParam.ts";
import {deleteChatHistory, getChatHistoryList, renameChatHistory, topChatHistory} from "../../services/chatHistory.ts";
import {DifyFile} from "../../types/difyFile.ts";
import {RcFile} from "antd/lib/upload";
import {MessageInfo} from "@ant-design/x/lib/use-x-chat";
import {buttonTheme} from "../../theme.tsx";
import {getAgentDictByIsDefaultTrue, getMobileAgentParam} from "../../services/agentDict.ts";

import './style.css';
import {replaceAiPortalDomain} from "../../services/configService.ts";


const md = markdownit({ html: true, breaks: true });


const useStyle = createStyles(({ token, css }) => {
  return {
    layout: css`
      width: 100%;
      min-width: 1000px;
      height: 100%;
      display: flex;
      background: ${token.colorBgContainer};
      font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;

      .ant-prompts {
        color: ${token.colorText};
      }
    `,
    menu: css`
      background: ${token.colorBgLayout}80;
      width: 280px;
      height: 100%;
      display: flex;
      flex-direction: column;
    `,
    conversations: css`
      padding: 0 12px;
      flex: 1;
      overflow-y: auto;
      height: 100%;

      scrollbar-color: #888 transparent;
      scrollbar-width: thin;

      li {
        padding-left: 16px !important;
      }
    `,
    conversationsMenu: css`
      color: rgba(0, 0, 0, 0.88);
    `,
    chat: css`
      height: 100%;
      width: 100%;
      max-width: 700px;
      margin: 0 auto;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      padding: ${token.paddingLG}px;
      gap: 16px;
    `,
    messages: css`
      ::-webkit-scrollbar {
        width: 12px;
        height: 12px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
      }

      ::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 10px;
        border: 2px solid #f1f1f1;
      }

      /* 基础样式 */
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      transition: all 0.5s ease-in-out;
      position: relative;
    `,
    messagesExpanded: css`
      /* 当消息列表展开时的样式 */
      flex: 1 !important;
      transform: translateY(0);
      opacity: 1;
    `,
    messagesCollapsed: css`
      /* 当消息列表收起时的样式 */
      flex: none !important;
      transform: translateY(25vh);
      opacity: 0.9;
    `,
    placeholder: css`
      //padding-top: 32px;
      //margin-top: 25vh;
      width: 100%;
    `,
    sender: css`
      box-shadow: ${token.boxShadow};
      transition: all 0.5s ease-in-out;
      position: relative;
    `,
    logo: css`
      display: flex;
      height: 72px;
      align-items: center;
      justify-content: start;
      padding: 0 24px;
      box-sizing: border-box;

      img {
        width: 24px;
        height: 24px;
        display: inline-block;
      }

      span {
        display: inline-block;
        margin: 0 8px;
        font-weight: bold;
        color: ${token.colorText};
        font-size: 16px;
      }
    `,
    addBtn: css`
      background: #1677ff0f;
      border: 1px solid #1677ff34;
      width: calc(100% - 24px);
      margin: 0 12px 0 12px;
    `,
    suggestionPrompts: css`
      background-color: rgba(0, 0, 0, 0.04) !important;
      border-radius: 12px !important;
      color: rgba(0, 0, 0, 0.7) !important;
      padding: 10px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
        color: rgba(0, 0, 0) !important;
      }
    `
    }
})


const Independent: FC = () => {
    // ==================== Style ====================
    const { styles } = useStyle();

    // ==================== State ====================

    const [userItem, setUserItem] = useState<Account | null>(null);

    const [isShowSpin, setIsShowSpin] = useState(false);

    const [headerOpen, setHeaderOpen] = useState(false);

    const [content, setContent] = useState('');

    const [renameModalType, setRenameModalType] = useState<'rename' | 'topChat'>('rename');

    const [llmDefaultSelect, setLlmDefaultSelect] = useState(localStorage.getItem('llmDefaultSelect') || 'Qwen2.5-32B-instruct');

    const [conversationsItems, setConversationsItems] = useState<Conversation[]>([]);

    const [activeKey, setActiveKey] = useState<string>('auto');

    const [attachedFiles, setAttachedFiles] = useState<GetProp<typeof Attachments, 'items'>>(
        []
    );

    const [chatNameForm] = Form.useForm<{
        name: string;
    }>();

    const [isShowRenameModal, setIsShowRenameModal] = useState(false);

    const [isBeRefreshConversations, setIsBeRefreshConversations] = useState(false);
    const isBeRefreshConversationsRef = useRef(isBeRefreshConversations);

    const [messageFeedBackDict, setMessageFeedBackDict] = useState<{
        [key: string]: string | null;
    }>({});

    useEffect(() => {
        isBeRefreshConversationsRef.current = isBeRefreshConversations;
    }, [isBeRefreshConversations]);

    // ==================== Drawer ====================

    const [drawerOpen, setDrawerOpen] = useState(false);


    // ==================== Agent ====================

    const [currentAgent, setCurrentAgent] = useState<AgentDict | null>(null);

    const [currentAgentParam, setCurrentAgentParam] = useState<AgentParam | null>(null);

    const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);

    const [beRenameConversionId, setBeRenameConversionId] = useState<string | null>(null);

    const [beLoadHistoryMessageConversationId, setBeLoadHistoryMessageConversationId] = useState('');

    useEffect(() => {
        if (currentAgent) {
            // 加载Agent配置
            // 加载历史聊天记录

            if (beLoadHistoryMessageConversationId) {
                loadAgentParameters(currentAgent).then((agentParam) => {
                    loadHistoryMessage(beLoadHistoryMessageConversationId).then(() => {
                        setBeLoadHistoryMessageConversationId('')
                    });
                })
            } else {
                Promise.all([loadAgentParameters(currentAgent), loadConversation()]).then(
                    ([agentParam, conversations]) => {
                        setActiveKey(`auto-${conversations.length}`);
                        initializeMessage(agentParam);
                        if (content) {
                            onSubmit(content);
                        }
                    }
                );
            }
        }
    }, [currentAgent]);

    const renderMarkdown: BubbleProps['messageRender'] = (content) => (
        <Typography>
            {/* biome-ignore lint/security/noDangerouslySetInnerHtml: used in demo */}
            <div dangerouslySetInnerHTML={{ __html: md.render(content) }} />
        </Typography>
    );

    const [agent] = useXAgent<AgentMessage>({
        request: async ({ message }, { onSuccess, onUpdate, onError }) => {
            if (!message) {
                onError(new Error('No message'));
                return;
            }

            if (!message.content || !message.agentId) {
                onError(new Error('No message or agent'));
                return;
            }

            let isDoReplaceStartThink = true;
            let isDoReplaceEndThink = false;

            let content = '';
            const conversationId = message.conversationId?.startsWith('auto')
                ? undefined
                : message.conversationId;

            onUpdate({
                type: 'ai',
                content: '',
                suggested: message.suggested
            });

            const stream = XStream({
                readableStream: streamApi(
                    message.content,
                    message.agentId,
                    conversationId,
                    message.fileList,
                    message.inputs
                )
            });

            const reader = stream.getReader();
            cancelRef.current = () => {
                reader?.cancel();
                if (!content) {
                    onError(new Error('No message or agent'));
                }
            };

            while (reader) {
                const { value, done } = await reader.read();

                if (!value && done) {
                    onSuccess({
                        id: "",
                        type: 'ai',
                        content: content,
                        suggested: message.suggested
                    });
                    break;
                }

                if (!value) continue;

                const response: StreamResponse = JSON.parse(value.data);

                if (done) {
                    onSuccess({
                        id: response.message_id,
                        type: 'ai',
                        content: content,
                        suggested: message.suggested
                    });
                    break;
                }

                // console.log('response:', response);

                switch (value.event) {
                    case 'workflow_started':
                        updateConversionId.current(response.conversation_id);
                        updateTaskId.current(response.task_id);
                        break;
                    case 'workflow_finished':
                        break;
                    case 'node_started':
                        break;
                    case 'node_finished':
                        break;
                    case 'message':
                        content += response.answer;

                        if (content.length > 10 && isDoReplaceStartThink) {
                            if (content.startsWith("<think>")) {
                                content = content.replace(/<think>/g, '<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 12px;" open> <summary> 思考过程... </summary>');
                                isDoReplaceStartThink = false;
                                isDoReplaceEndThink = true;
                            }
                        } else if (isDoReplaceEndThink && content.indexOf("</think>") > 5) {
                            content = content.replace(/<\/think>/g, '</details>');
                            isDoReplaceEndThink = false;
                        }

                        onUpdate({
                            id: response.message_id,
                            type: 'ai',
                            content: content,
                            suggested: message.suggested
                        });
                        break;
                    case 'message_end':
                    {
                        // console.log('success content:', content);

                        let retrieverResources = [];
                        // 获取 metadata 里面的内容，看看有没有引用
                        if (
                            response.metadata &&
                            response.metadata.retriever_resources &&
                            response.metadata.retriever_resources.length > 0
                        ) {
                            retrieverResources = response.metadata.retriever_resources;
                        }

                        let suggestion = [];
                        // 判断agent参数
                        if (message.suggested) {
                            onUpdate({
                                id: response.message_id,
                                type: 'ai',
                                content: content,
                                suggested: message.suggested
                            });
                            // 获取下一步建议
                            const suggests = await suggested(response.message_id, message.agentId);
                            // console.log('suggests:', suggests);
                            suggestion = suggests.data;
                        }

                        onSuccess({
                            id: response.message_id,
                            type: 'ai',
                            content: content,
                            suggested: message.suggested,
                            list: suggestion,
                            retrieverResources: retrieverResources
                        });
                    }
                        break;
                    case 'error':
                        break;
                }
            }
        }
    });

    const { onRequest, messages, setMessages, parsedMessages } = useXChat({
        agent,
        parser: (msg) => {
            if (msg.list && msg.list.length > 0) {
                const array = [
                    {
                        id: msg.id,
                        type: msg.type,
                        content: msg.content,
                        retrieverResources: msg.retrieverResources
                    },
                    {
                        id: msg.id,
                        type: 'suggestion',
                        content: msg.list
                    }
                ];
                return array;
            }

            return msg;
        }
    });

    const items: GetProp<typeof Bubble.List, 'items'> = parsedMessages.map(
        ({ id, message, status }) => {
            // console.log('messages:', message, message.content, message.content.length);

            let content;
            let loading = false; // 默认 loading 为 false
            let isShowFooter = true; // 控制页脚可见性的标志
            let lId = id; // 如果消息有 id，使用消息 id 作为 key

            if (message.id) {
                lId = message.id;
            }

            // --- 根据消息类型和状态确定内容和 loading 状态 ---
            if (message.type === 'ai') {

                // 如果内容可用，渲染 markdown 内容
                if (message.content && typeof message.content === 'string' && message.content.length > 0) {
                    content = renderMarkdown(message.content);
                } else if (status === 'success') {
                    content = ''; // 让 Bubble 处理打字动画显示
                    isShowFooter = false; // 打字时隐藏页脚
                } else {
                    loading = true;
                }

                // 处理成功 AI 消息的检索资源
                if (
                    status === 'success' && // 只为成功消息显示资源
                    currentAgentParam?.retriever_resource?.enabled &&
                    message.retrieverResources &&
                    message.retrieverResources.length > 0
                ) {
                    loading = false; // 显示资源时确保 loading 为 false
                    // 按文档 ID 分组检索资源
                    const grouped: Record<string, RetrieverResource[]> = {};
                    message.retrieverResources.forEach((reteriever) => {
                        const key = reteriever.document_id;
                        if (!grouped[key]) {
                            grouped[key] = [];
                        }
                        grouped[key].push(reteriever);
                    });

                    // 结合 markdown 内容和检索链接
                    content = (
                        <>
                            {renderMarkdown(message.content)}
                            <div>
                                <Divider
                                    style={{ margin: '5px 0', borderColor: 'rgba(0,0,0,.25)' }}
                                    orientation="left"
                                >
                                    引用
                                </Divider>
                                <div>{retrieverPopover(grouped)}</div>
                            </div>
                        </>
                    );
                }

            } else if (message.type === 'local') {

                // 渲染用户消息内容和附件
                if (message.content && typeof message.content === 'string' && message.content.length > 0) {
                    if (message.fileList && message.fileList.length > 0) {
                        content = (
                            <>
                                {/* 映射并渲染附件 (图片或文档) */}
                                {message.fileList.map((file, fileIndex) => {
                                    let dom;
                                    if (file.type === 'document') {
                                        dom = (<Attachments.FileCard key={file.file.uid || fileIndex} item={file.file} />);
                                    } else {
                                        dom = (
                                            <Image.PreviewGroup key={fileIndex}>
                                                <Image
                                                    width={100}
                                                    src={file.url ? file.url : (file.file.originFileObj ? URL.createObjectURL(file.file.originFileObj) : '')} // 使用 URL 或 createObjectURL
                                                    style={{ marginRight: '10px' }}
                                                />
                                            </Image.PreviewGroup>
                                        );
                                    }
                                    return dom;
                                })}
                                {/* 渲染用户的文本内容 */}
                                {renderMarkdown(message.content)}
                            </>
                        );
                    } else {
                        // 只渲染用户的文本内容
                        content = renderMarkdown(message.content);
                    }
                } else if (loading) {
                    // 本地消息正在加载但没有内容 (例如，发送没有文本的文件)
                    content = '发送中...';
                    isShowFooter = false; // 发送时隐藏页脚
                } else {
                    // 如果不是加载中 (发送成功)，应该有内容
                    content = ''; // 或者处理空消息的情况
                }

            } else if (message.type === 'suggestion') {
                // 处理建议消息
                content = message.content; // 内容是建议数组
                loading = false; // 建议不加载
                isShowFooter = false; // 建议没有页脚
            } else {
                // 处理其他潜在的消息类型或初始状态
                content = message.content;
                loading = false;
                isShowFooter = false;
            }

            // --- 确定页脚可见性和内容 ---
            let footer = null;
            // 仅当不加载且 isShowFooter 为 true (且状态为 success 用于最终内容，如反馈) 时显示页脚
            // 根据您希望页脚何时出现来调整此条件 (例如，仅用于已完成的 AI 回复)
            if (!loading && isShowFooter && status === 'success' && message.type === 'ai') { // 仅为已完成的 AI 消息显示页脚
                footer = (
                    <div
                        style={{ display: 'flex', justifyContent: 'space-between', width: '96%', marginLeft: 10 }}
                    >
                        <div>
                            {/* 复制按钮 */}
                            <Tooltip title="复制">
                                <CopyToClipboard text={typeof message.content === 'string' ? message.content : ''} onCopy={() => {
                                    antdMessage.success('内容已复制到剪贴板');
                                }}>
                                    <Button
                                        color="default"
                                        variant="text"
                                        size="small"
                                        icon={<CopyOutlined />}
                                    />
                                </CopyToClipboard>
                            </Tooltip>
                        </div>
                        {/* 反馈按钮 (点赞/点踩) */}
                        <div className="flex items-center">
                            <Tooltip title={messageFeedBackDict[lId] === 'like' ? '取消点赞' : '点赞'}>
                                <Button
                                    color="default"
                                    variant="text"
                                    size="small"
                                    icon={messageFeedBackDict[lId] === 'like' ? <LikeFilled style={{color: '#1890ff'}} /> : <LikeOutlined />}
                                    onClick={() => handleFeedback(lId, messageFeedBackDict[lId] === 'like' ? null : 'like')}
                                    style={{ marginRight: 10}}
                                />
                            </Tooltip>
                            <Tooltip title={messageFeedBackDict[lId] === 'dislike' ? '取消点踩' : '点踩'}>
                                <Button
                                    color="default"
                                    variant="text"
                                    size="small"
                                    icon={messageFeedBackDict[lId] === 'dislike' ? <DislikeFilled style={{color: '#dc2626'}} /> : <DislikeOutlined />}
                                    onClick={() => handleFeedback(lId, messageFeedBackDict[lId] === 'dislike' ? null : 'dislike')}
                                />
                            </Tooltip>
                        </div>
                    </div>
                );
            }

            // 在 AI 消息成功后延迟加载对话 (原逻辑)
            // 这可能需要根据您期望的行为进行调整
            if (status === 'success' && message.type === 'ai' && id !== 'welcome' && currentAgent) {
                setTimeout(() => {
                    loadConversation(activeKey, currentAgent.id).then()
                }, 1000)
            }

            // 返回用于 Bubble.List 的消息项结构
            return {
                key: lId, // 消息项的唯一 key
                loading: loading, // 传递确定的 loading 状态
                role: message.type, // 使用 message.type 作为角色 ('ai', 'local', 'suggestion')
                content: content, // 要显示的消息内容
                footer: footer, // 消息页脚 (复制、反馈等)
            };
        }
    );

    // ========================== ServerFunction =========================

    const loadConversation = async (conversationId?: string, agentId?: string) => {
        // if (!agent) {
        //   return;
        // }

        if (conversationId && conversationId.startsWith('auto')) {
            return [];
        }

        // console.log('loadConversation', isBeRefreshConversationsRef.current)
        if (conversationId && agentId && !isBeRefreshConversationsRef.current) {
            return [];
        }

        setIsBeRefreshConversations(false);

        try {
            const conversations = await getChatHistoryList(conversationId, agentId);
            // console.log('Conversations:', conversations);
            if (conversations && conversations.length > 0) {
                // 转换格式
                const formattedConversations = conversations.map((conversation) => ({
                        key: conversation.id,
                        label: conversation.name,
                        isTop: conversation.isTop,
                        conversationId: conversation.conversationId,
                        agentDict: conversation.agentDict,
                        timestamp: new Date(conversation.createAt).getTime(),
                        icon:
                            conversation.agentDict.isDefault ?
                                (<Avatar size="small" style={{ backgroundColor: 'rgb(243, 244, 246)'}}><AlignLeftOutlined style={{ color: 'rgba(0, 0, 0, 0.88)' }} /></Avatar>)
                                :
                                (<Avatar size="small" style={{ backgroundColor: conversation.agentDict.iconColor || '#dfdff8', color: getColorPairByBgColor(conversation.agentDict.iconColor).textColor }}>
                                    {conversation.agentDict.name ? conversation.agentDict.name[0] : '?'}
                                </Avatar>)
                    })
                );

                // console.log('Formatted Conversations:', formattedConversations);
                setConversationsItems(formattedConversations);
                return formattedConversations;
            } else {
                setConversationsItems([]);
                return [];
            }
        } catch (ex) {
            console.error('Error loading conversations:', ex);
        }
        return [];
    };

    const loadAgentParameters = async (agent: AgentDict) => {
        if (!agent) {
            return;
        }

        try {
            const agentParam = await parameters(agent.id);
            // console.log('agentParam:', agentParam);
            setCurrentAgentParam(agentParam);
            return agentParam;
        } catch (e) {
            console.error('Error loading agent parameters:', e);
            antdMessage.error('加载参数失败');
            return null;
        }
    };

    const loadHistoryMessage = async (conversationId: string) => {
        if (!currentAgent || !conversationId) {
            return;
        }

        const messageRes = await getAgentMessage(currentAgent.id, conversationId);
        // console.log('messageRes:', messageRes);

        if (messageRes.data && messageRes.data.length > 0) {
            setMessageFeedBackDict({});

            const array: MessageInfo<AgentMessage>[] = [];
            messageRes.data.forEach((message: any) => {
                let userFiles = [];
                let aiFiles = [];
                if (message.message_files && message.message_files.length > 0) {
                    // 找message.message_files中 belongs_to === 'user'的对象，和 belongs_to ！== 'user'的对象,拆分成两个集合
                    userFiles = message.message_files.filter((file: any) => file.belongs_to === 'local');
                    // 循环 userFiles，处理里面的url 地址，需要加上 currentAgent的apiServerHost，并去掉'/v1'
                    if (userFiles.length > 0) {
                        userFiles.forEach((file: any) => {
                            file.url = currentAgent.apiServerHost.replace('/v1', '') + file.url;
                        });
                    }

                    aiFiles = message.message_files.filter((file: any) => file.belongs_to !== 'local');
                    if (aiFiles.length > 0) {
                        aiFiles.forEach((file: any) => {
                            file.url = currentAgent.apiServerHost.replace('/v1', '') + file.url;
                        });
                    }
                }

                if (message.feedback && message.feedback.rating) {
                    setMessageFeedBackDict((prev) => ({
                        ...prev,
                        [message.id]: message.feedback.rating
                    }));
                }

                array.push(
                    {
                        id: message.id,
                        message: {
                            type: 'local',
                            content: message.query,
                            suggested: false,
                            fileList: userFiles
                        },
                        status: 'local'
                    },
                    {
                        id: message.id,
                        message: {
                            type: 'ai',
                            content: message.answer,
                            suggested: false,
                            fileList: aiFiles,
                            feedback: message.feedback
                        },
                        status: 'success'
                    }
                );
            });
            setMessages(array);
        }
    };

    const initializeMessage = (agentParam: AgentParam) => {
        // console.log('initializeMessage', agentParam);
        if (!agentParam) {
            setMessages([]);
            return;
        }

        if (agentParam.opening_statement) {
            setMessages([
                {
                    id: 'welcome',
                    message: {
                        type: 'ai',
                        content: agentParam.opening_statement,
                        suggested: false
                    },
                    status: 'success'
                }
            ]);
        } else {
            setMessages([]);
        }
    };

    const handleFileBeforeUpload = (file: RcFile) => {
        // console.log('fileType', file);

        if (!currentAgentParam) {
            return false;
        }

        const fileType = file.type.split('/')[0];
        // const fileExt = file.type.split('/')[1];

        const fileName = file.name.toLowerCase();
        const fileExt = fileName.split('.').pop() || '';

        if (
            currentAgentParam.file_upload.allowed_file_types &&
            currentAgentParam.file_upload.allowed_file_types.length > 0
        ) {
            // 判断image是否在包含在 fileType中
            if (currentAgentParam.file_upload.allowed_file_types.includes(fileType)) {
                return true;
            } else if (currentAgentParam.file_upload.allowed_file_types.includes('document')) {
                const allowedExtensions = [
                    'txt', 'md', 'mdx', 'markdown',
                    'pdf', 'html', 'xlsx', 'xls',
                    'docx', 'csv', 'eml', 'msg',
                    'pptx', 'ppt', 'xml', 'epub'
                ];
                if (allowedExtensions.includes(fileExt)) {
                    return true;
                } else {
                    antdMessage.error(`不支持的文件类型: ${fileExt.toUpperCase()}`);
                    return Upload.LIST_IGNORE;
                }
            } else {
                antdMessage.error(`不支持的文件类型: ${fileType}`);
                return Upload.LIST_IGNORE;
            }
        }

        if (
            currentAgentParam.file_upload.allowed_file_extensions &&
            currentAgentParam.file_upload.allowed_file_extensions.length > 0
        ) {
            if (currentAgentParam.file_upload.allowed_file_types.includes(fileExt)) {
                return true;
            } else {
                antdMessage.error(`不支持的文件类型: ${fileExt}`);
                return Upload.LIST_IGNORE;
            }
        }
    };

    const handleFeedback = (message: string, rating: string | null) => {
        if (!currentAgent) {
            return;
        }

        // console.log('handleFeedback', message, rating);

        feedback(currentAgent.id, message, rating).then(() => {
            antdMessage.success('反馈成功');
            setMessageFeedBackDict((prevState) => ({
                ...prevState,
                [message]: rating
            }))
        })
    }


    // ==================== Runtime ====================

    const retrieverPopover = (groupedData: Record<string, RetrieverResource[]>) => {
        return Object.entries(groupedData).map(([documentId, items]) => {
            const documentName = items[0]?.document_name || '无标题';

            return (
                <ConfigProvider
                    theme={buttonTheme}
                >
                    <Popover
                        key={documentId}
                        title={documentName}
                        trigger="click"
                        content={retrieverPopoverContent(items)}
                    >
                        <Button className="m-2 w-40 overflow-hidden">
                            <span className="w-full text-xs truncate">{documentName}</span>
                        </Button>
                    </Popover>
                </ConfigProvider>
            );
        });
    };

    const updateConversionId = useRef((conversationId: string) => {
        if (activeKey !== conversationId) {
            setActiveKey(conversationId);
        }
    });

    const updateTaskId = useRef((taskId: string) => {
        if (currentTaskId !== taskId) {
            setCurrentTaskId(taskId);
        }
    });

    const cancelRef = useRef(() => {});

    useEffect(() => {
        const fetchUserInfo = async () => {
            const userInfo = await getUserInfo();
            setUserItem(userInfo);
        };

        const fetchDefaultAgent = async () => {

            const mobileAgentParam = await getMobileAgentParam();
            if (mobileAgentParam && mobileAgentParam.mobile) {
                setCurrentAgent(mobileAgentParam.agentDict);
            } else {
                const defaultAgent = await getAgentDictByIsDefaultTrue();
                if (defaultAgent && defaultAgent.payload) {
                    setCurrentAgent(defaultAgent.payload);
                }
            }
        };

        fetchDefaultAgent();
        fetchUserInfo();

        return () => {
            updateConversionId.current('');
            updateTaskId.current('');
            cancelRef.current();

            // 关闭所有活跃的 EventSource 连接
            closeAllEventSources();
            console.log('组件卸载，关闭所有活跃的 EventSource 连接');
        };
    }, []);

    // ==================== Event ====================

    const onAddConversation = () => {
        // 关闭所有活跃的 EventSource 连接
        closeAllEventSources();
        // 取消当前请求
        cancelRef.current();

        setActiveKey(`auto-${conversationsItems.length + 1}`);
        setMessages([]);

        setDrawerOpen(false);
    };

    const handleLoginBtn = () => {
        localStorage.removeItem('userItem');
        window.location.href = replaceAiPortalDomain('/qy_wechat_h5/ai-portal/index.html#/qywxLogin');
    };

    const handleLogoutBtn = () => {
        logout();
    };

    const onConversationClick: GetProp<typeof Conversations, 'onActiveChange'> = (key) => {
        onCancel();

        // 关闭所有活跃的 EventSource 连接
        closeAllEventSources();
        // 取消当前请求
        cancelRef.current();

        // 根据 key 在conversationsItems 中查找对应的 对象
        const selectedConversation = conversationsItems.find((item) => item.key === key);
        if (selectedConversation) {
            setActiveKey(selectedConversation.conversationId);
            setBeLoadHistoryMessageConversationId(selectedConversation.conversationId)
            loadHistoryMessage(selectedConversation.conversationId).then(() => {
                setBeLoadHistoryMessageConversationId('')
            });
        } else {
            setActiveKey(key);
        }
        setDrawerOpen(false)
    };

    const handleConfirmRename = () => {
        if (!currentAgent || !beRenameConversionId) {
            return;
        }

        if (!chatNameForm.getFieldValue('name')) {
            antdMessage.error('请输入新的名字');
            return;
        }

        setIsShowSpin(true);

        if (renameModalType === 'topChat') {
            topChatHistory(beRenameConversionId, true, chatNameForm.getFieldValue('name')).then((res) => {
                if (res) {
                    setIsShowSpin(false);
                    chatNameForm.setFieldValue('name', '');
                    setBeRenameConversionId('');
                    setIsShowRenameModal(false);
                    loadConversation();
                    antdMessage.success('操作成功');
                }
            })
        } else {
            renameChatHistory(beRenameConversionId, chatNameForm.getFieldValue('name')).then((res) => {
                // renameConversation(currentAgent.id, beRenameConversionId, newName).then((res) => {
                if (res) {
                    setIsShowSpin(false);
                    chatNameForm.setFieldValue('name', '');
                    // setNewName('');
                    setBeRenameConversionId('');
                    setIsShowRenameModal(false);
                    loadConversation();
                }
            });
        }
    };

    const handleCancelRename = () => {
        setIsShowSpin(false);
        chatNameForm.setFieldValue('name', '');
        // setNewName('');
        setIsShowRenameModal(false);
    };

    const onSubmit = (nextContent: string, agent?: AgentDict) => {
        if (!nextContent || !currentAgent) return;

        let defAgent = currentAgent;

        if (agent) {
            defAgent = agent;
        }

        const fileList: DifyFile[] = [];
        if (attachedFiles && attachedFiles.length > 0) {
            attachedFiles.forEach((file) => {
                let type = file.type.split('/')[0];
                if (type !== 'image') {
                    const fileName = file.name.toLowerCase();
                    const fileExt = fileName.split('.').pop() || '';
                    const allowedExtensions = [
                        'txt', 'md', 'mdx', 'markdown',
                        'pdf', 'html', 'xlsx', 'xls',
                        'docx', 'csv', 'eml', 'msg',
                        'pptx', 'ppt', 'xml', 'epub'
                    ];
                    if (allowedExtensions.includes(fileExt)) {
                        type = "document";
                    }
                }

                fileList.push({
                    type: type,
                    transfer_method: 'local_file',
                    upload_file_id: file.response.id,
                    file: file
                });
            });
        }

        // 判断是否是默认agent，如果是就添加 LLM 参数
        let inputs = {};
        if (currentAgent.isDefault) {
            inputs = {
                'llm': llmDefaultSelect
            }
        }

        // 生成请求的 json
        const agentMessage: AgentMessage = {
            type: 'local',
            agentId: defAgent.id,
            conversationId: activeKey,
            suggested: currentAgentParam && currentAgentParam.suggested_questions_after_answer
                ? currentAgentParam.suggested_questions_after_answer.enabled
                : false,
            content: nextContent,
            fileList: fileList,
            inputs: inputs
        };

        setIsBeRefreshConversations(true);
        onRequest(agentMessage);
        setContent('');
        setAttachedFiles([]);
        setHeaderOpen(false);
    };

    const onCancel = () => {
        // console.log('onCancel', currentAgent, currentTaskId);
        if (currentAgent && currentTaskId) {
            stop(currentAgent.id, currentTaskId).then(() => {
                loadConversation(activeKey, currentAgent.id).then()
            });
            cancelRef.current();
        }
    };


    const handleFileUpload = async (file: any) => {
        // console.log('handleFileUpload', file);
        if (!currentAgent) {
            return;
        }

        const encodedFileName = encodeURIComponent(file.file.name);
        const fileWithEncodedName = new File([file.file], encodedFileName, { type: file.file.type });

        const res = await upload(fileWithEncodedName, currentAgent.id);
        file.onSuccess(res);
    };


    const handleFileChange: GetProp<typeof Attachments, 'onChange'> = (info) => {
        setAttachedFiles(info.fileList);
        // console.log('uploadOnchange', info);
    };

    const handleSuggestionPromptsClick = (item: PromptProps) => {
        // console.log(item);
        onSubmit(item.key);
    };

    // ==================== Props ====================

    const userNodeDropdown: MenuProps['items'] = [
//         {
//             key: '1',
//             label: '登出'
//         }
    ];

    const handleUserNodeDropDownItemClick: MenuProps['onClick'] = ({ key }) => {
        if (key === '1') {
            handleLogoutBtn();
        }
    };

    const conversationsMenu: ConversationsProps['menu'] = (conversation) => ({
        items: [
            {
                label: conversation['isTop'] ? '取消固定' : '固定',
                key: 'top',
                icon: <PushpinOutlined />
            },
            {
                label: '重命名',
                key: 'rename',
                icon: <EditOutlined />
            },
            {
                label: '删除',
                key: 'delete',
                icon: <DeleteOutlined />,
                danger: true
            }
        ],
        onClick: (menuInfo) => {
            // console.log(`Click ${conversation.key} - ${menuInfo.key}`, conversation);
            if (!currentAgent) {
                return;
            }
            const isTop: boolean = conversation['isTop'];

            // 根据menuInfo.key分别调用不同的方法
            switch (menuInfo.key) {
                case 'top':
                    if (!isTop) {
                        setRenameModalType('topChat');
                        setBeRenameConversionId(conversation.key);
                        chatNameForm.setFieldValue('name', conversation.label);
                        // setNewName(conversation.label);
                        setIsShowRenameModal(true);
                    } else {
                        topChatHistory(conversation.key, false).then((res) => {
                            if (res) {
                                loadConversation();
                                antdMessage.success('操作成功');
                            }
                        })
                    }
                    setRenameModalType('topChat');

                    break;
                case 'rename':
                    setRenameModalType('rename');
                    setBeRenameConversionId(conversation.key);
                    chatNameForm.setFieldValue('name', conversation.label);
                    // setNewName(conversation.label);
                    setIsShowRenameModal(true);
                    break;
                case 'delete':
                    setIsShowSpin(true);
                    deleteChatHistory(conversation.key).then((res) => {
                        // deleteConversation(currentAgent.id, conversation.key).then((res) => {
                        if (res) {
                            setIsShowSpin(false);
                            loadConversation();
                            antdMessage.success('删除成功');
                        }
                    });
                    break;
            }
        },
        trigger: (menuInfo) => {
            return menuInfo['isTop'] ?
                (<PushpinOutlined style={{color: 'rgba(0, 0, 0, 0.88)'}}
                                  onClick={(event) => {
                                      event.stopPropagation();
                                  }}
                />) :
                (<MoreOutlined style={{color: 'rgba(0, 0, 0, 0.88)'}}
                               onClick={(event) => {
                                   event.stopPropagation();
                               }}
                />)
        },
    });

    const roles: GetProp<typeof Bubble.List, 'roles'> = {
        ai: {
            placement: 'start',
            typing: {step: 1, interval: 50},
            styles: {
                content: {
                    backgroundColor: 'rgba(255,255,255)',
                    marginLeft: 10,
                    marginRight: 10,
                    paddingBottom: 0
                },
                footer: {
                    width: '100%'
                }
            }
        },
        local: {
            placement: 'end',
            styles: {
                content: {
                    backgroundColor: 'rgba(0,0,0,0.04)',
                    color: 'rgba(0,0,0,0.85)',
                    borderRadius: 12,
                    fontSize: 15,
                    marginLeft: 10,
                    marginRight: 10
                },
                footer: {
                    width: '100%'
                }
            },
            shape: 'round',
        },
        suggestion: {
            placement: 'start',
            variant: 'borderless',
            messageRender: (content) => (
                <Prompts
                    vertical={true}
                    items={(content as any as string[]).map((text) => ({
                        key: text,
                        description: (
                            <div>
                                <span>{text}</span>
                                <span style={{ marginLeft: 10 }}>
                  <ArrowRightOutlined />
                </span>
                            </div>
                        )
                    }))}
                    onItemClick={(key) => {
                        handleSuggestionPromptsClick(key.data);
                    }}
                    style={{
                        marginLeft: 20
                    }}
                    classNames={{
                        item: styles.suggestionPrompts
                    }}
                />
            )
        }
    };

    const retrieverPopoverContent = (items: RetrieverResource[]) => {
        return (
            <div
                style={{
                    width: '300px',
                    height: '200px',
                    overflow: 'auto',
                    paddingRight: 10,
                    scrollbarColor: '#888 transparent',
                    scrollbarWidth: 'thin'
                }}
            >
                {items.map((item) => (
                    <div
                        style={{
                            padding: '10px',
                            border: '1px solid #e8e8e8',
                            borderRadius: '4px',
                            marginTop: '10px',
                            marginBottom: '10px'
                        }}
                    >
                        {/* 用户名 */}
                        <div className="flex w-12 items-center px-1.5 h-5 border border-gray-200 rounded-md mb-1">
              <span className="text-[11px] font-medium text-gray-500">
                # {item.segment_position}
              </span>
                        </div>

                        {/* 内容区域 */}
                        <Typography.Paragraph>{item.content}</Typography.Paragraph>
                    </div>
                ))}
            </div>
        );
    };

    // ==================== Nodes ====================
    const loginNode = (
        <Button type="primary" onClick={() => handleLoginBtn()}>
            登录
        </Button>
    );

    const userNode = (
        <Dropdown
            className="h-36px"
            menu={{ items: userNodeDropdown, onClick: handleUserNodeDropDownItemClick }}
        >
            <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                <Avatar style={{ backgroundColor: '#1677ff', marginRight: 8 }}>
                    {userItem?.name ? userItem.name[0] : '?'}
                </Avatar>
                <span>{userItem?.name || '未知用户'}</span>
            </div>
        </Dropdown>
    );

    const logoNode = (
        <div className={styles.logo}>
            <img alt="" src={logoIcon} />
            <span>广州烟草AI门户</span>
        </div>
    );

    const newConversitionNode = (
        <div className="flex w-full justify-center flex-row ">
            <Button
                onClick={onAddConversation}
                type="link"
                className={styles.addBtn}
                icon={<PlusOutlined />}
                disabled={!currentAgent}
            >
                新的对话
            </Button>
        </div>
    )

    const historicalDialoguesNode = (
        <>
            <div className="p-3 w-full flex items-center" style={{ color: '#0000004d' }}>
              <span className="pl-4 text-xs whitespace-nowrap overflow-hidden text-ellipsis">
                历史对话
              </span>
            </div>
            <div className="h-[60%] flex-1 overflow-y-auto">
                <Conversations
                    items={conversationsItems}
                    className={styles.conversations}
                    activeKey={activeKey}
                    onActiveChange={onConversationClick}
                    menu={conversationsMenu}
                    styles={{item: {
                            marginRight: 8
                        }}}
                />
            </div>
        </>
    )

    const mobileLeftSider = (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Drawer: {
                            paddingLG: 0
                        }
                    }
                }}
            >
                <Drawer
                    placement={"left"}
                    closable={false}
                    open={drawerOpen}
                    onClose={() => setDrawerOpen(false)}
                    width={270}
                    style={{ backgroundColor: 'rgb(243, 244, 246)', border: '1px solid rgba(0, 0, 0, 0.08)' }}
                >
                    <div className="flex flex-col h-full">
                        {logoNode}
                        {newConversitionNode}
                        {historicalDialoguesNode}
                    </div>
                </Drawer>
            </ConfigProvider>
        </>
    )

    const senderHeader = (
        <Sender.Header
            title="附件上传"
            open={headerOpen}
            onOpenChange={setHeaderOpen}
            styles={{
                content: {
                    padding: 0
                }
            }}
        >
            <Attachments
                beforeUpload={handleFileBeforeUpload}
                items={attachedFiles}
                onChange={handleFileChange}
                customRequest={handleFileUpload}
                placeholder={(type) =>
                    type === 'drop'
                        ? { title: '拖动文件到此处' }
                        : {
                            icon: <CloudUploadOutlined />,
                            title: '附件上传',
                            description: '单击或拖动文件到此区域上传附件'
                        }
                }
            />
        </Sender.Header>
    );

    const placeholderNode = (
        <Space direction="vertical" size={16} className={styles.placeholder}>
            <Welcome
                variant="borderless"
                icon={<img alt="" src={welcomeIcon} />}
                title={
                    currentAgent ? `你好, 你的${currentAgent.name}已加载！` : '你好, 你的超级智能体已加载！'
                }
                styles={{
                    title: {
                        height: '100%',
                        alignContent: 'center',
                    }
                }}
            />
        </Space>
    );

    const attachmentsNode = (
        <ConfigProvider
            theme={{
                components: {
                    Button: {
                        defaultColor: 'rgba(0, 0, 0, 0.85)',
                        defaultHoverBg: 'rgba(0, 0, 0, 0.1)',
                        defaultHoverColor: 'rgba(0, 0, 0, 0.85)',
                        defaultHoverBorderColor: 'rgb(217, 217, 217)',
                    }
                }
            }}
        >
            <Button icon={<FileAddOutlined />}
                    onClick={() => setHeaderOpen(!headerOpen)}
                    style={{
                        marginRight: '10px',
                    }}
            >
                <Badge dot={attachedFiles.length > 0 && !headerOpen}>
                    上传参考文件
                </Badge>
            </Button>
        </ConfigProvider>
    );

    const senderFooter = (
        <div
            style={{ padding: '8px 0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
        >
            <div
                className="flex items-center justify-center"
            >
                {/*上传参考文件*/}
                {currentAgentParam?.file_upload?.enabled && (attachmentsNode)}
            </div>
        </div>
    )

    // ==================== Render =================
    return (
        <>
            <Layout style={{ height: '100%' }}>
                {mobileLeftSider}
                <Layout className="bg-white" style={{ width: '100%', height: '100%', paddingRight: 20 }}>
                    <Header className="bg-white p-0 flex justify-between items-center w-full">
                        <div className="flex items-center">
                            <Button
                                type="text"
                                variant="text"
                                shape="circle"
                                icon={<MenuUnfoldOutlined />}
                                onClick={() => {
                                    setDrawerOpen(true)
                                }}
                                style={{
                                    fontSize: '16px',
                                    marginLeft: 10
                                }}
                            />
                        </div>
                        <div className="h-9 cursor-pointer">
                            {/* 用户区域 */}
                            {!userItem && loginNode}
                            {userItem && userNode}
                        </div>
                    </Header>
                    <Content className="w-full p-2">
                        <div className={styles.chat}>
                            {/* 🌟 消息列表 */}
                            <Bubble.List
                                items={
                                    items.length > 0 ? items : [{ content: placeholderNode, variant: 'borderless', style: { justifyContent: 'center' } }]
                                }
                                roles={roles}
                                className={`${styles.messages} ${items.length > 0 ? styles.messagesExpanded : styles.messagesCollapsed}`}
                            />
                            {/* 🌟 提示词 */}
                            {/*<Prompts items={senderPromptsItems} onItemClick={onPromptsItemClick} />*/}
                            {/* 🌟 输入框 */}
                            <Sender
                                value={content}
                                placeholder="有什么我能帮您的吗？"
                                header={senderHeader}
                                onSubmit={onSubmit}
                                onCancel={onCancel}
                                onChange={setContent}
                                loading={agent.isRequesting()}
                                className={`${styles.sender} ${items.length > 0 ? 'sender-bottom' : 'sender-center'}`}
                                disabled={!currentAgent}
                                classNames={{
                                    input: "sender-input"
                                }}
                                footer={senderFooter}
                            />
                        </div>
                    </Content>
                </Layout>
            </Layout>
            <Spin spinning={isShowSpin} tip={'正在处理...'} fullscreen />
            <Modal
                title={renameModalType === 'rename' ? '重命名此对话' : '固定此对话'}
                open={isShowRenameModal}
                onOk={handleConfirmRename}
                onCancel={handleCancelRename}
                okText="保存"
                cancelText="取消"
            >
                <Form
                    form={chatNameForm}
                    size={'large'}
                >
                    <Form.Item
                        name="name"
                    >
                        <Input placeholder="请输入对话名称" />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    )

}

export default Independent;

import App from '../App.tsx';
import Independent from "../pages/home";
import QywxLogin from "../pages/qywxLogin";
import { Navigate } from 'react-router-dom';

export const DefaultRoutes = [
  {
    path: '/',
    element: <App />,
    children: [
      {
        index: true,
        element: <Navigate to="home" replace />
      },
      {
        path: 'home',
        name: '主页',
        element: <Independent/>
      },
      {
        path: 'qywxLogin',
        name: '企业微信登录',
        element: <QywxLogin/>
      },
    ]
  }
];

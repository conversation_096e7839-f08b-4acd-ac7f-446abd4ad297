export interface AgentDict {
    id?: string;
    apiServerHost?: string;
    name?: string;
    apiKey?: string;
    isDefault?: boolean;
    sortOrder?: number;
    icon?: string;
    description?: string;
    iconColor: string;
    fontColor: string;
    userAgentId?: string;
}

export interface ColorPair {
    bgColor: string; // 方块颜色
    textColor: string;  // 对应的文字颜色
}

export const colorPairs: ColorPair[] = [
    { bgColor: "#dfdff8", textColor: "#000080" },
    { bgColor: "#ccadf8", textColor: "#480d76" },
    { bgColor: "#aeede4", textColor: "#004d40" },
    { bgColor: "#fafda3", textColor: "#654321" },
    { bgColor: "#f8d5a4", textColor: "#8B4513" },
    { bgColor: "#f5bebb", textColor: "#800020" },
    { bgColor: "#b8edb0", textColor: "#006400" }
];

export function getColorPairByBgColor(bgColor: string): ColorPair {
    const find = colorPairs.find(pair => pair.bgColor === bgColor);
    return find || colorPairs[0];
}

export interface MobileAgentParam {
    mobile: boolean;
    apiServerHost: string;
    apiKey: string;
    name: string;

    agentDict: AgentDict;
}

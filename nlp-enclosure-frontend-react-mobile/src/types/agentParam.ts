export interface AgentParam {
    opening_statement: string;
    suggested_questions: string[];
    suggested_questions_after_answer: {
        enabled: boolean;
    };
    speech_to_text: {
        enabled: boolean;
    };
    text_to_speech: {
        enabled: boolean;
        voice: string;
        language: string;
    };
    retriever_resource: {
        enabled: boolean;
    };
    annotation_reply: {
        enabled: boolean;
    };
    more_like_this: {
        enabled: boolean;
    };
    user_input_form: any[];
    sensitive_word_avoidance: {
        enabled: boolean;
        type: string;
        configs: any[];
    };
    file_upload: {
        image: {
            detail: string;
            enabled: boolean;
            number_limits: number;
            transfer_methods: string[];
        };
        enabled: boolean;
        allowed_file_types: string[];
        allowed_file_extensions: string[];
        allowed_file_upload_methods: string[];
        number_limits: number;
    };
    system_parameters: {
        image_file_size_limit: number;
        video_file_size_limit: number;
        audio_file_size_limit: number;
        file_size_limit: number;
        workflow_file_upload_limit: number;
    };
}

export interface AgentInfo {
    name: string;
    description: string;
    tags: []
}
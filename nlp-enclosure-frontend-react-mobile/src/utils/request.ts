import axios from 'axios';
import {clearUserInfoFormLocalStorage} from "../services/user.ts";
import {replaceAiPortalDomain} from "../services/configService.ts";

const baseUrl = import.meta.env.VITE_APP_API;

// 存储认证令牌
export const setAuthToken = (token: string) => {
  localStorage.setItem('authToken', token);
};

// 获取认证令牌
export const getAuthToken = (): string | null => {
  return localStorage.getItem('authToken');
};

// 清除认证令牌
export const clearAuthToken = () => {
  localStorage.removeItem('authToken');
};

const http = axios.create({
  baseURL: baseUrl,
  timeout: 30000,
  withCredentials: true
  //   headers: { "X-Custom-Header": "foobar" },
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 在发送请求之前对请求配置进行处理
    // 可以添加请求头、验证等操作

    const token = getAuthToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    // 请求错误处理
    console.error('Request interceptor error:', error);

    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    // 对响应数据进行处理
    // 可以进行数据转换、错误处理等操作
    return response.data;
  },
  (error) => {
    // 响应错误处理
    console.error('Response interceptor error:', error);

    // 处理401错误，如果是401就跳转到登录页
    if (error.response && error.response.status === 401) {
      clearUserInfoFormLocalStorage();
      clearAuthToken(); // 清除无效的令牌
      // console.log(replaceAiPortalDomain('/qy_wechat_h5/ai-portal/index.html#/qywxLogin'))
      window.location.href = replaceAiPortalDomain('/qy_wechat_h5/ai-portal/index.html#/qywxLogin');
    }

    return Promise.reject(error);
  }
);

export default http;

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 定义全局css变量，方便复用 */
    --primary: #a991f7;
    --secondary: #f6d860;
    --accent: #37cdbe;
    --neutral: #3d4451;
    --color-primary: 255 115 179;
    --color-secondary: 111 114 185;

    --text1-light: color-mix(in oklab, var(--brand) 25%, black);
    --surface1-light: color-mix(in oklab, var(--brand) 5%, white);
    --text1-dark: color-mix(in oklab, var(--brand) 15%, white);
    --surface1-dark: color-mix(in oklab, var(--brand) 5%, black);
    --text1-dim: color-mix(in oklab, var(--brand) 20%, white);
    --surface1-dim: color-mix(in oklab, var(--brand) 15%, lch(20% 0 0));
  }
}
/* 作用范围是带有这个data-theme="light"属性的元素及其子元素下 */
[data-theme="light"] {
  color-scheme: light;
  --theme-primary: #ffffff;
  --theme-text-color: black;
  transition: all 0.3s ease;
}

/*[data-theme="dark"] {*/
/*  color-scheme: dark;*/
/*  --theme-primary: #1d232a;*/
/*  --theme-text-color: #a6adbb;*/
/*  padding: 2rem;*/
/*  transition: all 0.3s ease;*/
/*}*/

[data-theme="cupcake"] {
  color-scheme: light;
  --theme-primary: #faf7f5;
  --theme-text-color: #291334;
  transition: all 0.3s ease;
}

[data-theme="retro"] {
  color-scheme: light;
  --theme-primary: #ece3ca;
  --theme-text-color: #282425;
  transition: all 0.3s ease;
}

[data-theme="valentine"] {
  color-scheme: light;
  --theme-primary: #fae8f4;
  --theme-text-color: #632c3b;
  transition: all 0.3s ease;
}

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light /*dark*/;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;

  width: 100%;
  height: 100%;
}

body {
  /*margin: 0;*/
  /*display: flex;*/
  /* place-items: center; */
  /*min-width: 100vh;*/
  /*min-height: 100vh;*/
  height: 100%;
  width: 100%;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
}
/*button:hover {*/
/*  border-color: #646cff;*/
/*}*/
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}


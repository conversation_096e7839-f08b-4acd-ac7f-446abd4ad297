import React from 'react';
import ReactDOM from 'react-dom/client';
import { RouterProvider, createHashRouter } from 'react-router-dom';
// import { RouterProvider, createBrowserRouter } from 'react-router-dom';
import { DefaultRoutes } from './config/route.tsx';
import './index.css';
import { getSecondLevelDomainNames } from './services/configService.ts';


// const router = createBrowserRouter(DefaultRoutes);
// 使用 createHashRouter 可以避免 Tomcat 中的路径问题
const router = createHashRouter(DefaultRoutes, {
  // 使用与 vite.config.ts 中相同的 base 路径
  // 注意：如果使用 createHashRouter，通常不需要设置 basename，因为 hash 路由不依赖服务器路径
  // 但如果您的应用确实需要特定的上下文路径，可以取消下面的注释
  // basename: "/nlp-portal"
});

getSecondLevelDomainNames().then(() => {
    ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
        <React.StrictMode>
            <RouterProvider router={router} />
        </React.StrictMode>
    );
}).catch(error => {
    console.error('Failed to load initial config:', error);
    // 可以选择是否继续渲染应用
    ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
        <React.StrictMode>
            <RouterProvider router={router} />
        </React.StrictMode>
    );
})


import http from "../utils/request.ts";
import {MobileAgentParam} from "../types/agentDict.ts";

export const getAgentDictByIsDefaultTrue = (): Promise<any> => {
    return http.get('./enclosure/agentDict/getAgentDictByIsDefaultTrue');
};

export const getMobileAgentParam = async (): Promise<MobileAgentParam> => {
    try {
        const response = await http.get('./enclosure/agentDict/getMobileAgentParam');

        if (response.status.code !== '0') {
            return null;
        }

        return response.payload;
    } catch (error) {
        console.error('Get mobile agent param error:', error);
    }

}



import http from '../utils/request';

// Global variable to store second-level domain names
let secondLevelDomainNames: string;

/**
 * Get second-level domain names from the server
 * @returns Promise with the list of second-level domain names
 */
export const getSecondLevelDomainNames = async (): Promise<string> => {
    try {
        const response = await http.get('./enclosure/config/getSecondLevelDomainNames');

        secondLevelDomainNames = response;
        console.log('Second level domain names loaded:', secondLevelDomainNames);
        return response;
    } catch (error) {
        console.error('Error fetching second level domain names:', error);
        return '';
    }
};

/**
 * Get the stored second-level domain names
 * @returns Array of second-level domain names
 */
export const getStoredSecondLevelDomainNames = (): string => {
    return secondLevelDomainNames;
};

/**
 * Replace 'ai-portal' with the appropriate second-level domain in a URL
 * @param url The URL to process
 * @returns The URL with the domain replaced if needed
 */
export const replaceAiPortalDomain = (url: string): string => {
    if (!url || !getStoredSecondLevelDomainNames()) {
        return url;
    }

    // Check if the URL contains 'ai-portal'
    if (url.includes('ai-portal')) {
        // Replace 'ai-portal' with the first domain in the list
        // You might want to implement more sophisticated logic here
        // depending on your requirements
        return url.replace('ai-portal', getStoredSecondLevelDomainNames());
    }

    return url;
};
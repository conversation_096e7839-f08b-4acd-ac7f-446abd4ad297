import http, { setAuthToken, clearAuthToken } from '../utils/request';
import {Account} from "../types/account.ts";
import {replaceAiPortalDomain} from "./configService.ts";

export const getUserInfo = async ():Promise<Account> => {
    const userInfoFromLocalStorage = getUserInfoFromLocalStorage();
    if (userInfoFromLocalStorage) {
        return userInfoFromLocalStorage
    }

    const res = await http.get('./enclosure/dify/user/info')
    console.log(res)
    const userInfo = res.payload.principal.attributes;
    console.log(userInfo)
    if (userInfo) {
        setUserInfoToLocalStorage(userInfo)
    }
    return userInfo
}

export const setUserInfoToLocalStorage = (userInfo: Account) => {
    localStorage.setItem('userInfo', JSON.stringify(userInfo))
}

export const clearUserInfoFormLocalStorage = () => {
    localStorage.removeItem('userInfo')
}

export const getUserInfoFromLocalStorage = (): Account | null => {
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
        return JSON.parse(userInfo)
    }
    return null
}

export const logout = () => {
    clearUserInfoFormLocalStorage();
    clearAuthToken(); // 清除token
    http.post('./enclosure/logout').then(res => {
        if (res) {
            window.location.href = replaceAiPortalDomain('/qy_wechat_h5/ai-portal/index.html#/qywxLogin');
        }
    })
}
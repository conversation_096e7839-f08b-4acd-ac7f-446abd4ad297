import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';
import vitePluginImp from 'vite-plugin-imp';
import svgr from 'vite-plugin-svgr';

// const ORIGIN_SERVER = import.meta.env.VITE_ORIGIN_SERVER;
// https://vitejs.dev/config/
// Vite 默认是不加载 .env 文件的，因为这些文件需要在执行完 Vite 配置后才能确定加载哪一个
export default defineConfig(({ command, mode }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  // 设置第三个参数为 '' 来加载所有环境变量，而不管是否有 `VITE_` 前缀。
  const env = loadEnv(mode, process.cwd(), '');
  return {
    // 设置为相对路径，确保构建后的资源使用相对路径引用
    // './' - 使用相对路径，适用于任何路径部署
    // '/nlp-portal/' - 如果部署在特定子路径下，使用这种格式
    base: './',
    plugins: [
      react(),
      svgr(),
      vitePluginImp({
        libList: [
          // 按需引入 nutui
          {
            libName: '@nutui/nutui-react',
            style: (name) => {
              return `@nutui/nutui-react/dist/esm/${name}/style/css`;
            },
            replaceOldImport: false,
            camel2DashComponentName: false
          },
          // 按需引入 antd
          {
            libName: 'antd',
            style(name) {
              // use less
              return `antd/es/${name}/style/index.js`;
            }
          }
        ]
      })
    ],
    resolve: {
      alias: {
        react: 'preact/compat',
        'react-dom/test-utils': 'preact/test-utils',
        'react-dom': 'preact/compat',
        'react/jsx-runtime': 'preact/jsx-runtime'
      }
    },
    define: {
      'process.env': {
        NODE_ENV: JSON.stringify(process.env.NODE_ENV || 'development'),
        AUTH_API_BASE_URL: JSON.stringify(process.env.AUTH_API_BASE_URL || ''),
        CI: JSON.stringify(process.env.CI || false),
        FAST_REFRESH: JSON.stringify(process.env.FAST_REFRESH || true),
        WDS_SOCKET_HOST: JSON.stringify(process.env.WDS_SOCKET_HOST || null),
        WDS_SOCKET_PATH: JSON.stringify(process.env.WDS_SOCKET_PATH || null),
        WDS_SOCKET_PORT: JSON.stringify(process.env.WDS_SOCKET_PORT || null),
      }
    },
    server: {
      proxy: {
        // string shorthand: http://localhost:5173/foo -> http://localhost:4567/foo
        '/foo': 'http://localhost:4567',
        // with options: http://localhost:5173/api/bar-> http://jsonplaceholder.typicode.com/bar
        // '/api': {
        //   target: env.VITE_ORIGIN_SERVER,
        //   changeOrigin: true,
        //   rewrite: (path) => path.replace(/^\/api/, '')
        // },
        // with RegEx: http://localhost:5173/fallback/ -> http://jsonplaceholder.typicode.com/
        '^/fallback/.*': {
          target: 'http://jsonplaceholder.typicode.com',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/fallback/, '')
        },
        // Proxying websockets or socket.io: ws://localhost:5173/socket.io -> ws://localhost:5174/socket.io
        '/socket.io': {
          target: 'ws://localhost:5174',
          ws: true
        },
        '/api': {
          target: 'http://localhost:9080',
          changeOrigin: true
        },
        '/openapi': {
          target: 'http://localhost:9080',
          changeOrigin: true
        },
        '/auth': {
          target: 'http://localhost:9080',
          changeOrigin: true,
          secure: false
        },
      }
    },
    // 确保构建输出的资源使用相对路径
    build: {
      rollupOptions: {
        output: {
          // 确保所有静态资源使用相对路径
          assetFileNames: 'assets/[name].[hash].[ext]',
              chunkFileNames: 'assets/[name].[hash].js',

              entryFileNames: 'assets/[name].[hash].js',
        }
      }
    }
  };
});

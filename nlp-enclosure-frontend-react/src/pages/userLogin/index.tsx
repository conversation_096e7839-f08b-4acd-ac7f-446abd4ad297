// #file Login.tsx
import React from 'react';
import { Form, Input, Button, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import {getUserInfo, userNameLogin} from '../../services/user.ts';
import { useNavigate } from "react-router-dom";

const UserLogin: React.FC = () => {
    const [form] = Form.useForm();
    const navigate = useNavigate();

    const onFinish = (values: any) => {
        try {
            const response = userNameLogin(values)
            response.then((res) => {
                console.log('Login successful:', res);
                message.success('登录成功');
                getUserInfo().then((userInfo) => {
                    console.log("userInfo", userInfo)
                    if (userInfo) {
                      // 获取token并添加到URL中
                      const token = localStorage.getItem('authToken');
                      navigate(`/home?token=${token}`);
                    }
                });
            })
            // 可以在这里添加登录成功后的逻辑，比如跳转到主页
        } catch (error) {
            console.error('Login failed:', error);
            message.error('登录失败，请检查用户名和密码');
        }
    };

    return (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
            <Form
                form={form}
                name="login"
                initialValues={{ remember: true }}
                onFinish={onFinish}
                style={{ width: '300px' }}
            >
                <Form.Item
                    name="username"
                    rules={[{ required: true, message: '请输入用户名!' }]}
                >
                    <Input prefix={<UserOutlined className="site-form-item-icon" />} placeholder="用户名" />
                </Form.Item>
                <Form.Item
                    name="password"
                    rules={[{ required: true, message: '请输入密码!' }]}
                >
                    <Input.Password prefix={<LockOutlined className="site-form-item-icon" />} placeholder="密码" />
                </Form.Item>
                <Form.Item>
                    <Button type="primary" htmlType="submit" style={{ width: '100%' }}>
                        登录
                    </Button>
                </Form.Item>
            </Form>
        </div>
    );
};

export default UserLogin;

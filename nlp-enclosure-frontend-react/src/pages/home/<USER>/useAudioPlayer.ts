import { useState, useEffect, useRef } from 'react';
import audioPlayer, { AudioPlayerEventCallback, AudioPlayerEventType } from '../../../services/howlerAudioPlayerService';

export interface AudioPlayerState {
  isAudioPlaying: boolean;
  isAudioLoading: boolean;
  isAudioBuffering: boolean;
  currentPlayingText: string;
  realtimePlayEnabled: boolean;
}

export const useAudioPlayer = () => {
  // ==================== 音频播放相关状态 ====================
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [isAudioLoading, setIsAudioLoading] = useState(false);
  const [isAudioBuffering, setIsAudioBuffering] = useState(false);
  const [currentPlayingText, setCurrentPlayingText] = useState('');
  const [realtimePlayEnabled, setRealtimePlayEnabled] = useState(false);

  const audioEventHandlerRef = useRef<AudioPlayerEventCallback | null>(null);

  useEffect(() => {
    // 设置音频播放器状态变更回调
    audioPlayer.setStateChangeCallback((state) => {
      setIsAudioPlaying(state.isPlaying);
      setIsAudioLoading(state.isLoading);
      setIsAudioBuffering(state.isBuffering);
      setCurrentPlayingText(state.currentText);
    });

    // 添加音频播放器事件监听
    const handleAudioEvent: AudioPlayerEventCallback = (event, data) => {
      switch (event) {
        case 'start':
          console.log('开始播放音频:', data?.text);
          break;
        case 'play':
          console.log('正在播放音频:', data?.text);
          break;
        case 'buffer':
          console.log('音频缓冲中');
          break;
        case 'stop':
          console.log('停止播放音频');
          break;
        case 'end':
          console.log('音频播放结束');
          break;
        case 'error':
          console.error('音频播放错误:', data?.error);
          break;
      }
    };

    audioEventHandlerRef.current = handleAudioEvent;
    audioPlayer.addEventListener(handleAudioEvent);

    return () => {
      if (audioEventHandlerRef.current) {
        audioPlayer.removeEventListener(audioEventHandlerRef.current);
      }
    };
  }, []);

  const toggleRealtimePlay = (enabled: boolean) => {
    setRealtimePlayEnabled(enabled);
    if (!enabled) {
      // 关闭实时播放时，停止当前播放
      audioPlayer.stopRealtimePlay();
    }
  };

  const playRealtimeText = (text: string) => {
    if (realtimePlayEnabled) {
      audioPlayer.playRealtimeText(text);
    }
  };

  const endRealtimePlay = () => {
    if (realtimePlayEnabled) {
      audioPlayer.endRealtimePlay();
    }
  };

  const stopRealtimePlay = () => {
    audioPlayer.stopRealtimePlay();
  };

  return {
    // 状态
    isAudioPlaying,
    isAudioLoading,
    isAudioBuffering,
    currentPlayingText,
    realtimePlayEnabled,
    
    // 方法
    toggleRealtimePlay,
    playRealtimeText,
    endRealtimePlay,
    stopRealtimePlay,
  };
};

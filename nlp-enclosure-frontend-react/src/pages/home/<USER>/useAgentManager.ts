import { useState, useEffect } from 'react';
import { message as antdMessage } from 'antd';
import { AgentDict, getColorPairByBgColor } from '../../../types/agentDict';
import { UserAgentList } from '../../../types/userAgentList';
import { AgentParam } from '../../../types/agentParam';
import { 
  findByIsDefaultFalse, 
  getAgentDictByIsDefaultTrue, 
  deleteAgentDict, 
  findAllVisible 
} from '../../../services/agentDict';
import { 
  getUserAgentList, 
  addUserAgent, 
  deleteUserAgent, 
  setTopUserAgent 
} from '../../../services/userAgentList';
import { parameters } from '../../../services/difyApi';
import { isSuperSonicAgent, createSuperSonicAgentParam } from '../../../services/supersonic';

export const useAgentManager = () => {
  // ==================== Agent相关状态 ====================
  const [defaultAgent, setDefaultAgent] = useState<AgentDict | null>(null);
  const [defaultAgentIsOpenThink, setDefaultAgentIsOpenThink] = useState(false);
  const [isDisableSwitchOpenThink, setIsDisableSwitchOpenThink] = useState(false);
  const [agentList, setAgentList] = useState<AgentDict[]>([]);
  const [currentUserAgentList, setCurrentUserAgentList] = useState<UserAgentList[]>([]);
  const [currentAgent, setCurrentAgent] = useState<AgentDict | null>(null);
  const [currentAgentParam, setCurrentAgentParam] = useState<AgentParam | null>(null);
  const [selectedAgentId, setSelectedAgentId] = useState<string>('');
  const [nodeMenuSelectedKey, setNodeMenuSelectedKey] = useState<string>('');
  const [contentShowState, setContentShowState] = useState<'defaultChat' | 'agent' | 'agentManager' | 'supersonic'>('defaultChat');
  const [currentSuperSonicAgentId, setCurrentSuperSonicAgentId] = useState<string | null>(null);
  const [isOpenAgentFormModal, setIsOpenAgentFormModal] = useState(false);
  const [isCanCreateAgent, setIsCanCreateAgent] = useState(false);
  const [agentFormData, setAgentFormData] = useState<AgentDict | null>(null);
  const [suggestedPrompts, setSuggestedPrompts] = useState<any[]>([]);
  const [isShowVisibleSelectorDialog, setIsShowVisibleSelectorDialog] = useState(false);
  const [visibleSelectAgentDictId, setVisibleSelectAgentDictId] = useState<string>('');

  // ==================== Agent相关方法 ====================
  const fetchAgentList = async () => {
    const [userAgentListResult, agentListResult] = await Promise.all([getUserAgentList(), findAllVisible()]);

    const userAgentList = userAgentListResult;
    const agentList = agentListResult;

    if (userAgentList && userAgentList.length > 0) {
      userAgentList.forEach((userAgent: UserAgentList) => {
        if (userAgent.agentDict) {
          userAgent.agentDict.iconColor = userAgent.agentDict.iconColor || '#dfdff8';
          userAgent.agentDict.fontColor = getColorPairByBgColor(userAgent.agentDict.iconColor).textColor;
        }
      });
      setCurrentUserAgentList(userAgentList);
    } else {
      setCurrentUserAgentList([]);
    }

    if (agentList && agentList.payload) {
      agentList.payload.forEach((agent: AgentDict) => {
        agent.iconColor = agent.iconColor || '#dfdff8';
        agent.fontColor = getColorPairByBgColor(agent.iconColor).textColor;

        const userAgent = userAgentList.find((ua: UserAgentList) => ua.agentDictId === agent.id);
        if (userAgent) {
          agent.userAgentId = userAgent.id;
        }
      });
      setAgentList(agentList.payload);
    }
  };

  const loadAgentParameters = async (agent: AgentDict) => {
    if (!agent) {
      return;
    }

    if (isSuperSonicAgent(agent)) {
      const superSonicParam = createSuperSonicAgentParam();
      setCurrentAgentParam(superSonicParam);

      setSuggestedPrompts(superSonicParam.suggested_questions.map((question, index) => ({
        key: `supersonic-${index}`,
        description: question
      })));

      return superSonicParam;
    }

    try {
      const agentParam = await parameters(agent.id);
      setCurrentAgentParam(agentParam);
      return agentParam;
    } catch (e) {
      console.error('Error loading agent parameters:', e);
      antdMessage.error('加载参数失败');
      return null;
    }
  };

  const handleUpdateOpenThinkSwitch = (agent: AgentDict | null, llmDefaultSelect: string) => {
    if (!agent || !agent.isDefault) {
      return;
    }

    setIsDisableSwitchOpenThink(llmDefaultSelect === 'Qwen2.5-VL:32B-instruct');
    
    if (llmDefaultSelect === 'Qwen2.5-VL:32B-instruct') {
      setDefaultAgentIsOpenThink(false);
    }
  };

  const handleAgentClick = (agent: AgentDict) => {
    if (agent.type === 'supersonic') {
      antdMessage.info('正在加载 SuperSonic 问答页面...', 2);
      setCurrentSuperSonicAgentId(agent.id);
      setContentShowState('supersonic');
    } else {
      setContentShowState('defaultChat');
    }

    setCurrentAgent(agent);
    setNodeMenuSelectedKey(agent.id || '');
    setSelectedAgentId(agent.id || '');
  };

  const handleAgentManagerClick = () => {
    setContentShowState('agentManager');
    setSelectedAgentId('');
  };

  const handleCreateAgentBtnClick = () => {
    setAgentFormData(null);
    setTimeout(() => {
      setIsOpenAgentFormModal(true);
    }, 10);
  };

  const handleEditAgentBtnClick = (agent: AgentDict) => {
    setAgentFormData(agent);
    setTimeout(() => {
      setIsOpenAgentFormModal(true);
    }, 10);
  };

  const handleAddToList = async (agent: AgentDict) => {
    const userAgent: UserAgentList = {
      agentDictId: agent.id,
    };
    
    try {
      await addUserAgent(userAgent);
      antdMessage.success('添加成功');
      fetchAgentList();
    } catch (error) {
      antdMessage.error('添加失败');
    }
  };

  const handleAgentCardMoreDropDownClick = async (menuInfo: any, item: AgentDict) => {
    switch (menuInfo.key) {
      case 'addToList':
        await handleAddToList(item);
        break;
      case 'visibility':
        setVisibleSelectAgentDictId(item.id || '');
        setIsShowVisibleSelectorDialog(true);
        break;
      case 'edit':
        handleEditAgentBtnClick(item);
        break;
      case 'remove':
        try {
          await deleteAgentDict(item.id || '');
          antdMessage.success('删除成功');
          fetchAgentList();
        } catch (error) {
          antdMessage.error('删除失败');
        }
        break;
    }
  };

  const handleAgentListMoreDropDownClick = async (menuInfo: any, userAgent: UserAgentList) => {
    switch (menuInfo.key) {
      case 'top':
        try {
          await setTopUserAgent(userAgent.id || '', !userAgent.isTop);
          fetchAgentList();
          antdMessage.success('操作成功');
        } catch (error) {
          antdMessage.error('操作失败');
        }
        break;
      case 'remove':
        try {
          await deleteUserAgent(userAgent.id || '');
          fetchAgentList();
          antdMessage.success('删除成功');
        } catch (error) {
          antdMessage.error('删除失败');
        }
        break;
    }
  };

  const fetchDefaultAgent = async () => {
    const defaultAgent = await getAgentDictByIsDefaultTrue();
    if (defaultAgent && defaultAgent.payload) {
      setDefaultAgent(defaultAgent.payload);
      setCurrentAgent(defaultAgent.payload);
      return defaultAgent.payload;
    }
    return null;
  };

  return {
    // 状态
    defaultAgent,
    defaultAgentIsOpenThink,
    isDisableSwitchOpenThink,
    agentList,
    currentUserAgentList,
    currentAgent,
    currentAgentParam,
    selectedAgentId,
    nodeMenuSelectedKey,
    contentShowState,
    currentSuperSonicAgentId,
    isOpenAgentFormModal,
    isCanCreateAgent,
    agentFormData,
    suggestedPrompts,
    isShowVisibleSelectorDialog,
    visibleSelectAgentDictId,

    // 方法
    fetchAgentList,
    loadAgentParameters,
    handleUpdateOpenThinkSwitch,
    handleAgentClick,
    handleAgentManagerClick,
    handleCreateAgentBtnClick,
    handleEditAgentBtnClick,
    handleAddToList,
    handleAgentCardMoreDropDownClick,
    handleAgentListMoreDropDownClick,
    fetchDefaultAgent,

    // 设置方法
    setDefaultAgent,
    setDefaultAgentIsOpenThink,
    setIsDisableSwitchOpenThink,
    setCurrentAgent,
    setCurrentAgentParam,
    setSelectedAgentId,
    setNodeMenuSelectedKey,
    setContentShowState,
    setCurrentSuperSonicAgentId,
    setIsOpenAgentFormModal,
    setIsCanCreateAgent,
    setAgentFormData,
    setSuggestedPrompts,
    setIsShowVisibleSelectorDialog,
    setVisibleSelectAgentDictId,
  };
};

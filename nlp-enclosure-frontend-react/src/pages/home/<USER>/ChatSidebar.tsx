import React from 'react';
import { Layout, Drawer } from 'antd';
import { ConversationsProps } from '@ant-design/x';
import { Conversation } from '@ant-design/x/es/conversations';
import { useHomeStyles } from '../styles';
import { AgentDict } from '../../../types/agentDict';
import { UserAgentList } from '../../../types/userAgentList';
import { Account } from '../../../types/account';
import LogoSection from './LogoSection';
import NewConversationButton from './NewConversationButton';
import HistoricalDialogues from './HistoricalDialogues';
import UserSection from './UserSection';
import AgentList from './AgentList';

const { Sider } = Layout;

interface ChatSidebarProps {
  // 布局相关
  collapsed: boolean;
  drawerOpen: boolean;
  isLogoSpanVisible: boolean;
  isMobile: boolean;
  
  // 对话相关
  conversationsItems: Conversation[];
  activeKey: string;
  onConversationClick: ConversationsProps['onActiveChange'];
  conversationsMenu: ConversationsProps['menu'];
  onAddConversation: (llmSelect: string) => void;
  llmDefaultSelect: string;
  
  // Agent相关
  currentAgent: AgentDict | null;
  currentUserAgentList: UserAgentList[];
  selectedAgentId: string;
  contentShowState: string;
  nodeMenuSelectedKey: string;
  onAgentClick: (agent: AgentDict) => void;
  onAgentManagerClick: () => void;
  onAgentListMoreDropDownClick: (menuInfo: any, userAgent: UserAgentList) => void;
  
  // 用户相关
  userItem: Account | null;
  onLogin: () => void;
  onLogout: () => void;
  
  // 事件处理
  setDrawerOpen: (open: boolean) => void;
}

const ChatSidebar: React.FC<ChatSidebarProps> = ({
  collapsed,
  drawerOpen,
  isLogoSpanVisible,
  isMobile,
  conversationsItems,
  activeKey,
  onConversationClick,
  conversationsMenu,
  onAddConversation,
  llmDefaultSelect,
  currentAgent,
  currentUserAgentList,
  selectedAgentId,
  contentShowState,
  nodeMenuSelectedKey,
  onAgentClick,
  onAgentManagerClick,
  onAgentListMoreDropDownClick,
  userItem,
  onLogin,
  onLogout,
  setDrawerOpen
}) => {
  const { styles } = useHomeStyles();

  const sidebarContent = (
    <>
      <LogoSection isLogoSpanVisible={isLogoSpanVisible} />
      
      <NewConversationButton
        onAddConversation={onAddConversation}
        llmDefaultSelect={llmDefaultSelect}
        currentAgent={currentAgent}
        isLogoSpanVisible={isLogoSpanVisible}
      />
      
      <HistoricalDialogues
        conversationsItems={conversationsItems}
        activeKey={activeKey}
        onConversationClick={onConversationClick}
        conversationsMenu={conversationsMenu}
      />
      
      <div className="p-3 w-full flex items-center justify-between">
        <UserSection
          userItem={userItem}
          onLogin={onLogin}
          onLogout={onLogout}
        />
      </div>
      
      <AgentList
        currentUserAgentList={currentUserAgentList}
        selectedAgentId={selectedAgentId}
        collapsed={collapsed}
        contentShowState={contentShowState}
        nodeMenuSelectedKey={nodeMenuSelectedKey}
        onAgentClick={onAgentClick}
        onAgentManagerClick={onAgentManagerClick}
        onAgentListMoreDropDownClick={onAgentListMoreDropDownClick}
      />
    </>
  );

  if (isMobile) {
    return (
      <Drawer
        title={null}
        placement="left"
        closable={false}
        onClose={() => setDrawerOpen(false)}
        open={drawerOpen}
        styles={{
          body: {
            padding: 0,
            backgroundColor: 'rgb(243, 244, 246)'
          }
        }}
        width={280}
      >
        <div className={styles.menu}>
          {sidebarContent}
        </div>
      </Drawer>
    );
  }

  return (
    <Sider
      className={styles.menu}
      collapsed={collapsed}
      collapsedWidth={0}
      width={280}
      style={{
        transition: 'all 0.3s ease'
      }}
    >
      {sidebarContent}
    </Sider>
  );
};

export default ChatSidebar;

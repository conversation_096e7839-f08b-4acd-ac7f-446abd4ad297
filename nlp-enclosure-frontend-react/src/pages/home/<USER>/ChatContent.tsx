import React from 'react';
import { Bubble, Sender } from '@ant-design/x';
import { GetProp } from 'antd';
import { useHomeStyles } from '../styles';
import { AgentDict } from '../../../types/agentDict';
import WelcomePlaceholder from './WelcomePlaceholder';
import AttachmentsUpload from './AttachmentsUpload';

interface ChatContentProps {
  // 消息相关
  items: GetProp<typeof Bubble.List, 'items'>;
  roles: GetProp<typeof Bubble.List, 'roles'>;
  
  // Agent相关
  currentAgent: AgentDict | null;
  
  // 发送器相关
  content: string;
  setContent: (content: string) => void;
  onSubmit: (content: string) => void;
  onCancel: () => void;
  isRequesting: boolean;
  
  // 文件上传相关
  headerOpen: boolean;
  setHeaderOpen: (open: boolean) => void;
  attachedFiles: GetProp<typeof AttachmentsUpload, 'attachedFiles'>;
  handleFileBeforeUpload: GetProp<typeof AttachmentsUpload, 'handleFileBeforeUpload'>;
  handleFileChange: GetProp<typeof AttachmentsUpload, 'handleFileChange'>;
  handleFileUpload: GetProp<typeof AttachmentsUpload, 'handleFileUpload'>;
  
  // 语音相关
  isSpeechRecording: boolean;
  onSpeechStart: () => void;
  onSpeechStop: () => void;
  
  // 实时播放相关
  realtimePlayEnabled: boolean;
  onRealtimePlayToggle: (enabled: boolean) => void;
  
  // LLM选择相关
  llmDefaultSelect: string;
  onLlmChange: (value: string) => void;
  llmItems: any[];
  
  // 深度思考相关
  defaultAgentIsOpenThink: boolean;
  isDisableSwitchOpenThink: boolean;
  onOpenThinkChange: (checked: boolean) => void;
}

const ChatContent: React.FC<ChatContentProps> = ({
  items,
  roles,
  currentAgent,
  content,
  setContent,
  onSubmit,
  onCancel,
  isRequesting,
  headerOpen,
  setHeaderOpen,
  attachedFiles,
  handleFileBeforeUpload,
  handleFileChange,
  handleFileUpload,
  isSpeechRecording,
  onSpeechStart,
  onSpeechStop,
  realtimePlayEnabled,
  onRealtimePlayToggle,
  llmDefaultSelect,
  onLlmChange,
  llmItems,
  defaultAgentIsOpenThink,
  isDisableSwitchOpenThink,
  onOpenThinkChange
}) => {
  const { styles } = useHomeStyles();

  const { attachmentsNode, senderHeader } = AttachmentsUpload({
    headerOpen,
    setHeaderOpen,
    attachedFiles,
    handleFileBeforeUpload,
    handleFileChange,
    handleFileUpload
  });

  return (
    <div className={styles.chat}>
      {/* 消息列表 */}
      <div className={`${styles.messages} ${items.length > 0 ? styles.messagesExpanded : styles.messagesCollapsed}`}>
        {items.length === 0 ? (
          <WelcomePlaceholder 
            currentAgent={currentAgent} 
            className={styles.placeholder}
          />
        ) : (
          <Bubble.List
            items={items}
            roles={roles}
            style={{ flex: 1 }}
          />
        )}
      </div>

      {/* 发送器 */}
      <div className={styles.sender}>
        {senderHeader}
        
        <Sender
          value={content}
          onChange={setContent}
          onSubmit={onSubmit}
          onCancel={onCancel}
          loading={isRequesting}
          disabled={!currentAgent}
          placeholder={currentAgent ? `向 ${currentAgent.name} 发送消息...` : '请先选择一个Agent'}
          actions={[attachmentsNode]}
          // 这里可以添加更多的发送器配置
        />
      </div>
    </div>
  );
};

export default ChatContent;

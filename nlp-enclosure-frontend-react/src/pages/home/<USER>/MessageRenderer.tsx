import React from 'react';
import { Typo<PERSON>, Divider, <PERSON><PERSON>, Tooltip, Image, ConfigProvider } from 'antd';
import { Attachments, Prompts } from '@ant-design/x';
import { 
  CopyOutlined, 
  LikeOutlined, 
  DislikeOutlined, 
  LikeFilled, 
  DislikeFilled,
  ArrowRightOutlined 
} from '@ant-design/icons';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { message as antdMessage } from 'antd';
import markdownit from 'markdown-it';
import PlayButton from '../../../components/PlayButton';
import { RetrieverResource } from '../../../types/streamResponse';
import { buttonTheme } from '../../../theme';

const md = markdownit({ html: true, breaks: true });

interface MessageRendererProps {
  content: string;
  messageType: 'ai' | 'local' | 'suggestion';
  messageId: string;
  retrieverResources?: RetrieverResource[];
  fileList?: any[];
  messageFeedBackDict: { [key: string]: string | null };
  realtimePlayEnabled: boolean;
  suggestionPromptsClassName?: string;
  onFeedback: (messageId: string, rating: string | null) => void;
  onSuggestionClick: (suggestion: any) => void;
}

const MessageRenderer: React.FC<MessageRendererProps> = ({
  content,
  messageType,
  messageId,
  retrieverResources,
  fileList,
  messageFeedBackDict,
  realtimePlayEnabled,
  suggestionPromptsClassName,
  onFeedback,
  onSuggestionClick
}) => {
  const renderMarkdown = (content: string) => {
    return (
      <Typography>
        <div className="ai-content-class" dangerouslySetInnerHTML={{ __html: md.render(content) }} />
      </Typography>
    );
  };

  const retrieverPopover = (groupedData: Record<string, RetrieverResource[]>) => {
    return Object.entries(groupedData).map(([documentId, items]) => {
      const documentName = items[0]?.document_name || '无标题';

      return (
        <ConfigProvider theme={buttonTheme} key={documentId}>
          <Button className="m-2 w-40 overflow-hidden">
            <span className="w-full text-xs truncate">{documentName}</span>
          </Button>
        </ConfigProvider>
      );
    });
  };

  const renderAIMessage = () => {
    let messageContent = renderMarkdown(content);

    // 处理检索资源
    if (retrieverResources && retrieverResources.length > 0) {
      const grouped: Record<string, RetrieverResource[]> = {};
      retrieverResources.forEach((retriever) => {
        const key = retriever.document_id;
        if (!grouped[key]) {
          grouped[key] = [];
        }
        grouped[key].push(retriever);
      });

      messageContent = (
        <>
          {renderMarkdown(content)}
          <div>
            <Divider
              style={{ margin: '5px 0', borderColor: 'rgba(0,0,0,.25)' }}
              orientation="left"
            >
              引用
            </Divider>
            <div>{retrieverPopover(grouped)}</div>
          </div>
        </>
      );
    }

    return messageContent;
  };

  const renderLocalMessage = () => {
    if (fileList && fileList.length > 0) {
      return (
        <>
          {fileList.map((file, fileIndex) => {
            if (file.type === 'document') {
              return <Attachments.FileCard key={file.file?.uid || fileIndex} item={file.file} />;
            } else {
              return (
                <Image.PreviewGroup key={fileIndex}>
                  <Image
                    width={100}
                    src={file.url ? file.url : (file.file?.originFileObj ? URL.createObjectURL(file.file.originFileObj) : '')}
                    style={{ marginRight: '10px' }}
                  />
                </Image.PreviewGroup>
              );
            }
          })}
          {renderMarkdown(content)}
        </>
      );
    }
    return renderMarkdown(content);
  };

  const renderSuggestionMessage = () => {
    const suggestions = content as any as string[];
    return (
      <Prompts
        vertical={true}
        items={suggestions.map((text) => ({
          key: text,
          description: (
            <div>
              <span>{text}</span>
              <span style={{ marginLeft: 10 }}>
                <ArrowRightOutlined />
              </span>
            </div>
          )
        }))}
        onItemClick={(key) => {
          onSuggestionClick(key.data);
        }}
        style={{ marginLeft: 20 }}
        classNames={{ item: suggestionPromptsClassName }}
      />
    );
  };

  const renderFooter = () => {
    if (messageType !== 'ai') return null;

    return (
      <div style={{ display: 'flex', justifyContent: 'space-between', width: '96%', marginLeft: 10 }}>
        <div style={{ display: 'flex' }}>
          {/* 复制按钮 */}
          <Tooltip title="复制">
            <CopyToClipboard 
              text={typeof content === 'string' ? content : ''} 
              onCopy={() => antdMessage.success('内容已复制到剪贴板')}
            >
              <Button
                color="default"
                variant="text"
                size="small"
                icon={<CopyOutlined />}
              />
            </CopyToClipboard>
          </Tooltip>
          
          {/* 播放按钮 - 仅在未开启实时播放时显示 */}
          {!realtimePlayEnabled && (
            <PlayButton
              message={typeof content === 'string' ? content : ''}
              disabled={!content}
            />
          )}
        </div>
        
        {/* 反馈按钮 */}
        <div className="flex items-center">
          <Tooltip title={messageFeedBackDict[messageId] === 'like' ? '取消点赞' : '点赞'}>
            <Button
              color="default"
              variant="text"
              size="small"
              icon={messageFeedBackDict[messageId] === 'like' ? 
                <LikeFilled style={{color: '#1890ff'}} /> : 
                <LikeOutlined />
              }
              onClick={() => onFeedback(messageId, messageFeedBackDict[messageId] === 'like' ? null : 'like')}
              style={{ marginRight: 10}}
            />
          </Tooltip>
          <Tooltip title={messageFeedBackDict[messageId] === 'dislike' ? '取消点踩' : '点踩'}>
            <Button
              color="default"
              variant="text"
              size="small"
              icon={messageFeedBackDict[messageId] === 'dislike' ? 
                <DislikeFilled style={{color: '#dc2626'}} /> : 
                <DislikeOutlined />
              }
              onClick={() => onFeedback(messageId, messageFeedBackDict[messageId] === 'dislike' ? null : 'dislike')}
            />
          </Tooltip>
        </div>
      </div>
    );
  };

  const renderContent = () => {
    switch (messageType) {
      case 'ai':
        return renderAIMessage();
      case 'local':
        return renderLocalMessage();
      case 'suggestion':
        return renderSuggestionMessage();
      default:
        return renderMarkdown(content);
    }
  };

  return (
    <>
      {renderContent()}
      {renderFooter()}
    </>
  );
};

export default MessageRenderer;

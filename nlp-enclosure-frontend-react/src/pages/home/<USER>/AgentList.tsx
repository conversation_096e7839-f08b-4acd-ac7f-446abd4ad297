import React from 'react';
import { Ava<PERSON>, ConfigProvider, Dropdown, Menu, MenuProps } from 'antd';
import { MoreOutlined, PushpinOutlined } from '@ant-design/icons';
import SubMenu from 'antd/es/menu/SubMenu';
import { AgentDict } from '../../../types/agentDict';
import { UserAgentList } from '../../../types/userAgentList';
import { getAgentListMoreItems } from '../constants';
import agentSvg from '../img/agent.svg';

interface AgentListProps {
  currentUserAgentList: UserAgentList[];
  selectedAgentId: string;
  collapsed: boolean;
  contentShowState: string;
  nodeMenuSelectedKey: string;
  onAgentClick: (agent: AgentDict) => void;
  onAgentManagerClick: () => void;
  onAgentListMoreDropDownClick: (menuInfo: any, userAgent: UserAgentList) => void;
}

const AgentList: React.FC<AgentListProps> = ({
  currentUserAgentList,
  selectedAgentId,
  collapsed,
  contentShowState,
  nodeMenuSelectedKey,
  onAgentClick,
  onAgentManagerClick,
  onAgentListMoreDropDownClick
}) => {
  return (
    <div className="pt-3 pr-3 pl-3 min-h-[30%]" style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
      <ConfigProvider
        theme={{
          components: {
            Menu: {
              /* 这里是你的组件 token */
              popupBg: 'rgb(243, 244, 246)'
            }
          }
        }}
      >
        <Menu
          mode="vertical"
          style={{ backgroundColor: 'rgb(243, 244, 246)', border: 'None' }}
          selectedKeys={[nodeMenuSelectedKey]}
          triggerSubMenuAction={'click'}
        >
          {/* 前4个Agent作为一级菜单 */}
          {currentUserAgentList.slice(0, 5).map((item) => {
            if (item.agentDict) {
              return (
                <Menu.Item
                  key={item.id}
                  onClick={() => onAgentClick(item.agentDict!)}
                  style={{
                    margin: '10px 0',
                    width: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    borderRadius: '8px',
                    backgroundColor: selectedAgentId === item.agentDict?.id ? 'rgba(0, 0, 0, 0.06)' : 'transparent',
                    borderLeft: selectedAgentId === item.agentDict?.id ? '3px solid #1677ff' : '3px solid transparent',
                    transition: 'all 0.3s'
                  }}
                >
                  <div className="w-full flex items-center justify-between">
                    <div className="flex items-center cursor-pointer justify-start" style={{ width: '100%' }}>
                      <Avatar size="small" style={{
                        backgroundColor: item.agentDict.iconColor,
                        color: item.agentDict.fontColor,
                        marginRight: 8,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center'
                      }}
                      src={item.agentDict.icon && (item.agentDict.icon.startsWith('http://') || item.agentDict.icon.startsWith('https://'))
                        ? item.agentDict.icon
                        : undefined}
                      >
                        {item.agentDict.icon && !(item.agentDict.icon.startsWith('http://') || item.agentDict.icon.startsWith('https://')) ? (
                          <span style={{ fontSize: '14px' }}>{item.agentDict.icon}</span>
                        ) : (
                          !item.agentDict.icon && (item.agentDict.name ? item.agentDict.name[0] : '?')
                        )}
                      </Avatar>
                      <div className="w-full max-w-[150px]">
                        <div className="truncate w-full text-sm font-medium">
                          {item.agentDict.name}
                          {/* 如果是SuperSonic类型，显示特殊标记 */}
                          {item.agentDict.type === 'supersonic' && (
                            <span style={{
                              marginLeft: '5px',
                              color: '#00bfff',
                              fontWeight: 'bold',
                              fontSize: '10px'
                            }}>
                              SuperSonic
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div
                      style={{display: collapsed ? 'none' : 'block'}}
                      onClick={(event) => {
                        event.stopPropagation();
                      }}>
                      <Dropdown
                        menu={{
                          items: getAgentListMoreItems(item),
                          onClick: (info) => onAgentListMoreDropDownClick(info, item)
                        }}
                        trigger={['click']}
                      >
                        {item.isTop ? (<PushpinOutlined />) : (<MoreOutlined />)}
                      </Dropdown>
                    </div>
                  </div>
                </Menu.Item>
              );
            }
            return (<></>);
          })}

          {/* 剩余Agent放在"更多"二级菜单中 */}
          {currentUserAgentList.length > 5 && (
            <SubMenu
              key="more"
              title="更多"
              style={{ margin: '10px 0', width: '100%', display: 'flex', alignItems: 'center' }}
            >
              {currentUserAgentList.slice(5).map((item) => {
                if (item.agentDict) {
                  return (
                    <Menu.Item
                      key={item.id}
                      onClick={() => onAgentClick(item.agentDict!)}
                      style={{
                        margin: '5px 4px',
                        display: 'flex',
                        alignItems: 'center',
                        borderRadius: '8px',
                        backgroundColor: selectedAgentId === item.agentDict?.id ? 'rgba(0, 0, 0, 0.06)' : 'transparent',
                        borderLeft: selectedAgentId === item.agentDict?.id ? '3px solid #1677ff' : '3px solid transparent',
                        transition: 'all 0.3s'
                      }}
                    >
                      <div className="w-full flex items-center justify-between">
                        <div className="flex items-center cursor-pointer justify-start" style={{ width: '100%' }}>
                          <Avatar size="small" style={{
                            backgroundColor: item.agentDict.iconColor,
                            color: item.agentDict.fontColor,
                            marginRight: 8,
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center'
                          }}
                          src={item.agentDict.icon && (item.agentDict.icon.startsWith('http://') || item.agentDict.icon.startsWith('https://'))
                            ? item.agentDict.icon
                            : undefined}
                          >
                            {item.agentDict.icon && !(item.agentDict.icon.startsWith('http://') || item.agentDict.icon.startsWith('https://')) ? (
                              <span style={{ fontSize: '14px' }}>{item.agentDict.icon}</span>
                            ) : (
                              !item.agentDict.icon && (item.agentDict.name ? item.agentDict.name[0] : '?')
                            )}
                          </Avatar>
                          <div>
                            <div className="text-sm font-medium">
                              {item.agentDict.name}
                              {/* 如果是SuperSonic类型，显示特殊标记 */}
                              {item.agentDict.type === 'supersonic' && (
                                <span style={{
                                  marginLeft: '5px',
                                  color: '#00bfff',
                                  fontWeight: 'bold',
                                  fontSize: '10px'
                                }}>
                                  SuperSonic
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div
                          onClick={(event) => {
                            event.stopPropagation();
                          }}
                          style={{marginLeft: 30}}
                        >
                          <Dropdown
                            menu={{
                              items: getAgentListMoreItems(item),
                              onClick: (info) => onAgentListMoreDropDownClick(info, item)
                            }}
                            trigger={['click']}
                          >
                            <MoreOutlined />
                          </Dropdown>
                        </div>
                      </div>
                    </Menu.Item>
                  );
                }
                return (<></>);
              })}
            </SubMenu>
          )}

          {/* Agent管理器 */}
          <Menu.Item
            key="agentManager"
            onClick={() => onAgentManagerClick()}
            style={{
              margin: '10px 0',
              width: '100%',
              display: 'flex',
              alignItems: 'center',
              borderRadius: '8px',
              backgroundColor: contentShowState === 'agentManager' ? 'rgba(0, 0, 0, 0.06)' : 'transparent',
              borderLeft: contentShowState === 'agentManager' ? '3px solid #1677ff' : '3px solid transparent',
              transition: 'all 0.3s'
            }}
          >
            <div className="flex items-center cursor-pointer" style={{ width: '100%' }}>
              <Avatar size="small" style={{ backgroundColor: 'rgb(243, 244, 246)', marginRight: 8 }}>
                <img src={agentSvg} />
              </Avatar>
              <div>
                <div className="text-sm font-medium">Agent管理器</div>
              </div>
            </div>
          </Menu.Item>
        </Menu>
      </ConfigProvider>
    </div>
  );
};

export default AgentList;

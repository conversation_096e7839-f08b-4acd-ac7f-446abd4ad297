import { useState, useRef } from 'react';
import { Form, message as antdMessage } from 'antd';
import { Conversation } from '@ant-design/x/es/conversations';
import { AgentDict, getColorPairByBgColor } from '../../../types/agentDict';
import { getChatHistoryList, deleteChatHistory, renameChatHistory, topChatHistory } from '../../../services/chatHistory';
import { getAgentMessage, stop } from '../../../services/difyApi';
import { getBaseUrlWithoutProtocol } from '../../../services/websocketService';

export const useChatManager = () => {
  // ==================== 聊天相关状态 ====================
  const [conversationsItems, setConversationsItems] = useState<Conversation[]>([]);
  const [isBeRefreshConversations, setIsBeRefreshConversations] = useState(false);
  const [activeKey, setActiveKey] = useState<string>('auto');
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [beRenameConversionId, setBeRenameConversionId] = useState<string | null>(null);
  const [beLoadHistoryMessageConversationId, setBeLoadHistoryMessageConversationId] = useState('');
  const [isShowRenameModal, setIsShowRenameModal] = useState(false);
  const [renameModalType, setRenameModalType] = useState<'rename' | 'topChat'>('rename');
  const [messageFeedBackDict, setMessageFeedBackDict] = useState<{[key: string]: string | null}>({});

  const [chatNameForm] = Form.useForm<{ name: string }>();
  const isBeRefreshConversationsRef = useRef(isBeRefreshConversations);

  // ==================== 聊天相关方法 ====================
  const loadConversation = async (conversationId?: string, agentId?: string) => {
    if (conversationId && conversationId.startsWith('auto')) {
      return [];
    }

    if (conversationId && agentId && !isBeRefreshConversationsRef.current) {
      return [];
    }

    setIsBeRefreshConversations(false);

    try {
      const conversations = await getChatHistoryList(conversationId, agentId);
      if (conversations && conversations.length > 0) {
        const formattedConversations = conversations.map((conversation) => ({
          key: conversation.id,
          label: conversation.name,
          isTop: conversation.isTop,
          conversationId: conversation.conversationId,
          agentDict: conversation.agentDict,
          timestamp: new Date(conversation.createAt).getTime(),
          icon: conversation.agentDict.isDefault ? 
            (<div>Default Icon</div>) : 
            (<div>Agent Icon</div>)
        }));

        setConversationsItems(formattedConversations);
        return formattedConversations;
      } else {
        setConversationsItems([]);
        return [];
      }
    } catch (ex) {
      console.error('Error loading conversations:', ex);
    }
    return [];
  };

  const loadHistoryMessage = async (conversationId: string, currentAgent: AgentDict | null, setMessages: any) => {
    if (!currentAgent || !conversationId) {
      return;
    }

    const messageRes = await getAgentMessage(currentAgent.id, conversationId);

    if (messageRes.data && messageRes.data.length > 0) {
      setMessageFeedBackDict({});

      const array: any[] = [];
      const hostUrl = getBaseUrlWithoutProtocol(true);
      
      messageRes.data.forEach((message: any) => {
        let userFiles = [];
        let aiFiles = [];
        
        if (message.message_files && message.message_files.length > 0) {
          userFiles = message.message_files.filter((file: any) => file.belongs_to === 'user');
          if (userFiles.length > 0) {
            userFiles.forEach((file: any) => {
              file.url = hostUrl + '/dfile' + file.url;
            });
          }

          aiFiles = message.message_files.filter((file: any) => file.belongs_to !== 'user');
          if (aiFiles.length > 0) {
            aiFiles.forEach((file: any) => {
              file.url = hostUrl + '/dfile' + file.url;
            });
          }
        }

        if (message.feedback && message.feedback.rating) {
          setMessageFeedBackDict((prev) => ({
            ...prev,
            [message.id]: message.feedback.rating
          }));
        }

        let answer = message.answer;
        if (answer.startsWith("<think>")) {
          answer = answer
            .replace(/<think>/g, '<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 12px;" open> <summary> 思考过程... </summary>')
            .replace(/<\/think>/g, '</details>');
        }

        array.push(
          {
            id: message.id,
            message: {
              type: 'local',
              content: message.query,
              suggested: false,
              fileList: userFiles
            },
            status: 'local'
          },
          {
            id: message.id,
            message: {
              type: 'ai',
              content: answer,
              suggested: false,
              fileList: aiFiles,
              feedback: message.feedback
            },
            status: 'success'
          }
        );
      });
      setMessages(array);
    }
  };

  const updateDefaultAgentStateByMessage = (message: any, currentAgent: AgentDict | null, defaultAgent: AgentDict | null, setDefaultAgentIsOpenThink: any, setLlmDefaultSelect: any) => {
    if (!message) {
      return;
    }

    if (currentAgent && currentAgent.id !== defaultAgent?.id) {
      return;
    }

    const input = message.inputs;
    if (input) {
      if (input.llm === 'QwQ:32B') {
        setDefaultAgentIsOpenThink(true);
        setLlmDefaultSelect('Qwen2.5:32B-instruct');
      } else if (input.llm === 'DeepSeek-R1:671B') {
        setDefaultAgentIsOpenThink(true);
        setLlmDefaultSelect('DeepSeek-R1:671B');
      } else if (input.llm === 'Qwen2.5-VL:32B-instruct') {
        setDefaultAgentIsOpenThink(false);
        setLlmDefaultSelect('Qwen2.5-VL:32B-instruct');
      } else {
        setDefaultAgentIsOpenThink(false);
        setLlmDefaultSelect('Qwen2.5:32B-instruct');
      }
    }
  };

  const onAddConversation = (llmSelect: string) => {
    const newKey = `auto-${Date.now()}`;
    setActiveKey(newKey);
  };

  const onConversationClick = (key: string) => {
    setActiveKey(key);
    if (!key.startsWith('auto')) {
      setBeLoadHistoryMessageConversationId(key);
    }
  };

  const handleConfirmRename = async (currentAgent: AgentDict | null) => {
    if (!currentAgent || !beRenameConversionId) {
      return;
    }

    if (!chatNameForm.getFieldValue('name')) {
      antdMessage.error('请输入新的名字');
      return;
    }

    try {
      if (renameModalType === 'topChat') {
        await topChatHistory(beRenameConversionId, true, chatNameForm.getFieldValue('name'));
      } else {
        await renameChatHistory(beRenameConversionId, chatNameForm.getFieldValue('name'));
      }
      
      chatNameForm.setFieldValue('name', '');
      setBeRenameConversionId('');
      setIsShowRenameModal(false);
      loadConversation();
      antdMessage.success('操作成功');
    } catch (error) {
      antdMessage.error('操作失败');
    }
  };

  const handleCancelRename = () => {
    chatNameForm.setFieldValue('name', '');
    setIsShowRenameModal(false);
  };

  const onCancel = async (currentAgent: AgentDict | null) => {
    if (currentAgent && currentTaskId) {
      await stop(currentAgent.id, currentTaskId);
      loadConversation(activeKey, currentAgent.id);
    }
  };

  return {
    // 状态
    conversationsItems,
    isBeRefreshConversations,
    activeKey,
    currentTaskId,
    beRenameConversionId,
    beLoadHistoryMessageConversationId,
    isShowRenameModal,
    renameModalType,
    messageFeedBackDict,
    chatNameForm,
    isBeRefreshConversationsRef,

    // 方法
    loadConversation,
    loadHistoryMessage,
    updateDefaultAgentStateByMessage,
    onAddConversation,
    onConversationClick,
    handleConfirmRename,
    handleCancelRename,
    onCancel,

    // 设置方法
    setConversationsItems,
    setIsBeRefreshConversations,
    setActiveKey,
    setCurrentTaskId,
    setBeRenameConversionId,
    setBeLoadHistoryMessageConversationId,
    setIsShowRenameModal,
    setRenameModalType,
    setMessageFeedBackDict,
  };
};

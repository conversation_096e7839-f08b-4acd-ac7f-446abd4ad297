import { useState } from 'react';
import { Upload, message as antdMessage, GetProp } from 'antd';
import { Attachments } from '@ant-design/x';
import { RcFile } from 'antd/es/upload/interface';
import { upload } from '../../../services/difyApi';
import { AgentParam } from '../../../types/agentParam';
import { DifyFile } from '../../../types/difyFile';

export const useFileUpload = () => {
  const [headerOpen, setHeaderOpen] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<GetProp<typeof Attachments, 'items'>>([]);

  const handleFileBeforeUpload = (file: RcFile, currentAgentParam: AgentParam | null) => {
    if (!currentAgentParam) {
      return false;
    }

    const fileType = file.type.split('/')[0];
    const fileName = file.name.toLowerCase();
    const fileExt = fileName.split('.').pop() || '';

    // 检查是否已有上传文件，如果有，检查新上传文件的类型是否与已上传文件类型一致
    if (attachedFiles && attachedFiles.length > 0) {
      const firstFile = attachedFiles[0];
      const firstFileType = firstFile.type?.split('/')[0];

      if (firstFileType !== 'image') {
        const firstFileName = firstFile.name?.toLowerCase();
        const firstFileExt = firstFileName?.split('.').pop() || '';
        const allowedExtensions = [
          'txt', 'md', 'mdx', 'markdown',
          'pdf', 'html', 'xlsx', 'xls',
          'docx', 'csv', 'eml', 'msg',
          'pptx', 'ppt', 'xml', 'epub'
        ];

        if (allowedExtensions.includes(firstFileExt)) {
          if (fileType === 'image' || !allowedExtensions.includes(fileExt)) {
            antdMessage.error(`不能混合上传不同类型的文件，当前已上传文档类型文件`);
            return Upload.LIST_IGNORE;
          }
        }
      } else if (firstFileType === 'image') {
        if (fileType !== 'image') {
          antdMessage.error(`不能混合上传不同类型的文件，当前已上传图片类型文件`);
          return Upload.LIST_IGNORE;
        }
      }
    }

    if (
      currentAgentParam.file_upload.allowed_file_types &&
      currentAgentParam.file_upload.allowed_file_types.length > 0
    ) {
      if (currentAgentParam.file_upload.allowed_file_types.includes(fileType)) {
        return true;
      } else if (currentAgentParam.file_upload.allowed_file_types.includes('document')) {
        const allowedExtensions = [
          'txt', 'md', 'mdx', 'markdown',
          'pdf', 'html', 'xlsx', 'xls',
          'docx', 'csv', 'eml', 'msg',
          'pptx', 'ppt', 'xml', 'epub'
        ];
        if (allowedExtensions.includes(fileExt)) {
          return true;
        } else {
          antdMessage.error(`不支持的文件类型: ${fileExt.toUpperCase()}`);
          return Upload.LIST_IGNORE;
        }
      } else {
        antdMessage.error(`不支持的文件类型: ${fileType}`);
        return Upload.LIST_IGNORE;
      }
    }

    if (
      currentAgentParam.file_upload.allowed_file_extensions &&
      currentAgentParam.file_upload.allowed_file_extensions.length > 0
    ) {
      if (currentAgentParam.file_upload.allowed_file_extensions.includes(fileExt)) {
        return true;
      } else {
        antdMessage.error(`不支持的文件类型: ${fileExt}`);
        return Upload.LIST_IGNORE;
      }
    }

    return true;
  };

  const handleFileUpload = async (file: any, currentAgentId: string | undefined) => {
    if (!currentAgentId) {
      return;
    }

    const encodedFileName = encodeURIComponent(file.file.name);
    const fileWithEncodedName = new File([file.file], encodedFileName, { type: file.file.type });

    const res = await upload(fileWithEncodedName, currentAgentId);
    file.onSuccess(res);
  };

  const handleFileChange: GetProp<typeof Attachments, 'onChange'> = (info) => {
    setAttachedFiles(info.fileList);
  };

  const convertAttachedFilesToDifyFiles = (): DifyFile[] => {
    const fileList: DifyFile[] = [];
    if (attachedFiles && attachedFiles.length > 0) {
      attachedFiles.forEach((file) => {
        let type = file.type?.split('/')[0] || '';
        if (type !== 'image') {
          const fileName = file.name?.toLowerCase() || '';
          const fileExt = fileName.split('.').pop() || '';
          const allowedExtensions = [
            'txt', 'md', 'mdx', 'markdown',
            'pdf', 'html', 'xlsx', 'xls',
            'docx', 'csv', 'eml', 'msg',
            'pptx', 'ppt', 'xml', 'epub'
          ];
          if (allowedExtensions.includes(fileExt)) {
            type = "document";
          }
        }

        fileList.push({
          type: type,
          transfer_method: 'local_file',
          upload_file_id: file.response?.id,
          file: file as any
        });
      });
    }
    return fileList;
  };

  const clearAttachedFiles = () => {
    setAttachedFiles([]);
    setHeaderOpen(false);
  };

  return {
    // 状态
    headerOpen,
    attachedFiles,

    // 方法
    handleFileBeforeUpload,
    handleFileUpload,
    handleFileChange,
    convertAttachedFilesToDifyFiles,
    clearAttachedFiles,

    // 设置方法
    setHeaderOpen,
    setAttachedFiles,
  };
};

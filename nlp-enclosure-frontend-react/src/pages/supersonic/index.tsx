import React, { useState, useEffect } from 'react';
import { Button, Spin, message } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeftOutlined } from '@ant-design/icons';

/**
 * SuperSonic 问答对话页面
 * 通过 iframe 集成 SuperSonic 的外部页面
 */
const SuperSonic: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();

  // 从 URL 参数中获取 agentId
  const queryParams = new URLSearchParams(location.search);
  const agentId = queryParams.get('agentId');

  // iframe 加载完成后隐藏加载状态
  const handleIframeLoad = () => {
    setLoading(false);
    message.success('SuperSonic 问答页面已加载完成');
  };

  // 返回主页
  const handleBack = () => {
    navigate('/home');
  };

  // 在组件挂载和 agentId 变化时重置加载状态
  useEffect(() => {
    setLoading(true);

    // 如果没有提供 agentId，显示警告并返回主页
    if (!agentId) {
      message.warning('未提供有效的 SuperSonic 智能体 ID，将返回主页');

      // 使用定时器并在组件卸载时清除
      const timer = setTimeout(() => {
        navigate('/home');
      }, 2000);

      // 返回清理函数，在组件卸载时执行
      return () => {
        clearTimeout(timer);
      };
    }

    // 对于有 agentId 的情况，也返回一个空的清理函数
    return () => {};
  }, [agentId, navigate]);

  // 构建 iframe 的 URL，包含必要的参数
  // TODO
  const iframeUrl = `http://localhost:9080/webapp/chat/external${agentId ? `?agentId=${agentId}` : ''}`;

  return (
    <div style={{ width: '100%', height: '100vh', position: 'relative', display: 'flex', flexDirection: 'column' }}>
      {/* 导航栏 */}
      <div style={{
        padding: '10px 20px',
        backgroundColor: '#f0f2f5',
        borderBottom: '1px solid #e8e8e8',
        display: 'flex',
        alignItems: 'center'
      }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={handleBack}
          type="link"
          style={{ marginRight: '10px' }}
        >
          返回主页
        </Button>
        <h3 style={{ margin: 0 }}>SuperSonic 问答对话</h3>
      </div>

      {/* 内容区域 */}
      <div style={{ flex: 1, position: 'relative' }}>
        {/* 加载状态 */}
        {loading && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            zIndex: 10
          }}>
            <Spin tip="正在加载 SuperSonic 问答页面..." size="large" />
          </div>
        )}

        {/* SuperSonic iframe */}
        <iframe
          src={iframeUrl}
          style={{
            border: "none",
            width: "100%",
            height: "100%",
            display: "block"
          }}
          onLoad={handleIframeLoad}
          title="SuperSonic 问答对话"
        />
      </div>
    </div>
  );
};

export default SuperSonic;

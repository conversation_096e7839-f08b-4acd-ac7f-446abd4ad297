import {
  Attachments,
  Bubble,
  BubbleProps,
  Conversations,
  ConversationsProps,
  PromptProps,
  Prompts,
  Sender,
  useXAgent,
  useXChat,
  Welcome,
  XStream
} from '@ant-design/x';
import { Conversation } from '@ant-design/x/es/conversations';
import markdownit from 'markdown-it';
import React, {useEffect, useRef, useState} from 'react';
import { createStyles } from 'antd-style';

import Icon, {
  AlignLeftOutlined,
  ArrowRightOutlined,
  CloudUploadOutlined,
  CopyOutlined,
  DeleteOutlined, DownOutlined,
  EditOutlined,
  MoreOutlined,
  FireOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PaperClipOutlined,
  PlusOutlined, PushpinOutlined,
  ReadOutlined, LikeOutlined, DislikeOutlined, LikeFilled, DislikeFilled
} from '@ant-design/icons';
import { MessageInfo } from '@ant-design/x/es/use-x-chat';
import {
  message as antdMessage,
  Avatar,
  Badge,
  Button,
  ConfigProvider,
  Divider,
  Dropdown,
  type GetProp,
  Image,
  Input,
  Layout,
  Menu,
  MenuProps,
  Modal,
  Popover,
  Space,
  Spin,
  Tooltip,
  Typography,
  Upload, Drawer, Flex, Form
} from 'antd';
import SubMenu from 'antd/es/menu/SubMenu';
import { RcFile } from 'antd/es/upload/interface';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { findByIsDefaultFalse, getAgentDictByIsDefaultTrue, deleteAgentDict, findAllVisible } from '../../services/agentDict.ts';
import {
  deleteConversation,
  message as getAgentMessage,
  conversations as getConversations,
  parameters,
  renameConversation,
  stop,
  streamApi,
  suggested,
  upload, feedback
} from '../../services/difyApi.ts';
import {deleteChatHistory, getChatHistoryList, renameChatHistory, topChatHistory} from "../../services/chatHistory.ts";
import { isSuperSonicAgent, createSuperSonicAgentParam } from '../../services/supersonic';
import SuperSonicChatWrapper from '../../components/SuperSonicChatWrapper';
import { getUserInfo, logout } from '../../services/user.ts';
import { Account } from '../../types/account.ts';
import {AgentDict, getColorPairByBgColor} from '../../types/agentDict.ts';
import { AgentParam } from '../../types/agentParam.ts';
import { DifyFile } from '../../types/difyFile.ts';
import { AgentMessage, RetrieverResource, StreamResponse } from '../../types/streamResponse.ts';
import welcomeIcon from '../home/<USER>/fmt.webp';
import agentSvg from '../home/<USER>/agent.svg'
import logoIcon from '../home/<USER>/logo.png';
import '../home/<USER>';


import { useIsMobile } from '../../utils/device.ts';
import AgentFormModal from "../../components/agentManager/agentFormModal.tsx";
import {buttonTheme} from "../../theme.tsx";
import {addUserAgent, deleteUserAgent, getUserAgentList, setTopUserAgent} from "../../services/userAgentList.ts";
import {UserAgentList} from "../../types/userAgentList.ts";

const { Text, Title } = Typography;

const { Header, Sider, Content } = Layout;

const md = markdownit({ html: true, breaks: true });

const renderTitle = (icon: React.ReactElement, title: string) => (
  <Space align="start">
    {icon}
    <span>{title}</span>
  </Space>
);

const useStyle = createStyles(({ token, css }) => {
  return {
    layout: css`
      width: 100%;
      min-width: 1000px;
      height: 100%;
      display: flex;
      background: ${token.colorBgContainer};
      font-family: AlibabaPuHuiTi, ${token.fontFamily}, sans-serif;

      .ant-prompts {
        color: ${token.colorText};
      }
    `,
    menu: css`
      background: ${token.colorBgLayout}80;
      width: 280px;
      height: 100%;
      display: flex;
      flex-direction: column;
    `,
    conversations: css`
      padding: 0 12px;
      flex: 1;
      overflow-y: auto;
      height: 100%;

      scrollbar-color: #888 transparent;
      scrollbar-width: thin;

      li {
        padding-left: 16px !important;
      }
    `,
    conversationsMenu: css`
      color: rgba(0, 0, 0, 0.88);
    `,
    chat: css`
      height: 100%;
      width: 100%;
      max-width: 700px;
      margin: 0 auto;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      padding: ${token.paddingLG}px;
      gap: 16px;
    `,
    messages: css`
      flex: 1;
    `,
    placeholder: css`
      padding-top: 32px;
      width: 100%;
    `,
    sender: css`
      box-shadow: ${token.boxShadow};
    `,
    logo: css`
      display: flex;
      height: 72px;
      align-items: center;
      justify-content: start;
      padding: 0 24px;
      box-sizing: border-box;

      img {
        width: 24px;
        height: 24px;
        display: inline-block;
      }

      span {
        display: inline-block;
        margin: 0 8px;
        font-weight: bold;
        color: ${token.colorText};
        font-size: 16px;
      }
    `,
    addBtn: css`
      background: #1677ff0f;
      border: 1px solid #1677ff34;
      width: calc(100% - 24px);
      margin: 0 12px 0 12px;
    `,
    suggestionPrompts: css`
      background-color: rgba(0, 0, 0, 0.04) !important;
      border-radius: 12px !important;
      color: rgba(0, 0, 0, 0.7) !important;
      padding: 10px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
        color: rgba(0, 0, 0) !important;
      }
    `
  };
});

const placeholderPromptsItems: GetProp<typeof Prompts, 'items'> = [
  {
    key: '1',
    label: renderTitle(<FireOutlined style={{ color: '#FF4D4F' }} />, '常用提示词'),
    description: '以下热门提示词，帮助你提升效率',
    children: [
      {
        key: '1-1',
        description: `总结下网页内容`
      },
      {
        key: '1-2',
        description: `这份公告有什么影响，什么时候生效`
      },
      {
        key: '1-3',
        description: `参考网页内容帮我写一份读后感`
      }
    ]
  }
];

const llmItems: MenuProps['items'] = [
  {
    key: 'Qwen2.5-32B-instruct',
    label: (
        <div className="llm-dropdown">
          <span className="title">Qwen2.5-32B-instruct</span>
          <span className="desc">多场景智能小能手</span>
        </div>
    )
  },
  {
    key: 'QwQ:32B',
    label: (
        <div className="llm-dropdown">
          <span className="title">QwQ:32B</span>
          <span className="desc">小体量强推理担当</span>
        </div>
    )
  },
  {
    key: 'DeepSeek-R1:671B',
    label: (
        <div className="llm-dropdown">
          <span className="title">DeepSeek-R1:671B</span>
          <span className="desc">千亿参数推理王者</span>
        </div>
    )
  },
]

const agentCardMoreItems: MenuProps['items'] = [
  {
    key: 'addToList',
    label: '添加至列表',
    icon: <PlusOutlined />
  },
  {
    key: 'edit',
    label: '编辑',
    icon: <EditOutlined />
  },
  {
    key: 'remove',
    label: '删除',
    icon: <DeleteOutlined />,
    danger: true
  }
];

// 声明全局变量用于存储当前页面内容
declare global {
  interface Window {
    current_page_content: string;
  }
}

// 初始化全局变量
window.current_page_content = '';

// 生成唯一请求ID的函数
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

const Independent: React.FC = () => {
  // ==================== Style ====================
  const { styles } = useStyle();

  // ==================== State ====================
  const isMobile = useIsMobile();

  const [isShowSpin, setIsShowSpin] = React.useState(false);

  const [headerOpen, setHeaderOpen] = React.useState(false);

  const [content, setContent] = React.useState('');

  const [conversationsItems, setConversationsItems] = React.useState<Conversation[]>([]);

  const [isBeRefreshConversations, setIsBeRefreshConversations] = React.useState(false);
  const isBeRefreshConversationsRef = useRef(isBeRefreshConversations);

  const [activeKey, setActiveKey] = React.useState<string>('auto');

  const [attachedFiles, setAttachedFiles] = React.useState<GetProp<typeof Attachments, 'items'>>(
    []
  );

  const [userItem, setUserItem] = React.useState<Account | null>(null);

  const [nodeMenuSelectedKey, setNodeMenuSelectedKey] = React.useState<string>('');

  // const [newName, setNewName] = React.useState('');
  const [chatNameForm] = Form.useForm<{
    name: string;
  }>();

  const [isShowRenameModal, setIsShowRenameModal] = React.useState(false);

  const [renameModalType, setRenameModalType] = React.useState<'rename' | 'topChat'>('rename');

  const [llmDefaultSelect, setLlmDefaultSelect] = React.useState(localStorage.getItem('llmDefaultSelect') || 'Qwen2.5:7B');

  const [contentShowState, setContentShowState] = React.useState<'defaultChat' | 'agent' | 'agentManager' | 'supersonic'>('defaultChat');

  // 存储当前选中的 SuperSonic agent ID
  const [currentSuperSonicAgentId, setCurrentSuperSonicAgentId] = React.useState<string | null>(null);

  const [isOpenAgentFormModal, setIsOpenAgentFormModal] = React.useState(false);

  const [isCanCreateAgent, setIsCanCreateAgent] = React.useState(false);

  const [agentFormData, setAgentFormData] = React.useState<AgentDict | null>(null);

  const [messageFeedBackDict, setMessageFeedBackDict] = React.useState<{
    [key: string]: string | null;
  }>({});

  // 添加 suggestedPrompts 状态
  const [suggestedPrompts, setSuggestedPrompts] = React.useState<any[]>([]);

  // Chrome插件相关状态
  const [capturedContent, setCapturedContent] = React.useState<string>('');
  const [currentRequestId, setCurrentRequestId] = React.useState<string>('');
  const [isWaitingForPluginResponse, setIsWaitingForPluginResponse] = React.useState(false);

  // 监听来自Chrome插件的消息
  useEffect(() => {
    // 添加消息监听器
    const handleMessage = (event: MessageEvent) => {
      // 验证消息来源
      if (event.source === window.parent) {
        const data = event.data;
        console.log('收到插件消息:', data);

        switch(data.type) {
          case 'pageContent':
            // 处理接收到的页面内容
            antdMessage.success(`成功获取页面内容: ${data.pageTitle}`);
            break;
          case 'activeTabInfo':
            // 处理标签页信息
            antdMessage.success(`当前标签页: ${data.title}`);
            break;
          case 'capturedContent':
            // 处理捕获的内容
            if (data.requestId === currentRequestId) {
              // 将内容设置到状态变量
              setCapturedContent(data.content);
              setIsWaitingForPluginResponse(false);
              // 将捕获的内容设置到输入框
              //setContent(data.content);
              // 将内容设置到全局变量
              window.current_page_content = data.content;
              console.log('已将页面内容保存到全局变量 current_page_content');
              antdMessage.success('成功获取页面内容');
            }
            break;
          case 'tabChanged':
            // 处理标签页切换事件
            antdMessage.info(`切换到标签页: ${data.title}`);
            break;
        }
      }
    };

    window.addEventListener('message', handleMessage);

    // 组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [currentRequestId]);

  // 向插件发送消息获取当前页面内容
  const requestPageContent = () => {
    return new Promise<string>((resolve) => {
      const requestId = generateUUID();
      setCurrentRequestId(requestId);
      setIsWaitingForPluginResponse(true);

      // 创建一个事件监听器，等待插件响应
      const messageHandler = (event: MessageEvent) => {
        if (event.source === window.parent) {
          const data = event.data;
          if (data.type === 'capturedContent' && data.requestId === requestId) {
            // 移除事件监听器，避免重复触发
            window.removeEventListener('message', messageHandler);
            // 解析Promise，返回捕获的内容
            resolve(data.content);
          }
        }
      };

      // 添加事件监听器
      window.addEventListener('message', messageHandler);

      // 发送消息给插件
      window.parent.postMessage({
        type: 'captureContent',
        requestId: requestId,
        tabId: null // 不指定则使用当前活动标签页
      }, '*');

      //antdMessage.info('正在获取页面内容...');

      // 添加超时处理，避免永远等待
      setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        resolve(''); // 超时返回空字符串
        setIsWaitingForPluginResponse(false);
        //antdMessage.error('获取页面内容超时');
      }, 10000); // 10秒超时
    });
  };

  // 获取当前标签页信息
  const requestTabInfo = () => {
    window.parent.postMessage({
      type: 'getActiveTabInfo'
    }, '*');
  };

  useEffect(() => {
    isBeRefreshConversationsRef.current = isBeRefreshConversations;
  }, [isBeRefreshConversations]);

  // ==================== Drawer ====================
  const [collapsed, setCollapsed] = React.useState(false);

  const [drawerOpen, setDrawerOpen] = React.useState(false);

  const [isLogoSpanVisible, setIsLogoSpanVisible] = React.useState(!collapsed || drawerOpen); // 新增状态变量

  // 延迟显示
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (!collapsed || drawerOpen) {
      timer = setTimeout(() => {
        setIsLogoSpanVisible(true);
      }, 200); // 延迟200毫秒
    } else {
      setIsLogoSpanVisible(false);
    }

    const display = collapsed ? 'none' : 'block';
    const element = document.getElementsByClassName("ant-conversations-icon");
    if (element.length > 0) {
      for (let i = 0; i < element.length; i++) {
        // 修改style
        element[i].style.display = display;
      }
    }

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [collapsed, drawerOpen]);

  // ==================== Agent ====================
  const [defaultAgent, setDefaultAgent] = React.useState<AgentDict | null>(null);

  const [agentList, setAgentList] = React.useState<AgentDict[]>([]);

  const [currentUserAgentList, setCurrentUserAgentList] = React.useState<UserAgentList[]>([]);

  const [currentAgent, setCurrentAgent] = React.useState<AgentDict | null>(null);

  const [currentAgentParam, setCurrentAgentParam] = React.useState<AgentParam | null>(null);

  const [currentTaskId, setCurrentTaskId] = React.useState<string | null>(null);

  const [beRenameConversionId, setBeRenameConversionId] = React.useState<string | null>(null);

  const [beLoadHistoryMessageConversationId, setBeLoadHistoryMessageConversationId] = React.useState('');

  useEffect(() => {
    if (currentAgent) {
      // 加载Agent配置
      // 加载历史聊天记录

      if (beLoadHistoryMessageConversationId) {
        loadAgentParameters(currentAgent).then((agentParam) => {
          loadHistoryMessage(beLoadHistoryMessageConversationId).then(() => {
            setBeLoadHistoryMessageConversationId('')
          });
        })
      } else {
        Promise.all([loadAgentParameters(currentAgent), loadConversation()]).then(
            ([agentParam, conversations]) => {
              setActiveKey(`auto-${conversations.length}`);
              initializeMessage(agentParam);
              if (content) {
                onSubmit(content);
              }
            }
        );
      }
    }
  }, [currentAgent]);

  // useEffect(() => {
  //   if (!activeKey.startsWith('auto') && currentAgent) {
  //     loadConversation(currentAgent);
  //   }
  // }, [activeKey]);

  // ========================== ServerFunction =========================

  const loadConversation = async (conversationId?: string, agentId?: string) => {
    // if (!agent) {
    //   return;
    // }

    if (conversationId && conversationId.startsWith('auto')) {
      return [];
    }

    // console.log('loadConversation', isBeRefreshConversationsRef.current)
    if (conversationId && agentId && !isBeRefreshConversationsRef.current) {
      return [];
    }

    setIsBeRefreshConversations(false);

    try {
      const conversations = await getChatHistoryList(conversationId, agentId);
      // console.log('Conversations:', conversations);
      if (conversations && conversations.length > 0) {
        // 转换格式
        const formattedConversations = conversations.map((conversation) => ({
              key: conversation.id,
              label: conversation.name,
              isTop: conversation.isTop,
              conversationId: conversation.conversationId,
              agentDict: conversation.agentDict,
              timestamp: new Date(conversation.createAt).getTime(),
              icon:
                  conversation.agentDict.isDefault ?
                      (<Avatar size="small" style={{ backgroundColor: 'rgb(243, 244, 246)'}}><AlignLeftOutlined style={{ color: 'rgba(0, 0, 0, 0.88)' }} /></Avatar>)
                      :
                      (<Avatar size="small" style={{
                        backgroundColor: conversation.agentDict.iconColor || '#dfdff8',
                        color: getColorPairByBgColor(conversation.agentDict.iconColor).textColor,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center'
                      }}
                      src={conversation.agentDict.icon && (conversation.agentDict.icon.startsWith('http://') || conversation.agentDict.icon.startsWith('https://'))
                        ? conversation.agentDict.icon
                        : undefined}
                      >
                        {conversation.agentDict.icon && !(conversation.agentDict.icon.startsWith('http://') || conversation.agentDict.icon.startsWith('https://')) ? (
                          <span style={{ fontSize: '14px' }}>{conversation.agentDict.icon}</span>
                        ) : (
                          !conversation.agentDict.icon && (conversation.agentDict.name ? conversation.agentDict.name[0] : '?')
                        )}
                      </Avatar>)
            })
        );

        // console.log('Formatted Conversations:', formattedConversations);
        setConversationsItems(formattedConversations);
        return formattedConversations;
      } else {
        setConversationsItems([]);
        return [];
      }
    } catch (ex) {
      console.error('Error loading conversations:', ex);
    }
    return [];
  };

  const loadAgentParameters = async (agent: AgentDict) => {
    if (!agent) {
      return;
    }

    // 如果是 SuperSonic 类型的 agent，使用内置参数
    if (isSuperSonicAgent(agent)) {
      const superSonicParam = createSuperSonicAgentParam();
      setCurrentAgentParam(superSonicParam);

      // 设置建议问题
      setSuggestedPrompts(superSonicParam.suggested_questions.map((question, index) => ({
        key: `supersonic-${index}`,
        description: question
      })));

      return superSonicParam;
    }

    // 其他类型的 agent 从服务器加载参数
    try {
      const agentParam = await parameters(agent.id);
      // console.log('agentParam:', agentParam);
      setCurrentAgentParam(agentParam);
      return agentParam;
    } catch (e) {
      console.error('Error loading agent parameters:', e);
      antdMessage.error('加载参数失败');
      return null;
    }
  };

  const loadHistoryMessage = async (conversationId: string) => {
    if (!currentAgent || !conversationId) {
      return;
    }

    const messageRes = await getAgentMessage(currentAgent.id, conversationId);
    // console.log('messageRes:', messageRes);

    if (messageRes.data && messageRes.data.length > 0) {
      setMessageFeedBackDict({});

      const array: MessageInfo<AgentMessage>[] = [];
      messageRes.data.forEach((message: any) => {
        let userFiles = [];
        let aiFiles = [];
        if (message.message_files && message.message_files.length > 0) {
          // 找message.message_files中 belongs_to === 'user'的对象，和 belongs_to ！== 'user'的对象,拆分成两个集合
          userFiles = message.message_files.filter((file: any) => file.belongs_to === 'local');
          // 循环 userFiles，处理里面的url 地址，需要加上 currentAgent的apiServerHost，并去掉'/v1'
          if (userFiles.length > 0) {
            userFiles.forEach((file: any) => {
              file.url = currentAgent.apiServerHost.replace('/v1', '') + file.url;
            });
          }

          aiFiles = message.message_files.filter((file: any) => file.belongs_to !== 'local');
          if (aiFiles.length > 0) {
            aiFiles.forEach((file: any) => {
              file.url = currentAgent.apiServerHost.replace('/v1', '') + file.url;
            });
          }
        }

        if (message.feedback && message.feedback.rating) {
          setMessageFeedBackDict((prev) => ({
            ...prev,
            [message.id]: message.feedback.rating
          }));
        }

        array.push(
            {
              id: message.id,
              message: {
                type: 'local',
                content: message.query,
                suggested: false,
                fileList: userFiles
              },
              status: 'local'
            },
            {
              id: message.id,
              message: {
                type: 'ai',
                content: message.answer,
                suggested: false,
                fileList: aiFiles,
                feedback: message.feedback
              },
              status: 'success'
            }
        );
      });
      setMessages(array);
    }
  };

  const initializeMessage = (agentParam: AgentParam) => {
    // console.log('initializeMessage', agentParam);
    if (!agentParam) {
      setMessages([]);
      return;
    }

    // 判断如果是默认agent，直接跳过
    if (currentAgent && currentAgent.id === defaultAgent?.id) {
      setMessages([]);
      return;
    }

    if (agentParam.opening_statement) {
      setMessages([
        {
          id: 'welcome',
          message: {
            type: 'ai',
            content: agentParam.opening_statement,
            suggested: false
          },
          status: 'success'
        }
      ]);
    } else {
      setMessages([]);
    }
  };

  const handleFileBeforeUpload = (file: RcFile) => {
    // console.log('fileType', file);

    if (!currentAgentParam) {
      return false;
    }

    const fileType = file.type.split('/')[0];
    // const fileExt = file.type.split('/')[1];

    const fileName = file.name.toLowerCase();
    const fileExt = fileName.split('.').pop() || '';

    if (
        currentAgentParam.file_upload.allowed_file_types &&
        currentAgentParam.file_upload.allowed_file_types.length > 0
    ) {
      // 判断image是否在包含在 fileType中
      if (currentAgentParam.file_upload.allowed_file_types.includes(fileType)) {
        return true;
      } else if (currentAgentParam.file_upload.allowed_file_types.includes('document')) {
        const allowedExtensions = [
          'txt', 'md', 'mdx', 'markdown',
          'pdf', 'html', 'xlsx', 'xls',
          'docx', 'csv', 'eml', 'msg',
          'pptx', 'ppt', 'xml', 'epub'
        ];
        if (allowedExtensions.includes(fileExt)) {
          return true;
        } else {
          antdMessage.error(`不支持的文件类型: ${fileExt.toUpperCase()}`);
          return Upload.LIST_IGNORE;
        }
      } else {
        antdMessage.error(`不支持的文件类型: ${fileType}`);
        return Upload.LIST_IGNORE;
      }
    }

    if (
        currentAgentParam.file_upload.allowed_file_extensions &&
        currentAgentParam.file_upload.allowed_file_extensions.length > 0
    ) {
      if (currentAgentParam.file_upload.allowed_file_types.includes(fileExt)) {
        return true;
      } else {
        antdMessage.error(`不支持的文件类型: ${fileExt}`);
        return Upload.LIST_IGNORE;
      }
    }
  };

  const handleFileUpload = async (file: any) => {
    // console.log('handleFileUpload', file);
    if (!currentAgent) {
      return;
    }

    const encodedFileName = encodeURIComponent(file.file.name);
    const fileWithEncodedName = new File([file.file], encodedFileName, { type: file.file.type });

    const res = await upload(fileWithEncodedName, currentAgent.id);
    // console.log('handleFileUpload res:', res);
    // console.log('handleFileUpload filelist', attachedFiles);

    file.onSuccess(res);
  };

  const handleLogoutBtn = () => {
    logout();
  };

  const handleAgentManagerClick = () => {
    setContentShowState('agentManager')
  }

  const handleCreateAgentBtnClick = () => {
    setAgentFormData(null);
    setIsOpenAgentFormModal(true);
  }

  const handleEditAgentBtnClick = (agent: AgentDict) => {
    setAgentFormData(agent);
    setIsOpenAgentFormModal(true);
  }

  const handleAddToList = (agent: AgentDict) => {
    const userAgent: UserAgentList = {
      agentDictId: agent.id,
    }
    return addUserAgent(userAgent).then(() => {
      antdMessage.success('添加成功');
      fetchAgentList();
    })
  }

  const handleFeedback = (message: string, rating: string | null) => {
    if (!currentAgent) {
      return;
    }

    // console.log('handleFeedback', message, rating);

    feedback(currentAgent.id, message, rating).then(() => {
      antdMessage.success('反馈成功');
      setMessageFeedBackDict((prevState) => ({
        ...prevState,
        [message]: rating
      }))
    })
  }

  // 等待获取页面内容后再继续执行
  const getPageContentAndProcess = async () => {
    try {
      console.log('开始获取当前页面内容');

      // 等待获取页面内容，这里会阻塞直到收到插件响应或超时
      const content = await requestPageContent();

      // 当收到内容后才会执行下面的代码
      console.log('获取到页面内容，长度：', content.length);
      console.log('全局变量内容长度：', window.current_page_content.length);

      // 这里可以对内容进行处理
      if (content && content.length > 0) {
        // 例如，将内容设置到输入框
        setContent(`我分析了这个页面，内容长度为 ${content.length} 字符。`);
        // 或者执行其他操作...
      } else {
        antdMessage.warning('获取到的页面内容为空');
      }

      return content; // 可以返回内容供调用者使用
    } catch (error) {
      console.error('获取页面内容时出错：', error);
      antdMessage.error('获取页面内容时出错');
      return '';
    }
  };

  // ==================== Runtime ====================
  const updateConversionId = React.useRef((conversationId: string) => {
    if (activeKey !== conversationId) {
      setActiveKey(conversationId);
    }
  });

  const updateTaskId = React.useRef((taskId: string) => {
    if (currentTaskId !== taskId) {
      setCurrentTaskId(taskId);
    }
  });

  const cancelRef = React.useRef(() => {});

  const fetchAgentList = async () => {
    const [userAgentListResult, agentListResult] = await Promise.all([getUserAgentList(), findAllVisible()]);

    // 获取 userAgentList 和 agentList 的结果
    const userAgentList = userAgentListResult;
    const agentList = agentListResult;

    if (userAgentList && userAgentList.length > 0) {
      userAgentList.forEach((userAgent: UserAgentList) => {
        if (userAgent.agentDict) {
          userAgent.agentDict.iconColor = userAgent.agentDict.iconColor || '#dfdff8';
          userAgent.agentDict.fontColor = getColorPairByBgColor(userAgent.agentDict.iconColor).textColor;
        }
      });
      setCurrentUserAgentList(userAgentList);
    } else {
      setCurrentUserAgentList([]);
    }


    if (agentList && agentList.payload) {
      // 填充颜色代码
      agentList.payload.forEach((agent: AgentDict) => {
        agent.iconColor = agent.iconColor || '#dfdff8';
        agent.fontColor = getColorPairByBgColor(agent.iconColor).textColor;

        // 检查 agent 的 id 是否存在于 userAgentList 中
        const userAgent = userAgentList.find((ua: UserAgentList) => ua.agentDictId === agent.id);
        if (userAgent) {
          agent.userAgentId = userAgent.id; // 如果存在，赋值 userAgentId
        }
      });
      setAgentList(agentList.payload);
    }

  };

  useEffect(() => {
    const fetchUserInfo = async () => {
      const userInfo = await getUserInfo();
      setUserItem(userInfo);
      if (userInfo && userInfo.workNo === '1044010100000218') {
        setIsCanCreateAgent(true);
      }
    };

    const fetchDefaultAgent = async () => {
      const defaultAgent = await getAgentDictByIsDefaultTrue();
      if (defaultAgent && defaultAgent.payload) {
        setDefaultAgent(defaultAgent.payload);
        setCurrentAgent(defaultAgent.payload);
      }
    };

    fetchDefaultAgent();
    fetchUserInfo();
    fetchAgentList();

    return () => {
      updateConversionId.current('');
      updateTaskId.current('');
      cancelRef.current();
    };
  }, []);

  const renderMarkdown: BubbleProps['messageRender'] = (content) => (
    <Typography>
      {/* biome-ignore lint/security/noDangerouslySetInnerHtml: used in demo */}
      <div dangerouslySetInnerHTML={{ __html: md.render(content) }} />
    </Typography>
  );

  const retrieverPopover = (groupedData: Record<string, RetrieverResource[]>) => {
    return Object.entries(groupedData).map(([documentId, items]) => {
      const documentName = items[0]?.document_name || '无标题';

      return (
        <ConfigProvider
          theme={buttonTheme}
        >
          <Popover
            key={documentId}
            title={documentName}
            trigger="click"
            content={retrieverPopoverContent(items)}
          >
            <Tooltip title={documentName}>
              <Button className="m-2 w-40 overflow-hidden">
                <span className="w-full text-xs truncate">{documentName}</span>
              </Button>
            </Tooltip>
          </Popover>
        </ConfigProvider>
      );
    });
  };

  const retrieverPopoverContent = (items: RetrieverResource[]) => {
    return (
      <div
        style={{
          width: '500px',
          height: '300px',
          overflow: 'auto',
          paddingRight: 10,
          scrollbarColor: '#888 transparent',
          scrollbarWidth: 'thin'
        }}
      >
        {items.map((item) => (
          <div
            style={{
              padding: '10px',
              border: '1px solid #e8e8e8',
              borderRadius: '4px',
              marginTop: '10px',
              marginBottom: '10px'
            }}
          >
            {/* 用户名 */}
            <div className="flex w-12 items-center px-1.5 h-5 border border-gray-200 rounded-md mb-1">
              <span className="text-[11px] font-medium text-gray-500">
                # {item.segment_position}
              </span>
            </div>

            {/* 内容区域 */}
            <Typography.Paragraph>{item.content}</Typography.Paragraph>
          </div>
        ))}
      </div>
    );
  };

  const [agent] = useXAgent<AgentMessage>({
    request: async ({ message }, { onSuccess, onUpdate, onError }) => {
      if (!message) {
        onError(new Error('No message'));
        return;
      }

      if (!message.content || !message.agentId) {
        onError(new Error('No message or agent'));
        return;
      }

      let content = '';
      const conversationId = message.conversationId?.startsWith('auto')
        ? undefined
        : message.conversationId;

      onUpdate({
        type: 'ai',
        content: '',
        suggested: message.suggested
      });

      // 当使用 Chat SDK 时，不需要流式 API
      const readableStream = streamApi(message.content, message.agentId, conversationId, message.fileList, message.inputs);

      const stream = XStream({
        readableStream: readableStream
      });

      const reader = stream.getReader();
      cancelRef.current = () => {
        reader?.cancel();
        if (!content) {
          onError(new Error('No message or agent'));
        }
      };

      while (reader) {
        const { value, done } = await reader.read();

        // console.log('output:', value, done);

        if (!value && done) {
          onSuccess({
            id: "",
            type: 'ai',
            content: content,
            suggested: message.suggested
          });
          break;
        }

        if (!value) continue;

        const response: StreamResponse = JSON.parse(value.data);

        if (done) {
          onSuccess({
            id: response.message_id,
            type: 'ai',
            content: content,
            suggested: message.suggested
          });
          break;
        }

        // console.log('response:', response);

        switch (value.event) {
          case 'workflow_started':
            updateConversionId.current(response.conversation_id);
            updateTaskId.current(response.task_id);
            break;
          case 'workflow_finished':
            break;
          case 'node_started':
            break;
          case 'node_finished':
            break;
          case 'message':
            content += response.answer;
            // console.log('update content:', content);
            onUpdate({
              id: response.message_id,
              type: 'ai',
              content: content,
              suggested: message.suggested
            });
            break;
          case 'message_end':
            {
              // console.log('success content:', content);

              let retrieverResources = [];
              // 获取 metadata 里面的内容，看看有没有引用
              if (
                response.metadata &&
                response.metadata.retriever_resources &&
                response.metadata.retriever_resources.length > 0
              ) {
                retrieverResources = response.metadata.retriever_resources;
              }

              let suggestion = [];
              // 判断agent参数
              if (message.suggested) {
                onUpdate({
                  id: response.message_id,
                  type: 'ai',
                  content: content,
                  suggested: message.suggested
                });
                // 获取下一步建议
                const suggests = await suggested(response.message_id, message.agentId);
                // console.log('suggests:', suggests);
                suggestion = suggests.data;
              }

              onSuccess({
                id: response.message_id,
                type: 'ai',
                content: content,
                suggested: message.suggested,
                list: suggestion,
                retrieverResources: retrieverResources
              });
            }
            break;
          case 'error':
            break;
        }
      }
    }
  });

  const { onRequest, messages, setMessages, parsedMessages } = useXChat({
    agent,
    parser: (msg) => {
      if (msg.list && msg.list.length > 0) {
        const array = [
          {
            id: msg.id,
            type: msg.type,
            content: msg.content,
            retrieverResources: msg.retrieverResources
          },
          {
            id: msg.id,
            type: 'suggestion',
            content: msg.list
          }
        ];
        // // console.log("array:", array)
        return array;
      }

      return msg;
    }
  });

  const items: GetProp<typeof Bubble.List, 'items'> = parsedMessages.map(
    ({ id, message, status }, index) => { // 添加 index 用于遍历消息
      console.log('messages:', id, message, status, index);

      let content;
      let loading = false; // 默认 loading 为 false
      let isShowFooter = true; // 控制页脚可见性的标志
      let lId = id; // 如果消息有 id，使用消息 id 作为 key

      if (message.id) {
        lId = message.id;
      }

      // 找到 parsedMessages 数组中最后一个 AI 消息的索引
      // const lastAIMessageIndex = parsedMessages.slice().reverse().findIndex(msg => msg.message.type === 'ai');
      // 判断当前消息是否是最后一个 AI 消息
      // const isLastAIMessage = lastAIMessageIndex !== -1 && index === parsedMessages.length - 1 - lastAIMessageIndex;

      // --- 根据消息类型和状态确定内容和 loading 状态 ---
      if (message.type === 'ai') {
        // 对于 AI 消息，如果它是最后一个 AI 消息且 agent 正在请求中，则设置 loading 为 true
        // 这会向 Bubble.List 发出信号，表明此消息正在被“打字”输出
        // console.log("agent.isRequesting", agent.isRequesting());
        // if (isLastAIMessage && agent.isRequesting()) {
        //   loading = true;
        // } else {
        //   // 确保已完成的 AI 消息 loading 为 false
        //   loading = false;
        // }

        // 如果内容可用，渲染 markdown 内容
        if (message.content && typeof message.content === 'string' && message.content.length > 0) {
          content = renderMarkdown(message.content);
        } else if (status === 'success') {
          content = ''; // 让 Bubble 处理打字动画显示
          isShowFooter = false; // 打字时隐藏页脚
        } else {
          loading = true;
        }

        // 处理成功 AI 消息的检索资源
        if (
          status === 'success' && // 只为成功消息显示资源
          currentAgentParam?.retriever_resource?.enabled &&
          message.retrieverResources &&
          message.retrieverResources.length > 0
        ) {
          loading = false; // 显示资源时确保 loading 为 false
          // 按文档 ID 分组检索资源
          const grouped: Record<string, RetrieverResource[]> = {};
          message.retrieverResources.forEach((reteriever) => {
            const key = reteriever.document_id;
            if (!grouped[key]) {
              grouped[key] = [];
            }
            grouped[key].push(reteriever);
          });

          // 结合 markdown 内容和检索链接
          content = (
            <>
              {renderMarkdown(message.content)}
              <div>
                <Divider
                  style={{ margin: '5px 0', borderColor: 'rgba(0,0,0,.25)' }}
                  orientation="left"
                >
                  引用
                </Divider>
                <div>{retrieverPopover(grouped)}</div>
              </div>
            </>
          );
        }

      } else if (message.type === 'local') {

        // 渲染用户消息内容和附件
        if (message.content && typeof message.content === 'string' && message.content.length > 0) {

            // 只渲染用户的文本内容
            content = renderMarkdown(message.content);

        } else if (loading) {
          // 本地消息正在加载但没有内容 (例如，发送没有文本的文件)
          content = '发送中...';
          isShowFooter = false; // 发送时隐藏页脚
        } else {
          // 如果不是加载中 (发送成功)，应该有内容
          content = ''; // 或者处理空消息的情况
        }

      } else if (message.type === 'suggestion') {
        // 处理建议消息
        content = message.content; // 内容是建议数组
        loading = false; // 建议不加载
        isShowFooter = false; // 建议没有页脚
      } else {
        // 处理其他潜在的消息类型或初始状态
        content = message.content;
        loading = false;
        isShowFooter = false;
      }

      // --- 确定页脚可见性和内容 ---
      let footer = null;
      // 仅当不加载且 isShowFooter 为 true (且状态为 success 用于最终内容，如反馈) 时显示页脚
      // 根据您希望页脚何时出现来调整此条件 (例如，仅用于已完成的 AI 回复)
      if (!loading && isShowFooter && status === 'success' && message.type === 'ai') { // 仅为已完成的 AI 消息显示页脚
        footer = (
          <div
            style={{ display: 'flex', justifyContent: 'space-between', width: '96%', marginLeft: 10 }}
          >
            <div>
              {/* 复制按钮 */}
              <Tooltip title="复制">
                <CopyToClipboard text={typeof message.content === 'string' ? message.content : ''} onCopy={() => {
                  antdMessage.success('内容已复制到剪贴板');
                }}>
                  <Button
                    color="default"
                    variant="text"
                    size="small"
                    icon={<CopyOutlined />}
                  />
                </CopyToClipboard>
              </Tooltip>
            </div>
            {/* 反馈按钮 (点赞/点踩) */}
            <div className="flex items-center">
              <Tooltip title={messageFeedBackDict[lId] === 'like' ? '取消点赞' : '点赞'}>
                <Button
                  color="default"
                  variant="text"
                  size="small"
                  icon={messageFeedBackDict[lId] === 'like' ? <LikeFilled style={{color: '#1890ff'}} /> : <LikeOutlined />}
                  onClick={() => handleFeedback(lId, messageFeedBackDict[lId] === 'like' ? null : 'like')}
                  style={{ marginRight: 10}}
                />
              </Tooltip>
              <Tooltip title={messageFeedBackDict[lId] === 'dislike' ? '取消点踩' : '点踩'}>
                <Button
                  color="default"
                  variant="text"
                  size="small"
                  icon={messageFeedBackDict[lId] === 'dislike' ? <DislikeFilled style={{color: '#dc2626'}} /> : <DislikeOutlined />}
                  onClick={() => handleFeedback(lId, messageFeedBackDict[lId] === 'dislike' ? null : 'dislike')}
                />
              </Tooltip>
            </div>
          </div>
        );
      }

      // 在 AI 消息成功后延迟加载对话 (原逻辑)
      // 这可能需要根据您期望的行为进行调整
      if (status === 'success' && message.type === 'ai' && id !== 'welcome' && currentAgent) {
        // console.log("test123", status, message, id)
        setTimeout(() => {
          loadConversation(activeKey, currentAgent.id).then()
        }, 1000)
      }

      // 返回用于 Bubble.List 的消息项结构
      return {
        key: lId, // 消息项的唯一 key
        loading: loading, // 传递确定的 loading 状态
        role: message.type, // 使用 message.type 作为角色 ('ai', 'local', 'suggestion')
        content: content, // 要显示的消息内容
        footer: footer, // 消息页脚 (复制、反馈等)
      };
    }
  );

  const roles: GetProp<typeof Bubble.List, 'roles'> = {
    ai: {
      placement: 'start',
      typing: {step: 1, interval: 50},
      styles: {
        content: {
          backgroundColor: 'rgba(255,255,255)',
          marginLeft: 10,
          marginRight: 10,
          paddingBottom: 0
        },
        footer: {
          width: '100%'
        }
      }
    },
    local: {
      placement: 'end',
      styles: {
        content: {
          backgroundColor: 'rgba(0,0,0,0.04)',
          color: 'rgba(0,0,0,0.85)',
          borderRadius: 12,
          fontSize: 15,
          marginLeft: 10,
          marginRight: 10
        },
        footer: {
          width: '100%'
        }
      },
      shape: 'round',
    },
    suggestion: {
      placement: 'start',
      variant: 'borderless',
      messageRender: (content) => (
        <Prompts
          vertical={true}
          items={(content as any as string[]).map((text) => ({
            key: text,
            description: (
              <div>
                <span>{text}</span>
                <span style={{ marginLeft: 10 }}>
                  <ArrowRightOutlined />
                </span>
              </div>
            )
          }))}
          onItemClick={(key) => {
            handleSuggestionPromptsClick(key.data);
          }}
          style={{
            marginLeft: 20
          }}
          classNames={{
            item: styles.suggestionPrompts
          }}
        />
      )
    }
  };

  const conversationsMenu: ConversationsProps['menu'] = (conversation) => ({
    items: [
      {
        label: conversation['isTop'] ? '取消固定' : '固定',
        key: 'top',
        icon: <PushpinOutlined />
      },
      {
        label: '重命名',
        key: 'rename',
        icon: <EditOutlined />
      },
      {
        label: '删除',
        key: 'delete',
        icon: <DeleteOutlined />,
        danger: true
      }
    ],
    onClick: (menuInfo) => {
      // console.log(`Click ${conversation.key} - ${menuInfo.key}`, conversation);
      if (!currentAgent) {
        return;
      }
      const isTop: boolean = conversation['isTop'];

      // 根据menuInfo.key分别调用不同的方法
      switch (menuInfo.key) {
        case 'top':
          if (!isTop) {
            setRenameModalType('topChat');
            setBeRenameConversionId(conversation.key);
            chatNameForm.setFieldValue('name', conversation.label);
            // setNewName(conversation.label);
            setIsShowRenameModal(true);
          } else {
            topChatHistory(conversation.key, false).then((res) => {
              if (res) {
                loadConversation();
                antdMessage.success('操作成功');
              }
            })
          }
          setRenameModalType('topChat');

          break;
        case 'rename':
          setRenameModalType('rename');
          setBeRenameConversionId(conversation.key);
          chatNameForm.setFieldValue('name', conversation.label);
          // setNewName(conversation.label);
          setIsShowRenameModal(true);
          break;
        case 'delete':
          setIsShowSpin(true);
          deleteChatHistory(conversation.key).then((res) => {
          // deleteConversation(currentAgent.id, conversation.key).then((res) => {
            if (res) {
              setIsShowSpin(false);
              loadConversation();
              antdMessage.success('删除成功');
            }
          });
          break;
      }
    },
    trigger: (menuInfo) => {
      if (collapsed) {
        return (<></>);
      }
      return menuInfo['isTop'] ?
          (<PushpinOutlined style={{color: 'rgba(0, 0, 0, 0.88)'}}
            onClick={(event) => {
              event.stopPropagation();
            }}
          />) :
          (<MoreOutlined style={{color: 'rgba(0, 0, 0, 0.88)'}}
              onClick={(event) => {
                event.stopPropagation();
              }}
          />)
    },
  });

  const agentListMoreItems = (userAgent: UserAgentList): MenuProps['items'] => {
    return [
        {
          key: 'top',
          label: userAgent.isTop ? '取消固定' : '固定',
          icon: <PushpinOutlined />
        },
        {
          key: 'remove',
          label: '删除',
          icon: <DeleteOutlined />,
          danger: true
        }
      ]
  };

  // ==================== Event ====================
  const handleConfirmRename = () => {
    if (!currentAgent || !beRenameConversionId) {
      return;
    }

    if (!chatNameForm.getFieldValue('name')) {
      antdMessage.error('请输入新的名字');
      return;
    }

    setIsShowSpin(true);

    if (renameModalType === 'topChat') {
      topChatHistory(beRenameConversionId, true, chatNameForm.getFieldValue('name')).then((res) => {
        if (res) {
          setIsShowSpin(false);
          chatNameForm.setFieldValue('name', '');
          setBeRenameConversionId('');
          setIsShowRenameModal(false);
          loadConversation();
          antdMessage.success('操作成功');
        }
      })
    } else {
      renameChatHistory(beRenameConversionId, chatNameForm.getFieldValue('name')).then((res) => {
        // renameConversation(currentAgent.id, beRenameConversionId, newName).then((res) => {
        if (res) {
          setIsShowSpin(false);
          chatNameForm.setFieldValue('name', '');
          // setNewName('');
          setBeRenameConversionId('');
          setIsShowRenameModal(false);
          loadConversation();
        }
      });
    }
  };

  const handleCancelRename = () => {
    setIsShowSpin(false);
    chatNameForm.setFieldValue('name', '');
    // setNewName('');
    setIsShowRenameModal(false);
  };

  const onSubmit = (nextContent: string, agent?: AgentDict) => {
    if (!nextContent || !currentAgent) return;

    let defAgent = currentAgent;

    if (agent) {
      defAgent = agent;
    }
    const fileList: DifyFile[] = [];
    if (attachedFiles && attachedFiles.length > 0) {
      attachedFiles.forEach((file) => {
        let type = file.type.split('/')[0];
        if (type !== 'image') {
          const fileName = file.name.toLowerCase();
          const fileExt = fileName.split('.').pop() || '';
          const allowedExtensions = [
            'txt', 'md', 'mdx', 'markdown',
            'pdf', 'html', 'xlsx', 'xls',
            'docx', 'csv', 'eml', 'msg',
            'pptx', 'ppt', 'xml', 'epub'
          ];
          if (allowedExtensions.includes(fileExt)) {
            type = "document";
          }
        }

        fileList.push({
          type: type,
          transfer_method: 'local_file',
          upload_file_id: file.response.id,
          file: file
        });
      });
    }
    // 使用异步方式等待页面内容
    getPageContentAndProcess().then(async(content) => {
      if (content && content.length > 0) {
        console.log("page_content",content);
        //把content的内容转成txt格式的file文件
        const file = new File([content], 'page_content.txt', { type: 'text/plain' });
        const fileList: DifyFile[] = [];
        //使用upload方法等待返回值res
        const res =await upload(file, currentAgent.id);
        //
        console.log("res",res.id);
        fileList.push({
          type: 'document',
          transfer_method: 'local_file',
          upload_file_id: res.id,
          file: file
        });
        //帮网页内容放入Inputs参数里面
        // 判断是否是默认agent，如果是就添加 LLM 参数
        let inputs = {};
        if (currentAgent.isDefault) {
          inputs = {
            'llm': llmDefaultSelect
          }
        }
        // 自动发送消息，使用原始的发送逻辑，避免递归
        const agentMessage: AgentMessage = {
          type: 'local',
          agentId: defAgent.id,
          conversationId: activeKey,
          suggested: currentAgentParam && currentAgentParam.suggested_questions_after_answer
            ? currentAgentParam.suggested_questions_after_answer.enabled
            : false,
          content: nextContent,
          fileList:fileList,
          inputs: inputs
        };

        setIsBeRefreshConversations(true);
        onRequest(agentMessage);
        setContent('');
        setAttachedFiles([]);
        setHeaderOpen(false);
      }
    });
    return; // 不发送消息，等待插件响应
  };

  const onCancel = () => {
    // console.log('onCancel');
    if (currentAgent && currentTaskId) {
      stop(currentAgent.id, currentTaskId).then(() => {
        loadConversation(activeKey, currentAgent.id).then()
      });
      cancelRef.current();
    }
  };

  const onPromptsItemClick: GetProp<typeof Prompts, 'onItemClick'> = (info) => {
    // console.log('onPromptsItemClick', info);
    // onRequest(info.data.description as string);
    setContent(info.data.description as string);
    onSubmit(info.data.description as string);
  };

  const onAddConversation = () => {
    // console.log('onAddConversation', activeKey);
    setContentShowState('defaultChat')
    setCurrentAgent(defaultAgent);
    // if (!activeKey.startsWith('auto') && defaultAgent) {
    //   loadConversation(defaultAgent);
    // }
    setActiveKey(`auto-${conversationsItems.length + 1}`);
    setMessages([]);
    setNodeMenuSelectedKey('');

    if (isMobile) {
      setDrawerOpen(false);
    }
  };

  const onConversationClick: GetProp<typeof Conversations, 'onActiveChange'> = (key) => {
    // console.log('onConversationClick', key);
    onCancel();

    // 根据 key 在conversationsItems 中查找对应的 对象
    const selectedConversation = conversationsItems.find((item) => item.key === key);
    if (selectedConversation) {
      setActiveKey(selectedConversation.conversationId);
      setBeLoadHistoryMessageConversationId(selectedConversation.conversationId);

      // 判断AgentId和当前的currentAgentId 是不是同一个 ID
      if (selectedConversation.agentDictId !== currentAgent?.id) {
        // 如果不是同一个 ID，则设置当前 agent 为选中的 agent
        handleAgentClick(selectedConversation.agentDict);
        // 注意：handleAgentClick 函数已经会根据 agent 类型设置正确的 contentShowState
      } else {
        // 如果是同一个 ID，则直接加载历史消息

        // 无论是什么类型，都设置为问答页面
        setContentShowState('defaultChat');

        loadHistoryMessage(selectedConversation.conversationId).then(() => {
          setBeLoadHistoryMessageConversationId('');
        });
      }
    } else {
      setContentShowState('defaultChat');
      setActiveKey(key);
    }

    if (isMobile) {
      setDrawerOpen(false);
    }
  };

  const handleFileChange: GetProp<typeof Attachments, 'onChange'> = (info) => {
    setAttachedFiles(info.fileList);
    // console.log('uploadOnchange', info);
  };

  const handleLoginBtn = () => {
    // console.log('login', localStorage.getItem('userItem'));
  };

  const handleAgentClick = (agent: AgentDict) => {
    // console.log('Selected Agent:', agent);
    onCancel();

    // 如果是SuperSonic类型，在当前页面显示 iframe
    if (agent.type === 'supersonic') {
      antdMessage.info('正在加载 SuperSonic 问答页面...', 2);

      // 设置当前 SuperSonic agent ID
      setCurrentSuperSonicAgentId(agent.id);

      // 如果有自定义的 SuperSonic 服务器地址和 Agent ID，保存到当前参数中
      if (agent.superSonicServerHost) {
        console.log(`使用自定义 SuperSonic 服务器地址: ${agent.superSonicServerHost}`);
      }

      if (agent.superSonicAgentId) {
        console.log(`使用自定义 SuperSonic Agent ID: ${agent.superSonicAgentId}`);
      }

      // 切换到 SuperSonic 内容状态
      setContentShowState('supersonic');
    } else {
      // 其他类型的agent保持原有行为
      setContentShowState('agent');
      // 如果不是 SuperSonic 类型，从服务器加载参数
      loadAgentParameters(agent);
    }

    setCurrentAgent(agent);
    setNodeMenuSelectedKey(agent.id);

    if (isMobile) {
      setDrawerOpen(false);
    }
  };

  const handleSuggestionPromptsClick = (item: PromptProps) => {

    // 如果是 SuperSonic 的建议问题，直接提交问题内容
    if (item.key.toString().startsWith('supersonic-')) {
      // 使用 description 而不是 title，因为 PromptProps 没有 title 属性
      // 将 description 转换为字符串
      onSubmit(String(item.description || ''));
    } else {
      // 其他类型的建议问题，按原来的方式处理
      onSubmit(item.key);
    }
  };

  const handleLLMDropDownClick = (info: any) => {
    // console.log('handleLLMDropDownClick', info);
    setLlmDefaultSelect(info.key);
    localStorage.setItem('llmDefaultSelect', info.key)
    onAddConversation();
  }

  const handleAgentCardMoreDropDownClick = (info: any, item: AgentDict) => {
    // console.log('handleAgentCardMoreDropDownClick', info);
    const event = info.domEvent; // 获取事件对象
    event.stopPropagation(); // 阻止事件冒泡
    switch (info.key) {
      case 'addToList': {
        if (item.userAgentId) {
          antdMessage.error('该智能体已添加到列表中，请勿重复添加');
          return;
        }
        handleAddToList(item);
      }
      break;
      case 'remove': {
        // 删除当前 agent
        deleteAgentDict(item.id).then(() => {
          // console.log('deleteAgentDict');
          antdMessage.success('删除成功');
          fetchAgentList();
          loadConversation();
        })
      }
        break;
      case 'edit': {
        // 编辑当前 agent
        handleEditAgentBtnClick(item);
      }
        break;
    }
  }

  const handleAgentListMoreDropDownClick = (menuInfo: any, userAgent: UserAgentList) => {
    // console.log('handleAgentListMoreDropDownClick', menuInfo);
    if (!userAgent) {
      return;
    }

    switch (menuInfo.key) {
      case 'top':
        setTopUserAgent(userAgent.id, !userAgent.isTop).then(() => {
          fetchAgentList();
          antdMessage.success('操作成功');
        })
        break;
      case 'remove':
        setIsShowSpin(true);
        deleteUserAgent(userAgent.id).then(() => {
          fetchAgentList();
          antdMessage.success('删除成功');
        }).finally(() => {
          setIsShowSpin(false);
        })
    }

  }

  const handleAgentManagerAgentClick = (agent: AgentDict) => {
    // console.log('handleAgentManagerAgentClick', agent);
    if (isCanCreateAgent) {
      return;
    }

    if (agent.userAgentId) {
      antdMessage.error('该智能体已添加到列表中，请勿重复添加');
      return; // 如果已添加，直接返回，不执行后续操作
    }

    // 添加到列表
    handleAddToList(agent).then((addedAgent) => {
      // 如果是 SuperSonic 类型，添加后自动在当前页面显示
      if (addedAgent && addedAgent.type === 'supersonic') {
        // 显示提示消息
        antdMessage.success('已添加 SuperSonic 智能体，正在加载问答页面...');

        // 设置当前 SuperSonic agent ID
        setCurrentSuperSonicAgentId(addedAgent.id);

        // 切换到 SuperSonic 内容状态
        setContentShowState('supersonic');

        // 设置当前 Agent 和菜单选中状态
        setCurrentAgent(addedAgent);
        setNodeMenuSelectedKey(addedAgent.id);
      }
    });
  }

  // ==================== Nodes ====================
  const placeholderNode = (
    <Space direction="vertical" size={16} className={styles.placeholder}>
      <Welcome
        variant="borderless"
        icon={<img alt="" src={welcomeIcon} />}
        title={
          currentAgent ? `你好！` : '你好, 你的超级智能体已加载！'
        }
        // description={currentAgentParam?.opening_statement}
      />
      {currentAgent && currentAgent.isDefault && (
          <Prompts
              title="我是广州烟草浏览器AI数智助手"
              items={placeholderPromptsItems}
              styles={{
                list: {
                  width: '100%',
                  overflowX: 'hidden'
                },
                item: {
                  width: '300px'
                }
              }}
              onItemClick={onPromptsItemClick}
          />
      )}
    </Space>
  );

  const attachmentsNode = (
    <Badge dot={attachedFiles.length > 0 && !headerOpen}>
      <Button type="text" icon={<PaperClipOutlined />} onClick={() => setHeaderOpen(!headerOpen)} />
    </Badge>
  );

  const senderHeader = (
    <Sender.Header
      title="附件上传"
      open={headerOpen}
      onOpenChange={setHeaderOpen}
      styles={{
        content: {
          padding: 0
        }
      }}
    >
      <Attachments
        beforeUpload={handleFileBeforeUpload}
        items={attachedFiles}
        onChange={handleFileChange}
        customRequest={handleFileUpload}
        placeholder={(type) =>
          type === 'drop'
            ? { title: '拖动文件到此处' }
            : {
                icon: <CloudUploadOutlined />,
                title: '附件上传',
                description: '单击或拖动文件到此区域上传附件'
              }
        }
      />
    </Sender.Header>
  );

  const logoNode = (
    <div className={styles.logo}>
      <img alt="" src={logoIcon} />
      {isLogoSpanVisible && <span>广州烟草AI门户</span>}
    </div>
  );

  const newConversitionNode = (
      <div className="flex w-full justify-center flex-row ">
        <Button
            onClick={onAddConversation}
            type="link"
            className={styles.addBtn}
            icon={<PlusOutlined />}
            disabled={!currentAgent}
        >
          {isLogoSpanVisible && '新的对话'}
        </Button>
      </div>
  )

  const historicalDialoguesNode = (
    <>
      <div className="p-3 w-full flex items-center" style={{ color: '#0000004d' }}>
              <span className="pl-4 text-xs whitespace-nowrap overflow-hidden text-ellipsis">
                历史对话
              </span>
      </div>
      <div className="h-[43%] flex-1 overflow-y-auto">
        <Conversations
            items={conversationsItems}
            className={styles.conversations}
            activeKey={activeKey}
            onActiveChange={onConversationClick}
            menu={conversationsMenu}
            styles={{item: {
              marginRight: 8
              }}}
        />
      </div>
    </>
  )

  const loginNode = (
    <Button type="primary" onClick={() => handleLoginBtn()}>
      登录
    </Button>
  );

  const userNodeDropdown: MenuProps['items'] = [
    {
      key: '1',
      label: '登出'
    }
  ];

  const handleUserNodeDropDownItemClick: MenuProps['onClick'] = ({ key }) => {
    if (key === '1') {
      handleLogoutBtn();
    }
  };

  const userNode = (
    <Dropdown
      className="h-36px"
      menu={{ items: userNodeDropdown, onClick: handleUserNodeDropDownItemClick }}
    >
      <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
        <Avatar style={{ backgroundColor: '#1677ff', marginRight: 8 }}>
          {userItem?.name ? userItem.name[0] : '?'}
        </Avatar>
        <span>{userItem?.name || '未知用户'}</span>
      </div>
    </Dropdown>
  );

  const agentListNode = (
    <div className="pt-3 pr-3 pl-3 min-h-[30%]" style={{ color: 'rgba(0, 0, 0, 0.88)' }}>
      <ConfigProvider
        theme={{
          components: {
            Menu: {
              /* 这里是你的组件 token */
              popupBg: 'rgb(243, 244, 246)'
            }
          }
        }}
      >
        <Menu
          mode="vertical"
          style={{ backgroundColor: 'rgb(243, 244, 246)', border: 'None' }}
          selectedKeys={[nodeMenuSelectedKey]}
          triggerSubMenuAction={'click'}
        >
          {/* 前4个Agent作为一级菜单 */}
          {currentUserAgentList.slice(0, 5).map((item) => {
            if (item.agentDict) {
              return (
                  <Menu.Item
                      key={item.id}
                      onClick={() => handleAgentClick(item.agentDict)}
                      style={{ margin: '10px 0', width: '100%', display: 'flex', alignItems: 'center' }}
                  >
                    <div className="w-full flex items-center justify-between">
                      <div className="flex items-center cursor-pointer justify-start" style={{ width: '100%' }}>
                        <Avatar size="small" style={{
                          backgroundColor: item.agentDict.iconColor,
                          color: item.agentDict.fontColor,
                          marginRight: 8,
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center'
                        }}
                        src={item.agentDict.icon && (item.agentDict.icon.startsWith('http://') || item.agentDict.icon.startsWith('https://'))
                          ? item.agentDict.icon
                          : undefined}
                        >
                          {item.agentDict.icon && !(item.agentDict.icon.startsWith('http://') || item.agentDict.icon.startsWith('https://')) ? (
                            <span style={{ fontSize: '14px' }}>{item.agentDict.icon}</span>
                          ) : (
                            !item.agentDict.icon && (item.agentDict.name ? item.agentDict.name[0] : '?')
                          )}
                        </Avatar>
                        <div className="w-full max-w-[150px]">
                          <div className="truncate w-full text-sm font-medium">
                            {item.agentDict.name}
                            {/* 如果是SuperSonic类型，显示特殊标记 */}
                            {item.agentDict.type === 'supersonic' && (
                              <span style={{
                                marginLeft: '5px',
                                color: '#00bfff',
                                fontWeight: 'bold',
                                fontSize: '10px'
                              }}>
                                SuperSonic
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div
                          style={{display: collapsed ? 'none' : 'block'}}
                          onClick={(event) => {
                        event.stopPropagation();
                      }}>
                        <Dropdown
                            menu={{
                          items: agentListMoreItems(item),
                          onClick: (info) => handleAgentListMoreDropDownClick(info, item)
                        }}
                            trigger={['click']}
                        >
                          {item.isTop ? (<PushpinOutlined />) : (<MoreOutlined />)}
                        </Dropdown>
                      </div>
                    </div>
                  </Menu.Item>
              );
            }
            return (<></>);
          })}

          {/* 剩余Agent放在"更多"二级菜单中 */}
          {currentUserAgentList.length > 5 && (
            <SubMenu
              key="more"
              title="更多"
              style={{ margin: '10px 0', width: '100%', display: 'flex', alignItems: 'center' }}
            >
              {currentUserAgentList.slice(5).map((item) => {
                if (item.agentDict) {
                  return (
                      <Menu.Item
                          key={item.id}
                          onClick={() => handleAgentClick(item.agentDict)}
                          style={{
                            margin: '5px 4px',
                            display: 'flex',
                            alignItems: 'center',
                            borderRadius: '10px'
                          }}
                      >
                        <div className="w-full flex items-center justify-between">
                          <div className="flex items-center cursor-pointer justify-start" style={{ width: '100%' }}>
                            <Avatar size="small" style={{
                              backgroundColor: item.agentDict.iconColor,
                              color: item.agentDict.fontColor,
                              marginRight: 8,
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center'
                            }}
                            src={item.agentDict.icon && (item.agentDict.icon.startsWith('http://') || item.agentDict.icon.startsWith('https://'))
                              ? item.agentDict.icon
                              : undefined}
                            >
                              {item.agentDict.icon && !(item.agentDict.icon.startsWith('http://') || item.agentDict.icon.startsWith('https://')) ? (
                                <span style={{ fontSize: '14px' }}>{item.agentDict.icon}</span>
                              ) : (
                                !item.agentDict.icon && (item.agentDict.name ? item.agentDict.name[0] : '?')
                              )}
                            </Avatar>
                            <div>
                              <div className="text-sm font-medium">
                                {item.agentDict.name}
                                {/* 如果是SuperSonic类型，显示特殊标记 */}
                                {item.agentDict.type === 'supersonic' && (
                                  <span style={{
                                    marginLeft: '5px',
                                    color: '#00bfff',
                                    fontWeight: 'bold',
                                    fontSize: '10px'
                                  }}>
                                    SuperSonic
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                          <div
                              onClick={(event) => {
                            event.stopPropagation();
                          }}
                              style={{marginLeft: 30}}
                          >
                            <Dropdown
                                menu={{
                                  items: agentListMoreItems(item),
                                  onClick: (info) => handleAgentListMoreDropDownClick(info, item)
                                }}
                                trigger={['click']}
                            >
                              <MoreOutlined />
                            </Dropdown>
                          </div>
                        </div>
                      </Menu.Item>
                  );
                }
                return (<></>);
              })}
            </SubMenu>
          )}

          {/* Agent管理器 */}
          <Menu.Item
              key="agentManager"
              onClick={() => handleAgentManagerClick()}
              style={{ margin: '10px 0', width: '100%', display: 'flex', alignItems: 'center'}}
          >
            <div className="flex items-center cursor-pointer" style={{ width: '100%' }}>
              <Avatar size="small" style={{ backgroundColor: 'rgb(243, 244, 246)', marginRight: 8 }}>
                <img src={agentSvg} />
              </Avatar>
              <div>
                <div className="text-sm font-medium">Agent管理器</div>
              </div>
            </div>
          </Menu.Item>
        </Menu>
      </ConfigProvider>
    </div>
  );

  const pcLeftSider = (
      <>
        <Sider
            trigger={null}
            collapsible
            collapsed={collapsed}
            theme={'light'}
            width={270}
            style={{ backgroundColor: 'rgb(243, 244, 246)', border: '1px solid rgba(0, 0, 0, 0.08)' }}
        >
          <div className="flex flex-col h-full">
            {logoNode}
            {newConversitionNode}
            {historicalDialoguesNode}
            <Divider
                style={{
                  margin: '5px 12px',
                  minWidth: '0',
                  width: 'auto',
                  borderColor: 'rgba(0,0,0,.25)'
                }}
            />
            {agentList && agentListNode}
          </div>
        </Sider>
      </>
  )

  const mobileLeftSider = (
      <>
        <ConfigProvider
            theme={{
              components: {
                Drawer: {
                  paddingLG: 0
                }
              }
            }}
        >
          <Drawer
              placement={"left"}
              closable={false}
              open={drawerOpen}
              onClose={() => setDrawerOpen(false)}
              width={270}
              style={{ backgroundColor: 'rgb(243, 244, 246)', border: '1px solid rgba(0, 0, 0, 0.08)' }}
          >
            <div className="flex flex-col h-full">
              {logoNode}
              {newConversitionNode}
              {historicalDialoguesNode}
              <Divider
                  style={{
                    margin: '5px 12px',
                    minWidth: '0',
                    width: 'auto',
                    borderColor: 'rgba(0,0,0,.25)'
                  }}
              />
              {agentList && agentListNode}
            </div>
          </Drawer>
        </ConfigProvider>
      </>
  )

  // ==================== Render =================
  return (
    <>
      <Layout style={{ height: '100vh', overflow: 'hidden' }}>
        {isMobile ? mobileLeftSider : pcLeftSider}
        <Layout className="bg-white" style={{
          width: '100%',
          height: '100%',
          paddingRight: contentShowState === 'supersonic' ? 0 : 20,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          <Header className="bg-white p-0 flex justify-between items-center w-full" style={{
            paddingRight: contentShowState === 'supersonic' ? 20 : 0,
        }}>
            <div className="flex items-center">
              <Button
                type="text"
                variant="text"
                shape="circle"
                icon={isMobile ? <MenuUnfoldOutlined /> : collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={() => {
                  if (isMobile) setDrawerOpen(true)
                  else setCollapsed(!collapsed);
                }}
                style={{
                  fontSize: '16px',
                  marginLeft: 10
                }}
              />
              {currentAgent?.isDefault && (
                  <Dropdown menu={{
                    items: llmItems,
                    selectable: true,
                    defaultSelectedKeys: [llmDefaultSelect],
                    onClick: handleLLMDropDownClick
                  }}
                            trigger={['click']}
                  >
                    <div
                        className="ml-10"
                    >
                      <Space>
                        <span className="text-base">{llmDefaultSelect}</span>
                        <DownOutlined/>
                      </Space>
                    </div>
                  </Dropdown>
              )}
            </div>
            <div className="h-9 cursor-pointer">
              {/* 用户区域 */}
              {!userItem && loginNode}
              {userItem && userNode}
            </div>
          </Header>
          <Content className="w-full" style={{ height: 'calc(100vh - 64px)', overflow: 'hidden', padding: contentShowState === 'supersonic' ? 0 : '8px' }}>
            <div className={contentShowState === 'supersonic' ? '' : styles.chat} style={{
              height: '100%',
              width: contentShowState === 'supersonic' ? '100%' : undefined,
              maxWidth: contentShowState === 'supersonic' ? 'none' : undefined,
              padding: contentShowState === 'supersonic' ? 0 : undefined
            }}>
              {(contentShowState === 'defaultChat' || contentShowState === 'agent') && (
                  <>
                    {/* 🌟 消息列表 */}
                    <Bubble.List
                        items={
                          items.length > 0 ? items : [{ content: placeholderNode, variant: 'borderless' }]
                        }
                        roles={roles}
                        className={styles.messages}
                    />
                    {/* 🌟 提示词 */}
                    {/*<Prompts items={senderPromptsItems} onItemClick={onPromptsItemClick} />*/}
                    {/* 🌟 输入框 */}
                    <Sender
                        value={content}
                        header={senderHeader}
                        onSubmit={onSubmit}
                        onCancel={onCancel}
                        onChange={setContent}
                        prefix={
                          <div style={{ display: 'flex' }}>
                            {currentAgentParam?.file_upload?.enabled && attachmentsNode}
                          </div>
                        }
                        loading={agent.isRequesting() || isWaitingForPluginResponse}
                        className={styles.sender}
                        disabled={!currentAgent}
                        classNames={{
                          input: "sender-input"
                        }}
                    />
                  </>
              )}

              {contentShowState === 'supersonic' && (
                <SuperSonicChatWrapper
                  agent={currentAgent}
                />
              )}

              {contentShowState === 'agentManager' && (
                  <div className="w-full h-full flex flex-col">
                    {/* 标题加按钮，按钮有权限控制 */}
                    <div className="w-full h-[100px] flex flex-row items-center justify-between mb-4">
                      <div className="text-3xl">Agent 管理器</div>
                      <div>
                        <ConfigProvider
                            theme={{
                              components: {
                                Button: {
                                  defaultColor: 'rgba(0, 0, 0, 0.85)',
                                  defaultHoverBg: 'rgba(0, 0, 0, 0.1)',
                                  defaultHoverColor: 'rgba(0, 0, 0, 0.85)',
                                  defaultHoverBorderColor: 'rgb(217, 217, 217)',
                                }
                              }
                            }}
                        >
                          <Button
                              icon={<PlusOutlined />}
                              onClick={handleCreateAgentBtnClick}
                              disabled={!isCanCreateAgent}
                          >
                            <span>创建 Agent</span>
                          </Button>
                        </ConfigProvider>
                      </div>
                    </div>
                    {/* Agent列表，自动换行向下滚动 */}
                    {/*<div className="w-full flex-1 flex flex-wrap justify-start overflow-y-auto">*/}
                    <Flex wrap gap="middle" style={{
                      width: '100%',
                      flex: 1,
                      overflowY: 'auto',
                      scrollbarColor: '#888 transparent',
                      scrollbarWidth: 'thin'
                    }}>
                      {agentList.map(item => (
                          <div
                              onClick={() => handleAgentManagerAgentClick(item)}
                              className=" w-[200px] h-[175px] bg-gray-100 rounded-2xl p-4 flex flex-col cursor-pointer"
                          >
                            <div className="mb-4 flex justify-between">
                              <div>
                                <Avatar
                                  size="small"
                                  style={{
                                    backgroundColor: item.iconColor,
                                    color: item.fontColor,
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                  }}
                                  src={item.icon && (item.icon.startsWith('http://') || item.icon.startsWith('https://'))
                                    ? item.icon
                                    : undefined}
                                >
                                  {item.icon && !(item.icon.startsWith('http://') || item.icon.startsWith('https://')) ? (
                                    <span style={{ fontSize: '14px' }}>{item.icon}</span>
                                  ) : (
                                    !item.icon && (item.name ? item.name[0] : '?')
                                  )}
                                </Avatar>
                              </div>
                              {isCanCreateAgent && (
                                  <div onClick={(event) => {
                                    event.stopPropagation();
                                  }}>
                                    <Dropdown menu={{
                                      items: agentCardMoreItems,
                                      onClick: (info) => handleAgentCardMoreDropDownClick(info, item)
                                    }}
                                              trigger={['click']}
                                    >
                                      <MoreOutlined />
                                    </Dropdown>
                                  </div>
                              )}
                              {!isCanCreateAgent && (
                                  <div onClick={(event) => {
                                    event.stopPropagation();
                                  }}>
                                    <Popover
                                        content="添加至列表"
                                    >
                                      <Button
                                          type={'text'}
                                          size={'small'}
                                          disabled={item.userAgentId != null}
                                          onClick={() => handleAddToList(item)}>
                                        <PlusOutlined />
                                      </Button>
                                    </Popover>
                                  </div>
                              )}
                            </div>
                            <div className="truncate overflow-hidden mb-1 text-sm">{item.name}</div>
                            <div className="overflow-y-hidden text-xs h-[66px]" style={{
                              color: 'rgb(175, 177, 196)',
                            }}>{item.description}</div>
                          </div>
                      ))}
                    </Flex>

                    {/*</div>*/}
                  </div>
              )}
            </div>
          </Content>
        </Layout>
      </Layout>
      <Spin spinning={isShowSpin} tip={'正在处理...'} fullscreen />
      <Modal
        title={renameModalType === 'rename' ? '重命名此对话' : '固定此对话'}
        open={isShowRenameModal}
        onOk={handleConfirmRename}
        onCancel={handleCancelRename}
        okText="保存"
        cancelText="取消"
      >
        <Form
            form={chatNameForm}
            size={'large'}
        >
          <Form.Item
            name="name"
          >
            <Input placeholder="请输入对话名称" />
          </Form.Item>
        </Form>
      </Modal>
      <AgentFormModal
        isOpen={isOpenAgentFormModal}
        onClose={() => setIsOpenAgentFormModal(false)}
        agentFormData={agentFormData}
        onSubmit={() => {
          setAgentFormData(null);
          setIsOpenAgentFormModal(false);
          fetchAgentList();
        }}
        isCanCreateAgent={isCanCreateAgent}
      />
    </>
  );
};



export default Independent;

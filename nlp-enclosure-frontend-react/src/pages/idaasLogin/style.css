.idaas-center {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;
    color: #1a1a1a;
}

.idaas-shadow {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.idaas-heigth {
    height: 100vh;
}

.idaas-container {
    overflow: hidden;
}

.idaas-main-container {
    height: 90%;
    width: 96%;
}

.idaas-title {
    font-size: 1.5rem;
    line-height: 2rem;
    position: absolute;
    float: left;
    margin: 20px;
    display: flex;
}

.iconfont {
    color: #fff;
    font-size: 30px;
}

.sys-container {
    padding: 2rem;
    width: 368px;
    height: 189px;
}

.sys-button {
    --height: 30px;
    cursor: pointer;
    display: flex;
    width: 300px;
    height: var(--height);
    line-height: var(--height);
    text-align: center;
    border-radius: 4px;
    margin: 50px auto;
    justify-content: center;
    align-items: center;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.title {}

import { Image, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { getUserInfo } from '../../services/user.ts'; // 引入样式文件
import { setAuthToken } from '../../utils/request.ts';
import './style.css';
import {useLocation} from "react-router-dom";

const baseUrl = import.meta.env.VITE_APP_API;

const IdaasLogin = () => {
  const location = useLocation();
  const uid = new URLSearchParams(location.search).get('uid') || '';
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (uid) {
      setLoading(true);
      // 获取URL中的token参数
      const token = new URLSearchParams(location.search).get('token');
      if (token) {
        // 如果有token，存储到localStorage
        setAuthToken(token);
      }
      // 这里可以发起请求，但为了示例，我们只设置loading状态
      getUserInfo().then((res) => {
        if (res) {
          let pre = '';
          if (baseUrl.includes('localhost')) {
            pre = '/#/';
          } else {
            pre = '/index.html#/';
          }
          pre += "home"

          window.location.href = pre;
        }
      });
    }
    else {
      window.location.href = '/enclosure/idaas/login/dingtalk';
    }
  }, [uid]);

  const handleClick = () => {
    // http.get('./api/idaas/login/test')
    window.location.href = '/enclosure/idaas/login/dingtalk';
  };

  return (
    <div className="idaas-container idaas-center idaas-heigth">
      <div className="idaas-main-container idaas-shadow">
        <div className="idaas-title">
          <Image src="/favicon.ico" width={32} height={32} style={{ marginRight: 10 }} />
          <span style={{ marginLeft: 10 }}>广州烟草AI门户</span>
        </div>
        <div className="idaas-center idaas-heigth">
          <div className="idaas-shadow">
            <div onClick={handleClick} className="sys-container">
              <h2 className="title">嗨，近来可好</h2>
              <p className="title">欢迎来到 广州烟草AI门户，登录以继续</p>
              <span className="sys-button">
                <Image src="/fsyc.svg" width={20} height={20} style={{ marginRight: 5 }} />
                使用内部账号登录
              </span>
            </div>
          </div>
        </div>
      </div>
      {loading && (
        <Spin
          tip="登录中,请稍后"
          style={{ position: 'fixed', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}
        />
      )}
    </div>
  );
};

export default IdaasLogin;

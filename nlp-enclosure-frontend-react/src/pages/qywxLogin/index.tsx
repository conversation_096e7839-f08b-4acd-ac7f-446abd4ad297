import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { getUserInfo } from '../../services/user.ts';
import { setAuthToken } from '../../utils/request.ts';
import { replaceAiPortalDomain } from '../../services/configService.ts';
import styles from './style.module.css';
import fsycIcoSvg from './img/fsyc-ico.svg';


const baseUrl = import.meta.env.VITE_APP_API;

const QywxLogin: React.FC = () => {
  const location = useLocation();
  const uid = new URLSearchParams(location.search).get('uid') || '';
  const [loadingText, setLoadingText] = useState('正在登录');
  const [dots, setDots] = useState('.');

  // 添加动态的加载点
  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => prev.length >= 3 ? '.' : prev + '.');
    }, 500);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    console.log('uid', uid);
    if (uid) {
      // 获取URL中的token参数
      const token = new URLSearchParams(location.search).get('token');
      if (token) {
        // 如果有token，存储到localStorage
        setAuthToken(token);
      }
      // 这里可以发起请求，但为了示例，我们只设置loading状态
      setLoadingText('正在获取用户信息');
      getUserInfo().then((res) => {
        if (res) {
            setLoadingText('登录成功，正在跳转');
            setTimeout(() => {
              window.location.href = replaceAiPortalDomain('/qy_wechat_h5/ai-portal/index.html#/home');
            }, 800); // 添加短暂延迟以显示成功消息
        }
      });
    } else {
      setLoadingText('未检测到用户信息，正在跳转');
      setTimeout(() => {
        window.location.href = replaceAiPortalDomain('/qy_wechat_h5/ai-portal/enclosure/qywx/login');
      }, 1000);
    }
  }, [uid]);

  return (
    <div className={styles.loginContainer}>
      <div className={styles.wave}></div>
      <div className={styles.loadingContainer}>
        {/* 公司或应用logo */}
        <img src={fsycIcoSvg} className={styles.logo} />
        <div className={styles.spinner}></div>
        <h1 className={styles.loadingText}>{loadingText}{dots}</h1>
        <p className={styles.subtitle}>广州烟草AI门户</p>
      </div>
    </div>
  );
};

export default QywxLogin;

/* Login page animations and styles */

/* Main container animation */
.loginContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  animation: fadeIn 0.8s ease-out forwards;
  position: relative;
  overflow: hidden;
}

/* Loading text container */
.loadingContainer {
  text-align: center;
  animation: slideUp 0.6s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
  transform: translateY(20px);
  background-color: rgba(255, 255, 255, 0.8);
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  max-width: 90%;
  width: 320px;
}

/* Logo styling */
.logo {
  width: 100%;
  height: 80px;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

/* Loading text styling */
.loadingText {
  font-size: 1.5rem;
  font-weight: 500;
  color: #333;
  margin: 0;
}

/* Subtitle styling */
.subtitle {
  font-size: 1rem;
  color: #666;
  margin-top: 8px;
}

/* Loading spinner */
.spinner {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #1890ff;
  animation: spin 1s ease-in-out infinite;
  margin: 20px 0;
}

/* Wave animation at the bottom */
.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%231890ff' fill-opacity='0.2' d='M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: 1440px 100px;
  background-repeat: repeat-x;
  animation: wave 15s linear infinite;
}

/* Keyframes for animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes wave {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 1440px 0;
  }
}

/* Floating particles for background effect */
.particle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  pointer-events: none;
}

.particle:nth-child(1) {
  width: 10px;
  height: 10px;
  top: 20%;
  left: 20%;
  animation: floatParticle 15s infinite linear;
}

.particle:nth-child(2) {
  width: 15px;
  height: 15px;
  top: 40%;
  left: 80%;
  animation: floatParticle 20s infinite linear;
}

.particle:nth-child(3) {
  width: 8px;
  height: 8px;
  top: 70%;
  left: 10%;
  animation: floatParticle 18s infinite linear;
}

.particle:nth-child(4) {
  width: 12px;
  height: 12px;
  top: 30%;
  left: 60%;
  animation: floatParticle 22s infinite linear;
}

@keyframes floatParticle {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(50px, 50px) rotate(90deg);
  }
  50% {
    transform: translate(0, 100px) rotate(180deg);
  }
  75% {
    transform: translate(-50px, 50px) rotate(270deg);
  }
  100% {
    transform: translate(0, 0) rotate(360deg);
  }
}

/* Media query for smaller screens */
@media (max-width: 480px) {
  .loadingText {
    font-size: 1.2rem;
  }

  .subtitle {
    font-size: 0.9rem;
  }

  .spinner {
    width: 40px;
    height: 40px;
  }

  .logo {
    width: 60px;
    height: 60px;
  }

  .wave {
    height: 80px;
    background-size: 1440px 80px;
  }
}

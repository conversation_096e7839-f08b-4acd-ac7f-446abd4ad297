import { QueryClient, QueryClientProvider } from 'react-query';
import { Outlet } from 'react-router-dom';
import './App.css';

// 创建一个 client
const queryClient = new QueryClient();

function App() {

  return (
    <>
      <QueryClientProvider client={queryClient}>
        <div className="h-full w-full">
          <Outlet />
        </div>
      </QueryClientProvider>
    </>
  );
}

export default App;

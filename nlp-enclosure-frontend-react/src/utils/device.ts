
export function useIsMobile() {
    let isMobile = false;

    const userAgent = navigator.userAgent.toLowerCase();
    const isUserAgentMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|mobile/.test(userAgent);
    const mediaQuery = window.matchMedia('(max-width: 768px)').matches;

    isMobile = (isUserAgentMobile || mediaQuery);

    // 监听屏幕变化
    // const handleResize = () => {
    //     setIsMobile(isUserAgentMobile || window.matchMedia('(max-width: 768px)').matches);
    // };
    // window.addEventListener('resize', handleResize);
    //
    // return () => window.removeEventListener('resize', handleResize);

    return isMobile;
}

export function isWeChatWorkBrowser() {
    const userAgent = navigator.userAgent.toLowerCase();
    console.log('userAgent', userAgent)
    return userAgent.includes('wxwork');
}

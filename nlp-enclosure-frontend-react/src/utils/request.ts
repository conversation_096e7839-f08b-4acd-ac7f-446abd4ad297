import axios from 'axios';
import {clearUserInfoFormLocalStorage} from "../services/user.ts";
import {isWeChatWorkBrowser, useIsMobile} from "./device.ts";
import {getStoredSecondLevelDomainNames, replaceAiPortalDomain} from "../services/configService";

const baseUrl = import.meta.env.VITE_APP_API;

// 存储认证令牌
export const setAuthToken = (token: string) => {
  localStorage.setItem('authToken', token);
};

// 获取认证令牌
export const getAuthToken = (): string | null => {
  return localStorage.getItem('authToken');
};

// 清除认证令牌
export const clearAuthToken = () => {
  localStorage.removeItem('authToken');
};

const http = axios.create({
  baseURL: baseUrl,
  timeout: 30000,
  withCredentials: true
  //   headers: { "X-Custom-Header": "foobar" },
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 在发送请求之前对请求配置进行处理
    // 可以添加请求头、验证等操作

    // 获取认证令牌并添加到请求头
    const token = getAuthToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    // 当请求路径中包含 api2 时，将请求地址改为 localhost:9080
    // if (config.url && config.url.includes('/api2/')) {
    //   // 修改请求的 baseURL
    //   config.baseURL = 'http://localhost:9080';
    //   console.log('Redirecting to localhost:9080:', config.url);
    // }

    return config;
  },
  (error) => {
    // 请求错误处理
    console.error('Request interceptor error:', error);

    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    // 对响应数据进行处理
    // 可以进行数据转换、错误处理等操作
    return response.data;
  },
  (error) => {
    // 响应错误处理
    console.error('Response interceptor error:', error);

    // 处理401错误，如果是401就跳转到登录页
    if (error.response && error.response.status === 401) {
      clearUserInfoFormLocalStorage();
      clearAuthToken(); // 清除无效的令牌

      // 本地开发环境直接跳转到登录页
      if (baseUrl.includes('localhost') || baseUrl.includes('192.168.0')) {
        // 跳转到登录页
        window.location.href = '/#/userLogin';
      } else {
        if (useIsMobile() || isWeChatWorkBrowser()) {
          // 使用配置的二级域名替换 ai-portal
          const redirectUrl = replaceAiPortalDomain('https://www.gz-tobacco.net:9999/qy_wechat_h5/ai-portal/index.html#/qywxLogin');
          console.log('Redirecting to:', redirectUrl);
          window.location.href = redirectUrl;
        } else {
          window.location.href = '/index.html#/idaasLogin';
        }
      }
    }

    return Promise.reject(error);
  }
);

export default http;

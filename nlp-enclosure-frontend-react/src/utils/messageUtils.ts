/**
 * 消息处理工具函数
 */

/**
 * 过滤掉消息中的<think>标签内容
 * @param message 原始消息
 * @returns 过滤后的消息
 */
export const filterThinkTags = (message: string): string => {
  if (!message) return '';

  // 使用正则表达式移除<think>...</think>标签及其内容
  return message.replace(/<think>[\s\S]*?<\/think>/g, '');
};

/**
 * 过滤掉消息中的<details>标签内容
 * @param message 原始消息
 * @returns 过滤后的消息
 */
export const filterDetailTags = (message: string): string => {
  if (!message) return '';

  // 使用正则表达式移除<details>...</details>标签及其内容
  return message.replace(/<details[^>]*>[\s\S]*?<\/details>/g, '');
};

/**
 * 从HTML字符串中提取纯文本
 * @param html HTML字符串
 * @returns 提取的纯文本
 */
export const extractTextFromHtml = (html: string): string => {
  if (!html) return '';

  // 创建临时DOM元素
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // 获取纯文本内容
  return tempDiv.textContent || tempDiv.innerText || '';
};

/**
 * 处理AI回复消息，移除<think>标签、<details>标签并提取纯文本
 * @param message AI回复的消息
 * @returns 处理后的纯文本消息
 */
export const processAIMessage = (message: string): string => {
  if (!message) return '';

  // 先过滤<think>标签
  const filteredThinkMessage = filterThinkTags(message);

  // 再过滤<details>标签
  const filteredDetailMessage = filterDetailTags(filteredThinkMessage);

  // 最后提取纯文本
  return extractTextFromHtml(filteredDetailMessage);
};

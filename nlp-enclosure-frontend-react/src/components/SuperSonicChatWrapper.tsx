import React, { useEffect, useState } from 'react';
import { Chat } from 'llm-supersonic-chat';
import { Spin, message } from 'antd';
import { AgentDict } from '../types/agentDict';
import { loginSuperSonic, SUPERSONIC_TOKEN_KEY } from '../services/supersonic-auth';
import { getUserInfoFromLocalStorage } from '../services/user';

interface SuperSonicChatWrapperProps {
  agent: AgentDict | null;
}

/**
 * SuperSonic Chat 包装组件
 * 负责在渲染 Chat 组件前调用登录接口获取 token
 */
const SuperSonicChatWrapper: React.FC<SuperSonicChatWrapperProps> = ({ agent }) => {
  const [loading, setLoading] = useState(true);
  const [token, setToken] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 直接调用登录接口获取 token
    const loginSuperSonicChat = async () => {
      setLoading(true);
      setError(null);

      try {
        // 清除现有 token，确保每次都重新登录
        localStorage.removeItem(SUPERSONIC_TOKEN_KEY);

        // 尝试使用当前系统用户信息登录
        const userInfo = getUserInfoFromLocalStorage();

        // 优先使用 agent 中的凭据，其次使用系统用户信息，最后使用默认凭据
        // 统一使用 workNo 作为用户名和密码
        const username = agent?.username || (userInfo ? userInfo.workNo : 'admin');
        const password = agent?.password || (userInfo ? userInfo.workNo : '123456');

        console.log(`Logging in to SuperSonic with username: ${username}`);

        const loginResult = await loginSuperSonic({
          username,
          password
        });

        if (loginResult.code === 200 && loginResult.data) {
          setToken(loginResult.data);
          console.log('SuperSonic login successful');
        } else {
          const errorMsg = loginResult.msg || '登录失败，请检查用户名和密码';
          setError('登录失败：' + errorMsg);
          message.error('SuperSonic 登录失败：' + errorMsg);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '未知错误';
        setError('SuperSonic 登录出错：' + errorMessage);
        message.error('SuperSonic 登录出错：' + errorMessage);
        console.error('Error logging in to SuperSonic:', err);
      } finally {
        setLoading(false);
      }
    };

    loginSuperSonicChat();
  }, [agent]);

  if (loading) {
    return (
      <div style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <Spin size="large" />
        <div>正在初始化 SuperSonic...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <div>{error}</div>
        <button
          onClick={() => window.location.reload()}
          style={{
            padding: '8px 16px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          重试
        </button>
      </div>
    );
  }

  return (
    <Chat
      token={token || ''}
      agentIds={agent?.superSonicAgentId ? [parseInt(agent.superSonicAgentId)] : undefined}
      initialAgentId={agent?.superSonicAgentId ? parseInt(agent.superSonicAgentId) : undefined}
      // 可以根据需要添加其他配置项
    />
  );
};

export default SuperSonicChatWrapper;

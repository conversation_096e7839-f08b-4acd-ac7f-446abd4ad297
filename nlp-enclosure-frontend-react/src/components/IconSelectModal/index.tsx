import React, { useState } from 'react';
import { Modal, Button, Space, Tabs, Upload, message } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
import { uploadToMinio } from '../../services/minioApi';

// 注意：这里我们不再直接导入 emoji-mart
// 而是在组件中动态加载

interface IconSelectModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (icon: string) => void;
  currentIcon?: string;
}

/**
 * 图标选择弹窗组件
 *
 * @param visible 是否显示弹窗
 * @param onClose 关闭弹窗的回调函数
 * @param onSelect 选择图标的回调函数
 * @param currentIcon 当前选中的图标
 */
const IconSelectModal: React.FC<IconSelectModalProps> = ({
  visible,
  onClose,
  onSelect,
  currentIcon
}) => {
  // 表情符号列表
  const emojiIcons = [
    '😀', '😁', '😂', '🤣', '😃', '😄', '😅', '😆', '😉', '😊',
    '😋', '😎', '😍', '😘', '🥰', '😗', '😙', '😚', '🙂', '🤗',
    '🤩', '🤔', '🤨', '😐', '😑', '😶', '🙄', '😏', '😣', '😥',
    '🤖', '🧠', '💡', '🔍', '📚', '🌐', '💬', '🧩', '🎯', '🚀',
    '📊', '📈', '📝', '📌', '🔧', '⚙️', '🔔', '🔑', '🔒', '📱',
    '💻', '🖥️', '📡', '🛠️', '🔋', '⚡', '🔍', '🔎', '📁', '📂'
  ];

  // 上传文件列表状态
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  // 处理表情符号选择
  const handleEmojiSelect = (icon: string) => {
    onSelect(icon);
    onClose();
  };

  // 处理图片上传前的检查
  const beforeUpload = (file: RcFile) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return Upload.LIST_IGNORE;
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片必须小于2MB!');
      return Upload.LIST_IGNORE;
    }

    return true;
  };

  // 处理图片上传
  const handleUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;

    try {
      // 将文件转换为 File 对象
      const fileObj = file as File;

      // 调用 Minio 上传服务
      const result = await uploadToMinio(fileObj);

      if (result && result.url) {
        // 上传成功，返回图片 URL
        onSuccess && onSuccess(result);
        onSelect(result.url);
        onClose();
        message.success('图片上传成功');
      } else {
        onError && onError(new Error('上传失败'));
        message.error('图片上传失败: ' + (result?.message || '未知错误'));
      }
    } catch (error) {
      console.error('上传出错:', error);
      onError && onError(new Error('上传出错'));
      message.error('图片上传出错: ' + (error instanceof Error ? error.message : '未知错误'));
    }
  };

  return (
    <Modal
      title="选择图标"
      open={visible}
      onCancel={onClose}
      width={650}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>
      ]}
    >
      <Tabs
        defaultActiveKey="emoji"
        items={[
          {
            key: 'emoji',
            label: '表情符号',
            children: (
              <div style={{ display: 'flex', justifyContent: 'center', padding: '10px 0' }}>
                <div style={{ textAlign: 'center' }}>
                  <Space wrap size={[16, 16]} style={{ justifyContent: 'center' }}>
                    {emojiIcons.map((icon, index) => (
                      <Button
                        key={index}
                        type={currentIcon === icon ? 'primary' : 'default'}
                        style={{
                          width: '50px',
                          height: '50px',
                          fontSize: '24px',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          borderRadius: '8px'
                        }}
                        onClick={() => handleEmojiSelect(icon)}
                      >
                        {icon}
                      </Button>
                    ))}
                  </Space>
                </div>
              </div>
            ),
          },
          {
            key: 'upload',
            label: '图片上传',
            children: (
              <div style={{ padding: '20px 0' }}>
                <Upload.Dragger
                  name="file"
                  fileList={fileList}
                  beforeUpload={beforeUpload}
                  customRequest={handleUpload}
                  onChange={({ fileList }) => setFileList(fileList)}
                  maxCount={1}
                  showUploadList={{ showPreviewIcon: true, showRemoveIcon: true }}
                >
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                  <p className="ant-upload-hint">
                    支持单个图片上传，图片大小不超过2MB
                  </p>
                </Upload.Dragger>
              </div>
            ),
          },
        ]}
      />
    </Modal>
  );
};

export default IconSelectModal;

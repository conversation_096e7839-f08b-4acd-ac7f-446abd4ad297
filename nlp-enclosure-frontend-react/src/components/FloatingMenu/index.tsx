import React, { useState } from 'react';

// 导入拆分后的组件
import FloatingButtons from './FloatingButtons';
import SuggestionForm from './SuggestionForm';
import UpdateLog from './UpdateLog';

/**
 * 悬浮菜单组件
 * 包含优化建议箱和更新日志两个功能按钮
 * 组合了三个子组件：FloatingButtons、SuggestionForm、UpdateLog
 */
const FloatingMenu: React.FC = () => {
  // 控制优化建议弹窗的显示
  const [suggestionModalVisible, setSuggestionModalVisible] = useState(false);
  // 控制更新日志弹窗的显示
  const [updateLogModalVisible, setUpdateLogModalVisible] = useState(false);

  // 打开优化建议弹窗
  const showSuggestionModal = () => {
    setSuggestionModalVisible(true);
  };

  // 关闭优化建议弹窗
  const closeSuggestionModal = () => {
    setSuggestionModalVisible(false);
  };

  // 打开更新日志弹窗
  const showUpdateLogModal = () => {
    setUpdateLogModalVisible(true);
  };

  // 关闭更新日志弹窗
  const closeUpdateLogModal = () => {
    setUpdateLogModalVisible(false);
  };

  return (
    <>
      {/* 悬浮按钮组 */}
      <FloatingButtons
        onSuggestionClick={showSuggestionModal}
        onUpdateLogClick={showUpdateLogModal}
      />

      {/* 优化建议弹窗 */}
      <SuggestionForm
        visible={suggestionModalVisible}
        onClose={closeSuggestionModal}
      />

      {/* 更新日志弹窗 */}
      <UpdateLog
        visible={updateLogModalVisible}
        onClose={closeUpdateLogModal}
      />
    </>
  );
};

export default FloatingMenu;

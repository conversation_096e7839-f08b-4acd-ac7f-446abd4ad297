import { CSSProperties, useState } from 'react';
import { Button } from 'antd';
import { BulbOutlined, HistoryOutlined } from '@ant-design/icons';

interface FloatingButtonsProps {
  onSuggestionClick: () => void;
  onUpdateLogClick: () => void;
}

/**
 * 悬浮按钮组件
 * 显示在页面右侧中间的显眼按钮
 * 内容垂直排列，使按钮更加紧凑
 */
const FloatingButtons: React.FC<FloatingButtonsProps> = ({
  onSuggestionClick,
  onUpdateLogClick
}) => {
  // 按钮悬停状态
  const [suggestionHovered, setSuggestionHovered] = useState(false);
  const [updateLogHovered, setUpdateLogHovered] = useState(false);

  // 按钮容器的基本样式
  const containerStyle: CSSProperties = {
    position: 'fixed',
    right: 0,
    top: '50%',
    transform: 'translateY(-50%)',
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
    zIndex: 1000,
    padding: '0 0',
  };

  // 按钮的基本样式
  const buttonStyle: CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '10px 5px',
    borderRadius: '8px 0 0 8px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    width: '80px',
    height: '80px',
    border: 'none',
    fontSize: '12px',
    fontWeight: 'bold',
    transition: 'all 0.3s ease',
  };

  // 图标样式
  const iconStyle: CSSProperties = {
    fontSize: '24px',
    marginBottom: '4px',
  };

  return (
    <div style={containerStyle}>
      {/* 优化建议箱按钮 */}
      <Button
        type="primary"
        onClick={onSuggestionClick}
        onMouseEnter={() => setSuggestionHovered(true)}
        onMouseLeave={() => setSuggestionHovered(false)}
        style={{
          ...buttonStyle,
          backgroundColor: '#1890ff',
          transform: suggestionHovered ? 'translateX(-10px)' : 'translateX(0)',
          boxShadow: suggestionHovered
            ? '0 6px 16px rgba(24, 144, 255, 0.4)'
            : '0 4px 12px rgba(0, 0, 0, 0.15)',
        }}
      >
        <BulbOutlined style={iconStyle} />
        优化建议箱
      </Button>

      {/* 更新日志按钮 */}
      <Button
        type="primary"
        onClick={onUpdateLogClick}
        onMouseEnter={() => setUpdateLogHovered(true)}
        onMouseLeave={() => setUpdateLogHovered(false)}
        style={{
          ...buttonStyle,
          backgroundColor: '#52c41a',
          transform: updateLogHovered ? 'translateX(-10px)' : 'translateX(0)',
          boxShadow: updateLogHovered
            ? '0 6px 16px rgba(82, 196, 26, 0.4)'
            : '0 4px 12px rgba(0, 0, 0, 0.15)',
        }}
      >
        <HistoryOutlined style={iconStyle} />
        更新日志
      </Button>
    </div>
  );
};

export default FloatingButtons;

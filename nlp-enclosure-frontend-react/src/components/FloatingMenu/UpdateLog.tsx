import React from 'react';
import { Modal, Button, Typography } from 'antd';

const { Title, Paragraph, Text } = Typography;

interface UpdateLogProps {
  visible: boolean;
  onClose: () => void;
}

/**
 * 更新日志组件
 * 显示应用的版本更新历史
 */
const UpdateLog: React.FC<UpdateLogProps> = ({ visible, onClose }) => {
  // 更新日志内容
  const updateLogContent = (
    <Typography>
      <Title level={2}>更新日志</Title>

      <Paragraph>
        <Text>2025-4-29</Text>
        <Title level={5}>功能更新：</Title>
        <ul>
          <li>新增“优化建议箱”</li>
          <li>新增“更新日志”查看页</li>
        </ul>
        <Text>2025-4-25</Text>
        <Title level={5}>功能更新：</Title>
        <ul>
          <li>实现对话内容流式输出（打字机）</li>
        </ul>
        <Text>2025-4-18</Text>
        <Title level={5}>功能更新：</Title>
        <ul>
          <li>接入DeepSeek-R1:671B（“满血版”）大模型</li>
        </ul>
        <Text>2025-4-07</Text>
        <Title level={5}>功能更新：</Title>
        <ul>
          <li>新增对话历史管理</li>
          <li>支持文件上传</li>
        </ul>
        <Text>2025-3-31</Text>
        <Title level={5}>初始版本发布:</Title>
        <ul>
          <li>实现了基本的对话功能</li>
          <li>支持智能体发布（Agent 管理器）</li>
          <li>接入综管平台单点登录</li>
        </ul>
      </Paragraph>
    </Typography>
  );

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" type="primary" onClick={onClose}>
          关闭
        </Button>
      ]}
      width={700}
    >
      {updateLogContent}
    </Modal>
  );
};

export default UpdateLog;

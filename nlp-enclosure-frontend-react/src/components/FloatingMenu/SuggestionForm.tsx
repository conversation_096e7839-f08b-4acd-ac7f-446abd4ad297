import React, { useState } from 'react';
import { Modal, Form, Input, Button, message } from 'antd';
import { submitSuggestion } from '../../services/suggestionService';
import { SuggestionSubmitRequest } from '../../types/suggestion';

const { TextArea } = Input;

interface SuggestionFormProps {
  visible: boolean;
  onClose: () => void;
}

/**
 * 优化建议表单组件
 * 允许用户提交优化建议和详细描述
 */
const SuggestionForm: React.FC<SuggestionFormProps> = ({ visible, onClose }) => {
  // 表单实例
  const [form] = Form.useForm();
  // 提交按钮加载状态
  const [submitLoading, setSubmitLoading] = useState(false);

  // 关闭弹窗并重置表单
  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  // 提交优化建议
  const handleSubmit = async () => {
    try {
      // 表单验证
      const values = await form.validateFields();
      setSubmitLoading(true);

      // 准备请求数据
      const suggestionData: SuggestionSubmitRequest = {
        title: values.title,
        description: values.description
      };

      // 调用服务提交建议
      const response = await submitSuggestion(suggestionData);

      // 处理响应
      if (response && response.status && response.status.code === '0') {
        message.success('感谢您的建议，我们会认真考虑！');
        handleClose();
      } else {
        message.error(response.status.message || '提交失败，请稍后再试');
      }
    } catch (error) {
      console.error('提交建议时出错:', error);
      message.error('提交过程中发生错误');
    } finally {
      setSubmitLoading(false);
    }
  };

  return (
    <Modal
      title="优化建议箱"
      open={visible}
      onCancel={handleClose}
      footer={[
        <Button key="cancel" onClick={handleClose}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={submitLoading}
          onClick={handleSubmit}
        >
          提交
        </Button>
      ]}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        name="suggestion_form"
      >
        <Form.Item
          name="title"
          label="优化建议"
          rules={[
            { required: true, message: '请输入您的优化建议' },
            { max: 150, message: '不能超过150个字符' }
          ]}
        >
          <Input placeholder="请简要描述您的优化建议（最多150字）" />
        </Form.Item>
        <Form.Item
          name="description"
          label="详细描述"
          rules={[
            { required: true, message: '请输入详细描述' },
            { max: 800, message: '不能超过800个字符' }
          ]}
        >
          <TextArea
            placeholder="请详细描述您的优化建议（最多800字）"
            rows={6}
            showCount
            maxLength={800}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SuggestionForm;

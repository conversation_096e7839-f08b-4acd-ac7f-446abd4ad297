import React from 'react';
import FloatingMenu from './index';

/**
 * FloatingMenu 组件的示例页面
 * 展示如何使用拆分后的 FloatingMenu 组件
 */
const FloatingMenuDemo: React.FC = () => {
  return (
    <div style={{
      height: '100vh',
      position: 'relative',
      padding: '20px',
      maxWidth: '800px',
      margin: '0 auto'
    }}>
      <h1 style={{ fontSize: '28px', marginBottom: '20px' }}>FloatingMenu 组件示例</h1>

      <div style={{
        backgroundColor: '#f5f5f5',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h2 style={{ fontSize: '20px', marginBottom: '10px' }}>组件说明</h2>
        <p>右侧中间有一个悬浮菜单，包含优化建议箱和更新日志两个按钮。</p>
        <p>新设计使用了更加显眼的按钮，包含图标和文字，并添加了悬停效果。</p>
        <p>该组件已拆分为三个子组件：悬浮按钮、优化建议弹窗和更新日志内容。</p>
      </div>

      <div style={{
        backgroundColor: '#e6f7ff',
        padding: '20px',
        borderRadius: '8px',
        border: '1px solid #91d5ff'
      }}>
        <h2 style={{ fontSize: '20px', marginBottom: '10px' }}>使用方法</h2>
        <p>将鼠标悬停在右侧的按钮上，可以看到悬停效果。</p>
        <p>点击按钮可以打开相应的弹窗。</p>
      </div>

      {/* 引入 FloatingMenu 组件 */}
      <FloatingMenu />
    </div>
  );
};

export default FloatingMenuDemo;

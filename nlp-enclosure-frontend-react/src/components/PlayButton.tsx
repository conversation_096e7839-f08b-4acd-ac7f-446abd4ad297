import React from 'react';
import { Button, Tooltip } from 'antd';
import { SoundOutlined, LoadingOutlined, PauseCircleOutlined } from '@ant-design/icons';
// import audioPlayer from '../services/audioPlayerService';
import audioPlayer from '../services/howlerAudioPlayerService';

interface PlayButtonProps {
  message: string;
  disabled?: boolean;
}

const PlayButton: React.FC<PlayButtonProps> = ({ message: textToPlay, disabled = false }) => {
  // 处理按钮点击
  const handleClick = () => {
    if (disabled) return;

    audioPlayer.togglePlayback(textToPlay);
  };

  // 获取当前音频播放状态
  const state = audioPlayer.getState();
  const isPlaying = state.isPlaying && state.currentText === textToPlay;
  const isLoading = state.isLoading && state.currentText === textToPlay;
  const isBuffering = state.isBuffering && state.currentText === textToPlay;

  // 渲染按钮
  return (
    <Tooltip title={
      isLoading ? '正在加载...' :
      isBuffering ? '正在缓冲...' :
      isPlaying ? '停止播放' :
      '播放语音'
    }>
      <Button
        type="text"
        icon={
          isLoading ? <LoadingOutlined spin /> :
          isBuffering ? <LoadingOutlined spin /> :
          isPlaying ? <PauseCircleOutlined /> :
          <SoundOutlined />
        }
        onClick={handleClick}
        disabled={disabled}
        size="small"
        color="default"
        variant="text"
      />
    </Tooltip>
  );
};

export default PlayButton;

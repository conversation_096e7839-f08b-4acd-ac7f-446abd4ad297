import React, { useEffect, useState } from 'react';
import { Modal, Switch, Select, Button, message, Spin } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { Account } from '../../types/account';
import { getUserList, searchUsers } from '../../services/account';
import { getVisibility, updateVisibility } from '../../services/agentDict';
import debounce from 'lodash/debounce';

interface VisibilitySelectorProps {
  agentDictId: string;
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

/**
 * AgentDict可见性选择器组件
 * 包含全部可见开关和用户多选下拉框
 */
const VisibilitySelector: React.FC<VisibilitySelectorProps> = ({
  agentDictId,
  open,
  onClose,
  onSuccess
}) => {
  // 状态管理
  const [isPublic, setIsPublic] = useState<boolean>(true);
  const [visibleUserIds, setVisibleUserIds] = useState<string[]>([]);
  const [userOptions, setUserOptions] = useState<Account[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<Account[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [searchLoading, setSearchLoading] = useState<boolean>(false);

  // 获取当前AgentDict的可见性设置
  useEffect(() => {
    if (open && agentDictId) {
      fetchVisibilitySetting();
      fetchUserList();
    }
  }, [open, agentDictId]);

  // 获取可见性设置
  const fetchVisibilitySetting = async () => {
    try {
      setLoading(true);
      const { isPublic, visibleToUsers } = await getVisibility(agentDictId);
      setIsPublic(isPublic);

      // 解析可见用户ID列表
      if (visibleToUsers) {
        try {
          const userIds = JSON.parse(visibleToUsers);
          setVisibleUserIds(userIds);
        } catch (e) {
          console.error('解析可见用户ID列表失败:', e);
          setVisibleUserIds([]);
        }
      } else {
        setVisibleUserIds([]);
      }
    } catch (error) {
      console.error('获取可见性设置失败:', error);
      message.error('获取可见性设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户列表
  const fetchUserList = async () => {
    try {
      setLoading(true);
      const response = await getUserList();
      setUserOptions(response || []);

      // 根据visibleUserIds匹配用户
      if (visibleUserIds.length > 0 && response) {
        const selectedUsers = response.filter((user: Account) =>
          visibleUserIds.includes(user.id)
        );
        setSelectedUsers(selectedUsers);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 搜索用户
  const handleSearch = debounce(async (keyword: string) => {
    if (!keyword) {
      fetchUserList();
      return;
    }

    try {
      setSearchLoading(true);
      const response = await searchUsers(keyword);
      setUserOptions(response || []);
    } catch (error) {
      console.error('搜索用户失败:', error);
    } finally {
      setSearchLoading(false);
    }
  }, 500);

  // 保存可见性设置
  const handleSave = async () => {
    try {
      setSaving(true);
      // 如果是公开可见，则不需要传递用户ID列表
      // 如果不是公开可见，则传递用户ID列表
      const response = await updateVisibility(
        agentDictId,
        isPublic,
        isPublic ? [] : visibleUserIds
      );

      if (response && response.code === 200) {
        message.success('保存成功');
        onSuccess?.();
        onClose();
      } else {
        message.error(response?.message || '保存失败');
      }
    } catch (error) {
      console.error('保存可见性设置失败:', error);
      message.error('保存失败');
    } finally {
      setSaving(false);
    }
  };

  // 处理用户选择变化
  const handleUserSelectChange = (values: string[]) => {
    setVisibleUserIds(values);

    // 更新已选用户列表
    const selected = userOptions.filter(user => values.includes(user.id));
    setSelectedUsers(selected);
  };

  return (
    <Modal
      title="设置可见性"
      open={open}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="save"
          type="primary"
          loading={saving}
          onClick={handleSave}
        >
          保存
        </Button>
      ]}
      width={500}
    >
      <Spin spinning={loading}>
        <div style={{ marginBottom: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
            <span style={{ marginRight: 8 }}>全部可见：</span>
            <Switch
              checked={isPublic}
              onChange={setIsPublic}
            />
          </div>

          {!isPublic && (
            <div>
              <div style={{ marginBottom: 8 }}>选择可见用户：</div>
              <Select
                mode="multiple"
                style={{ width: '100%' }}
                placeholder="请选择可见用户"
                value={visibleUserIds}
                onChange={handleUserSelectChange}
                optionFilterProp="label"
                loading={searchLoading}
                onSearch={handleSearch}
                showSearch
                filterOption={false}
                options={userOptions.map(user => ({
                  label: `${user.name} (${user.workNo || user.email || ''})`,
                  value: user.id
                }))}
                suffixIcon={<UserOutlined />}
                notFoundContent={searchLoading ? <Spin size="small" /> : "无匹配用户"}
                maxTagCount={5}
              />
            </div>
          )}
        </div>
      </Spin>
    </Modal>
  );
};

export default VisibilitySelector;

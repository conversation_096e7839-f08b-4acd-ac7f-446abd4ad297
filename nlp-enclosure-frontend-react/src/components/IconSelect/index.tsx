import React, { useState } from 'react';
import { Button, Image } from 'antd';
import IconSelectModal from '../IconSelectModal';

interface IconSelectProps {
  value?: string;
  onChange?: (value: string) => void;
}

/**
 * 图标选择组件
 *
 * @param value 当前选中的图标
 * @param onChange 图标变更的回调函数
 */
const IconSelect: React.FC<IconSelectProps> = ({ value, onChange }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedIcon, setSelectedIcon] = useState(value || '🤖');

  // 判断是否为URL
  const isImageUrl = (str: string) => {
    return str.startsWith('http://') || str.startsWith('https://');
  };

  // 处理图标选择
  const handleIconSelect = (icon: string) => {
    console.log("handleIconSelect", icon)
    setSelectedIcon(icon);
    onChange?.(icon);
  };

  return (
    <>
      <Button
        onClick={() => setModalVisible(true)}
        style={{
          height: '40px',
          width: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 11px'
        }}
      >
        {isImageUrl(selectedIcon) ? (
          <Image
            src={selectedIcon}
            width={24}
            height={24}
            style={{ objectFit: 'cover', borderRadius: '4px' }}
            preview={false}
          />
        ) : (
          <span style={{ fontSize: '20px' }}>{selectedIcon}</span>
        )}
        <span>选择图标</span>
      </Button>

      <IconSelectModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onSelect={handleIconSelect}
        currentIcon={selectedIcon}
      />
    </>
  );
};

export default IconSelect;

import axios from 'axios';
import CryptoJS from 'crypto-js';
import { getUserInfoFromLocalStorage } from '../services/user';

// 定义加密密钥
export const encryptKey = CryptoJS.enc.Utf8.parse('supersonic@2024');

/**
 * 对密码进行加密
 * @param password 原始密码
 * @param key 加密密钥（可选）
 * @returns 加密后的密码
 */
export const encryptPassword = (password: string, key?: CryptoJS.lib.WordArray) => {
  if (!password) {
    return password;
  }
  const srcs = CryptoJS.enc.Utf8.parse(password);
  const encrypted = CryptoJS.AES.encrypt(srcs, key || encryptKey, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
};

// 定义常量
export const SUPERSONIC_TOKEN_KEY = 'SUPERSONIC_TOKEN';

// 定义登录参数接口
export interface SuperSonicLoginParams {
  username: string;
  password: string;
}

// 定义响应接口
export interface SuperSonicResponse<T> {
  code: number;
  data: T;
  msg: string;
  success?: boolean;
}

// 定义用户信息接口
export interface SuperSonicUserInfo {
  id: number;
  username: string;
  name: string;
  email?: string;
  superAdmin?: boolean;
  [key: string]: any;
}

/**
 * SuperSonic 登录方法
 * @param params 登录参数
 * @returns 登录结果
 */
export const loginSuperSonic = async (params: SuperSonicLoginParams): Promise<SuperSonicResponse<string>> => {
  try {
    // 创建一个新的 axios 实例，避免与应用其他部分的拦截器冲突
    const instance = axios.create({
      // 使用相对路径，让 Vite 代理处理跨域请求
      baseURL: '',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      // 对于 CORS 请求，不发送认证信息
      withCredentials: false,
    });

    // 打印请求 URL
    console.log('SuperSonic login URL:', '/api/auth/user/login');

    // 对密码进行加密
    const encryptedParams = {
      name: params.username,
      password: encryptPassword(params.password)
    };
    console.log('Encrypted password for SuperSonic login');

    // 发送登录请求
    // 使用 POST 方式时，直接将参数作为请求体发送
    // 注意：已经设置了 baseURL，所以这里只需要提供相对路径
    // 不要在 URL 中包含完整的域名，因为已经在 axios.create 中设置了 baseURL
    const response = await instance.post( '/api/auth/user/login', encryptedParams);

    // 打印响应信息，便于调试
    console.log('SuperSonic login response:', response.data);

    // 如果请求成功，保存 token 到本地存储
    if (response.data.code === 200 && response.data.data) {
      // 保存 token 到本地存储
      localStorage.setItem(SUPERSONIC_TOKEN_KEY, response.data.data);
      console.log('SuperSonic login successful, token saved to localStorage');
      // 返回成功响应
      return {
        code: 200,
        data: response.data.data,
        msg: 'Login successful',
        success: true
      };
    } else {
      console.error('SuperSonic login failed:', response.data.msg);

      // 如果错误信息为 "password encrypt error, please try again"，尝试注册
      if (response.data.msg === 'password encrypt error, please try again') {
        console.log('Trying to register SuperSonic account');
        return await registerSuperSonic(params);
      }

      // 返回失败响应
      return {
        code: 500,
        data: '',
        msg: response.data.msg || 'Login failed',
        success: false
      };
    }
  } catch (error) {
    console.error('SuperSonic login error:', error);

    // 打印更详细的错误信息
    if (axios.isAxiosError(error)) {
      console.error('SuperSonic login axios error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          baseURL: error.config?.baseURL,
          headers: error.config?.headers,
        }
      });
    }

    // 返回一个错误响应
    return {
      code: 500,
      data: '',
      msg: error instanceof Error ? error.message : 'Unknown error',
      success: false,
    };
  }
};

/**
 * 设置 cookie
 * @param name cookie 名称
 * @param value cookie 值
 * @param days 过期天数
 * @param domain 域名（可选）
 */
export const setCookie = (name: string, value: string, days: number = 7, domain?: string): void => {
  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
  let cookieString = `${name}=${encodeURIComponent(value)};expires=${expires.toUTCString()};path=/`;

  // 如果提供了域名，添加域名参数
  if (domain) {
    cookieString += `;domain=${domain}`;
  }

  document.cookie = cookieString;
};

/**
 * 获取 cookie
 * @param name cookie 名称
 * @returns cookie 值
 */
export const getCookie = (name: string): string => {
  const nameEQ = name + '=';
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
  }
  return '';
};

/**
 * 获取 SuperSonic Token
 * @returns SuperSonic Token
 */
export const getSuperSonicToken = (): string => {
  // 从 localStorage 获取 token
  return localStorage.getItem(SUPERSONIC_TOKEN_KEY) || '';
};

/**
 * 获取 SuperSonic 用户信息
 * @returns SuperSonic 用户信息
 */
export const getSuperSonicUserInfo = (): SuperSonicUserInfo | null => {
  const userInfoStr = localStorage.getItem(SUPERSONIC_TOKEN_KEY + '_USER');
  if (userInfoStr) {
    try {
      return JSON.parse(userInfoStr);
    } catch (error) {
      console.error('Failed to parse SuperSonic user info:', error);
    }
  }
  return null;
};

/**
 * 检查 SuperSonic 是否已登录
 * @returns 是否已登录
 */
export const isSuperSonicLoggedIn = (): boolean => {
  return !!getSuperSonicToken();
};

/**
 * 初始化 SuperSonic
 * 如果本地没有 token，则使用默认账号登录
 */
export const initSuperSonic = async (): Promise<boolean> => {
  // 检查本地是否已有 token
  const token = getSuperSonicToken();

  if (!token) {
    console.log('No SuperSonic token found, trying to login');

    try {
      // 尝试使用当前系统用户信息登录
      const userInfo = getUserInfoFromLocalStorage();

      // 如果有用户信息，统一使用用户的 workNo 作为登录用户名和密码
      // 否则使用默认账号登录
      const loginResult = await loginSuperSonic({
        username: userInfo ? userInfo.workNo : 'admin',
        password: userInfo ? userInfo.workNo : '123456'
      });

      if (loginResult.code === 200 && loginResult.data) {
        console.log('SuperSonic login successful');
        return true;
      } else {
        console.error('SuperSonic login failed');
      }
    } catch (loginError) {
      console.error('Error during SuperSonic login:', loginError);
    }

    // 如果登录失败，返回 false
    return false;
  } else {
    console.log('SuperSonic token already exists');
    return true;
  }
};

/**
 * 删除 cookie
 * @param name cookie 名称
 * @param domain 域名（可选）
 */
export const deleteCookie = (name: string, domain?: string): void => {
  // 设置过期时间为过去的时间，即可删除 cookie
  let cookieString = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;

  // 如果提供了域名，添加域名参数
  if (domain) {
    cookieString += `;domain=${domain}`;
  }

  document.cookie = cookieString;
};

/**
 * SuperSonic 登出
 */
export const logoutSuperSonic = (): void => {
  // 从 localStorage 中删除 token
  localStorage.removeItem(SUPERSONIC_TOKEN_KEY);
  localStorage.removeItem(SUPERSONIC_TOKEN_KEY + '_USER');

  console.log('SuperSonic logged out from localStorage');
};

/**
 * SuperSonic 注册方法
 * @param params 注册参数
 * @returns 注册结果
 */
export const registerSuperSonic = async (params: SuperSonicLoginParams): Promise<SuperSonicResponse<string>> => {
  try {
    // 创建一个新的 axios 实例
    const instance = axios.create({
      // 使用相对路径，让 Vite 代理处理跨域请求
      baseURL: '',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      // 对于 CORS 请求，不发送认证信息
      withCredentials: false,
    });

    // 打印请求 URL
    console.log('SuperSonic register URL:', '/api/auth/user/register');

    // 对密码进行加密
    const encryptedParams = {
      username: params.username,
      password: encryptPassword(params.password),
      // 添加其他注册所需的参数
      email: `${params.username}@example.com`,
      name: params.username
    };
    console.log('Encrypted password for SuperSonic register');

    // 发送注册请求
    const response = await instance.post('/api/auth/user/register', encryptedParams);

    // 打印响应信息
    console.log('SuperSonic register response:', response.data);

    // 注册成功后，自动登录
    if (response.data.code === 200) {
      console.log('SuperSonic register successful, trying to login');
      return await loginSuperSonic(params);
    } else {
      console.error('SuperSonic register failed:', response.data.msg);

      // 如果错误信息包含 "user xxx exist"，尝试重置密码
      if (response.data.msg && response.data.msg.includes('exist')) {
        console.log('User already exists, trying to reset password');
        return await resetSuperSonicPassword(params);
      }

      return {
        code: response.data.code || 500,
        data: '',
        msg: response.data.msg || 'Register failed',
        success: false
      };
    }
  } catch (error) {
    console.error('SuperSonic register error:', error);

    // 打印更详细的错误信息
    if (axios.isAxiosError(error)) {
      console.error('SuperSonic register axios error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          baseURL: error.config?.baseURL,
          headers: error.config?.headers,
        }
      });
    }

    // 返回一个错误响应
    return {
      code: 500,
      data: '',
      msg: error instanceof Error ? error.message : 'Unknown error',
      success: false,
    };
  }
};

/**
 * SuperSonic 重置密码方法
 * @param params 重置密码参数
 * @returns 重置密码结果
 */
export const resetSuperSonicPassword = async (params: SuperSonicLoginParams): Promise<SuperSonicResponse<string>> => {
  try {
    // 创建一个新的 axios 实例
    const instance = axios.create({
      // 使用相对路径，让 Vite 代理处理跨域请求
      baseURL: '',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      // 对于 CORS 请求，不发送认证信息
      withCredentials: false,
    });

    // 打印请求 URL
    console.log('SuperSonic reset password URL:', '/api/auth/user/resetPassword');

    // 对密码进行加密
    const encryptedParams = {
      username: params.username,
      password: encryptPassword(params.password),
      // 可能需要的其他参数
      email: `${params.username}@example.com`
    };
    console.log('Encrypted password for SuperSonic reset password');

    // 发送重置密码请求
    const response = await instance.post('/api/auth/user/resetPassword', encryptedParams);

    // 打印响应信息
    console.log('SuperSonic reset password response:', response.data);

    // 重置密码成功后，自动登录
    if (response.data.code === 200) {
      console.log('SuperSonic reset password successful, trying to login');
      return await loginSuperSonic(params);
    } else {
      console.error('SuperSonic reset password failed:', response.data.msg);
      return {
        code: response.data.code || 500,
        data: '',
        msg: response.data.msg || 'Reset password failed',
        success: false
      };
    }
  } catch (error) {
    console.error('SuperSonic reset password error:', error);

    // 打印更详细的错误信息
    if (axios.isAxiosError(error)) {
      console.error('SuperSonic reset password axios error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          baseURL: error.config?.baseURL,
          headers: error.config?.headers,
        }
      });
    }

    // 返回一个错误响应
    return {
      code: 500,
      data: '',
      msg: error instanceof Error ? error.message : 'Unknown error',
      success: false,
    };
  }
};

/**
 * 创建带有认证的 SuperSonic API 请求实例
 * @returns axios 实例
 */
export const createSuperSonicAPI = () => {
  const token = getSuperSonicToken();

  return axios.create({
    // 使用相对路径，让 Vite 代理处理跨域请求
    baseURL: '',
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    },
    // 对于 CORS 请求，不发送认证信息
    withCredentials: false,
  });
};

import { Howl, Howler } from 'howler';
import {
  setTTSCallback,
  setTTSEndCallback,
  setErrorCallback,
  sendTextToSpeech,
  stopTextToSpeech,
  setTTSStoppedCallback,
  sendStreamTextToSpeech,
  sendStreamTextEnd
} from './websocketService';
import { processAIMessage } from '../utils/messageUtils';
import { message as antdMessage } from 'antd';

// 音频播放状态接口
export interface AudioPlayerState {
  isPlaying: boolean;
  isLoading: boolean;
  isBuffering: boolean;
  currentText: string;
}

// 音频播放事件类型
export type AudioPlayerEventType =
  | 'start'      // 开始播放
  | 'stop'       // 停止播放
  | 'buffer'     // 缓冲中
  | 'play'       // 播放中
  | 'end'        // 播放结束
  | 'error';     // 错误

// 音频播放事件回调接口
export interface AudioPlayerEventCallback {
  (event: AudioPlayerEventType, data?: any): void;
}

// 使用Howler.js的音频播放器类
class HowlerAudioPlayer {
  // 状态
  private state: AudioPlayerState = {
    isPlaying: false,
    isLoading: false,
    isBuffering: false,
    currentText: ''
  };

  // 状态变更回调
  private stateChangeCallback: ((state: AudioPlayerState) => void) | null = null;

  // 事件回调
  private eventCallbacks: AudioPlayerEventCallback[] = [];

  // Howler相关
  private audioQueue: Blob[] = [];
  private howls: Howl[] = [];
  private currentHowl: Howl | null = null;
  private isProcessingAudio: boolean = false;
  private beRevokeAudio: string[] = [];

  // 音频上下文解锁状态
  private audioUnlocked: boolean = false;

  // 缓冲区配置
  private bufferThreshold: number = 2; // 开始播放前需要缓冲的音频块数量
  private streamEnded: boolean = false; // 标记流是否结束
  private playbackStarted: boolean = false; // 标记是否已开始播放

  // 消息API
  private messageApi = antdMessage;

  // 用于跟踪标签状态的变量
  private inThinkTag: boolean = false;
  private inDetailsTag: boolean = false;
  private textBuffer: string = '';

  constructor() {
    // 初始化WebSocket连接和回调
    console.log('Howler audio player');
    this.setupCallbacks();

    // 设置Howler全局配置
    Howler.autoUnlock = true; // 尝试自动解锁音频
    Howler.html5PoolSize = 10; // 增加HTML5音频池大小

  }

  // 设置状态变更回调
  public setStateChangeCallback(callback: (state: AudioPlayerState) => void): void {
    this.stateChangeCallback = callback;
  }

  // 添加事件监听器
  public addEventListener(callback: AudioPlayerEventCallback): void {
    this.eventCallbacks.push(callback);
  }

  // 移除事件监听器
  public removeEventListener(callback: AudioPlayerEventCallback): void {
    const index = this.eventCallbacks.indexOf(callback);
    if (index !== -1) {
      this.eventCallbacks.splice(index, 1);
    }
  }

  // 触发事件
  private triggerEvent(event: AudioPlayerEventType, data?: any): void {
    this.eventCallbacks.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        console.error('事件回调执行错误:', error);
      }
    });
  }

  // 获取当前状态
  public getState(): AudioPlayerState {
    return { ...this.state };
  }

  // 更新状态并触发回调
  private updateState(newState: Partial<AudioPlayerState>): void {
    this.state = { ...this.state, ...newState };
    if (this.stateChangeCallback) {
      this.stateChangeCallback(this.state);
    }
  }

  // 设置WebSocket回调
  private setupCallbacks(): void {
    // 设置文字转语音回调
    setTTSCallback((audioChunk) => {
      // 将音频块添加到队列
      this.audioQueue.push(audioChunk);
      console.log('接收到音频块，大小:', audioChunk.size, '字节');

      // 如果正在加载中，表示这是第一批数据，可以取消加载状态
      if (this.state.isLoading) {
        this.updateState({ isLoading: false, isBuffering: true });
        this.triggerEvent('buffer');
      }

      // 如果当前没有在处理音频，开始处理
      if (!this.isProcessingAudio) {
        this.processAudioQueue();
      }
    });

    // 设置文字转语音结束回调
    setTTSEndCallback(() => {
      console.log('语音合成流结束');
      this.streamEnded = true;
      this.updateState({ isLoading: false });

      // 如果缓冲区中有足够的数据但还没开始播放，立即开始播放
      if (!this.playbackStarted && this.howls.length > 0) {
        this.startAudioPlayback();
      }
    });

    setTTSStoppedCallback((message) => {
      console.log('语音合成已停止:', message);
      this.streamEnded = true;
      this.updateState({ isPlaying: false, isLoading: false, isBuffering: false });
      this.triggerEvent('stop', { message });
    });

    // 设置错误回调
    setErrorCallback((error) => {
      this.messageApi.error(`语音合成错误: ${error}`);
      this.updateState({ isPlaying: false, isLoading: false, isBuffering: false });
      this.streamEnded = true;
      this.triggerEvent('error', { error });
    });
  }

  // 处理音频队列 - 使用Howler.js创建音频对象
  private async processAudioQueue(): Promise<void> {
    // 如果队列为空或已经在处理，则返回
    if (this.audioQueue.length === 0 || this.isProcessingAudio) {
      return;
    }

    this.isProcessingAudio = true;

    try {
      // 获取队列中的下一个音频块
      const audioBlob = this.audioQueue.shift();
      if (!audioBlob) {
        this.isProcessingAudio = false;
        return;
      }

      // 创建URL对象
      const audioUrl = URL.createObjectURL(audioBlob);

      // 创建Howl对象
      const howl = new Howl({
        src: [audioUrl],
        format: ['wav'],
        html5: true, // 使用HTML5 Audio以支持流式播放
        onload: () => {
          // 音频加载完成后，释放URL对象
          // URL.revokeObjectURL(audioUrl);
          this.beRevokeAudio.push(audioUrl);
        },
        onend: () => {
          // 当前Howl播放结束
          if (this.currentHowl === howl) {
            console.log("播放结束, 继续播放下一个");
            this.currentHowl = null;
            this.playNextHowl();
          }
        },
        onloaderror: (id, error) => {
          console.error('Howl加载错误:', error);
          this.messageApi.error(`音频加载错误: ${error}`);
          URL.revokeObjectURL(audioUrl);
        },
        onplayerror: (id, error) => {
          console.error('Howl播放错误:', error);
          this.messageApi.error(`音频播放错误: ${error}`);
          howl.once('unlock', function() {
            console.log('音频解锁成功，继续播放');
            const hid = howl.play();
            howl.fade(1, 0.5, 50, hid);
          });
        },
      });

      howl.fade(0.5, 1, 50);

      // 将Howl对象添加到队列
      this.howls.push(howl);
      console.log(`已缓冲 ${this.howls.length} 个音频块，当前阈值: ${this.bufferThreshold}`);

      // 检查播放状态
      if (!this.playbackStarted) {
        // 如果播放尚未开始，检查是否有足够的缓冲数据开始播放
        if (this.howls.length >= this.bufferThreshold ||
            (this.streamEnded && this.howls.length > 0)) {
          // 有足够的数据或流已结束，开始播放
          this.startAudioPlayback();
        }
      } else if (this.howls.length === 1 && !this.currentHowl) {
        // 如果播放已经开始，但当前没有活跃的音频源，且缓冲区刚刚从空变为非空
        console.log('检测到缓冲区从空变为非空，继续播放');
        this.playNextHowl(); // 继续播放
      }

      // 继续处理队列中的下一个音频块
      this.isProcessingAudio = false;
      if (this.audioQueue.length > 0) {
        this.processAudioQueue();
      }
    } catch (error) {
      console.error('处理音频时出错:', error);
      this.messageApi.error(`处理音频时出错: ${error instanceof Error ? error.message : String(error)}`);
      this.isProcessingAudio = false;
      this.updateState({ isPlaying: false, isBuffering: false });
    }
  }

  // 开始音频播放 - 从缓冲区播放音频
  private startAudioPlayback(): void {
    if (this.howls.length === 0 || this.playbackStarted) {
      return;
    }

    console.log('开始播放缓冲的音频');
    this.playbackStarted = true;
    this.updateState({ isPlaying: true, isBuffering: false });
    this.triggerEvent('play', { text: this.state.currentText });

    // 播放第一个Howl
    console.log("startAudioPlayback")
    this.playNextHowl();
  }

  // 播放下一个Howl
  private playNextHowl(): void {
    if (!this.playbackStarted) {
      return;
    }

    // 如果没有更多的Howl
    if (this.howls.length === 0) {
      if (this.streamEnded) {
        // 如果流已结束，则播放完成
        console.log('所有音频播放完成');
        this.playbackStarted = false;
        this.updateState({ isPlaying: false });
        this.triggerEvent('end');
        // 清空缓存
        this.clearRevokeAudio();
      } else {
        // 如果流未结束，等待新的音频块
        console.log('缓冲区为空，等待更多音频数据...');
      }
      return;
    }

    // 获取下一个Howl
    const nextHowl = this.howls.shift();
    if (!nextHowl) {
      return; // 安全检查
    }

    // 存储当前Howl
    this.currentHowl = nextHowl;

    // 开始播放
    const hid = nextHowl.play();
    nextHowl.fade(1, 0.5, 50, hid);

    console.log(`正在播放音频块，剩余缓冲区: ${this.howls.length}`);
  }

  // 停止播放
  public stopPlayback(): void {
    // 停止当前正在播放的音频
    if (this.currentHowl) {
      this.currentHowl.stop();
      this.currentHowl = null;
    }

    // 停止所有Howl
    this.howls.forEach(howl => {
      howl.stop();
      howl.unload(); // 释放资源
    });

    // 清空缓存
    this.clearRevokeAudio();

    // 发送停止指令到服务器
    stopTextToSpeech();

    // 重置所有状态
    this.audioQueue = [];
    this.howls = [];
    this.isProcessingAudio = false;
    this.playbackStarted = false;
    this.streamEnded = true;

    // 更新UI状态
    this.updateState({ isPlaying: false, isBuffering: false });
    this.triggerEvent('stop');

    console.log('已停止播放并清空所有缓冲区');
  }

  // 开始播放
  public startPlayback(text: string): void {
    // 如果已经在播放，则停止
    if (this.state.isPlaying || this.state.isBuffering) {
      this.stopPlayback();
      return;
    }

    // 确保音频已解锁
    // if (!this.audioUnlocked) {
    //   console.log('音频尚未解锁，尝试解锁');
    // }

    // 设置加载状态和当前文本
    this.updateState({ isLoading: true, currentText: text });
    this.triggerEvent('start', { text });

    try {
      // 重置所有状态
      this.audioQueue = [];
      this.howls = [];
      this.isProcessingAudio = false;
      this.playbackStarted = false;
      this.streamEnded = false;

      // 处理消息，移除<think>标签并提取纯文本
      const processedMessage = processAIMessage(text);

      console.log("处理后的消息:", processedMessage);
      if (!processedMessage) {
        this.messageApi.warning('没有可播放的内容');
        this.updateState({ isLoading: false });
        this.triggerEvent('error', { error: '没有可播放的内容' });
        return;
      }

      // 发送文本到WebSocket
      console.log('发送文本到语音合成服务...');
      sendTextToSpeech(processedMessage);
    } catch (error) {
      console.error('开始播放时出错:', error);
      this.messageApi.error(`开始播放时出错: ${error instanceof Error ? error.message : String(error)}`);
      this.updateState({ isLoading: false, isBuffering: false });
      this.triggerEvent('error', { error });
    }
  }

  // 处理<think>标签
  private processThinkTags(): string {
    let processedText = this.textBuffer;
    let startIndex = 0;
    let continueProcessing = true;

    while (continueProcessing) {
      // 查找<think>标签
      const startTagIndex = processedText.indexOf('<think>', startIndex);

      if (startTagIndex !== -1) {
        // 找到开始标签，查找对应的结束标签
        const endTagIndex = processedText.indexOf('</think>', startTagIndex);

        if (endTagIndex !== -1) {
          // 找到完整的标签对，移除它们及其内容
          processedText = processedText.substring(0, startTagIndex) +
                         processedText.substring(endTagIndex + 8); // 8是</think>的长度
          // 继续从当前位置查找下一个标签
          startIndex = startTagIndex;
        } else {
          // 只找到开始标签，没有找到结束标签
          // 保留开始标签之前的内容，标记我们在标签内
          processedText = processedText.substring(0, startTagIndex);
          this.inThinkTag = true;
          continueProcessing = false;
        }
      } else if (this.inThinkTag) {
        // 我们在标签内，但没有找到新的开始标签
        // 查找结束标签
        const endTagIndex = processedText.indexOf('</think>');

        if (endTagIndex !== -1) {
          // 找到结束标签，保留结束标签之后的内容
          processedText = processedText.substring(endTagIndex + 8);
          this.inThinkTag = false;
        } else {
          // 没有找到结束标签，整个文本都在标签内
          processedText = '';
        }
        continueProcessing = false;
      } else {
        // 没有找到任何标签，结束处理
        continueProcessing = false;
      }
    }

    // 更新缓冲区
    this.textBuffer = '';

    return processedText;
  }

  // 处理<details>标签
  private processDetailsTags(text: string): string {
    if (!text) return '';

    let processedText = text;
    let startIndex = 0;
    let continueProcessing = true;

    while (continueProcessing) {
      // 查找<details>标签（考虑可能有属性）
      const startTagIndex = processedText.indexOf('<details', startIndex);

      if (startTagIndex !== -1) {
        // 找到开始标签，查找标签的结束>
        const tagEndIndex = processedText.indexOf('>', startTagIndex);

        if (tagEndIndex !== -1) {
          // 找到标签的结束，查找</details>结束标签
          const endTagIndex = processedText.indexOf('</details>', tagEndIndex);

          if (endTagIndex !== -1) {
            // 找到完整的标签对，移除它们及其内容
            processedText = processedText.substring(0, startTagIndex) +
                           processedText.substring(endTagIndex + 10); // 10是</details>的长度
            // 继续从当前位置查找下一个标签
            startIndex = startTagIndex;
          } else {
            // 只找到开始标签，没有找到结束标签
            // 保留开始标签之前的内容，标记我们在标签内
            processedText = processedText.substring(0, startTagIndex);
            this.inDetailsTag = true;
            continueProcessing = false;
          }
        } else {
          // 没有找到标签的结束>，可能是不完整的标签
          // 保留到目前为止的内容
          processedText = processedText.substring(0, startTagIndex);
          continueProcessing = false;
        }
      } else if (this.inDetailsTag) {
        // 我们在标签内，但没有找到新的开始标签
        // 查找结束标签
        const endTagIndex = processedText.indexOf('</details>');

        if (endTagIndex !== -1) {
          // 找到结束标签，保留结束标签之后的内容
          processedText = processedText.substring(endTagIndex + 10);
          this.inDetailsTag = false;
        } else {
          // 没有找到结束标签，整个文本都在标签内
          processedText = '';
        }
        continueProcessing = false;
      } else {
        // 没有找到任何标签，结束处理
        continueProcessing = false;
      }
    }

    return processedText;
  }

  // 实时播放文本（用于一边流式输出文本，一边进行文字转语音）
  public playRealtimeText(text: string): void {
    // 如果没有文本，则不处理
    if (!text || text.trim() === '') {
      return;
    }

    // 确保音频已解锁
    // if (!this.audioUnlocked) {
    //   console.log('音频尚未解锁，尝试解锁');
    // }

    try {
      // 流式处理文本，过滤<think>和<details>标签
      // 将新文本添加到缓冲区
      this.textBuffer += text;

      // 处理<think>标签
      let processedText = this.processThinkTags();

      // 处理<details>标签
      processedText = this.processDetailsTags(processedText);
      console.log('处理后的实时文本:', processedText);

      // 如果没有处理后的文本，直接返回
      if (!processedText) {
        return;
      }

      // 如果当前没有在播放，则初始化播放状态
      if (!this.state.isPlaying && !this.state.isBuffering && !this.state.isLoading) {
        // 重置所有状态
        this.audioQueue = [];
        this.howls = [];
        this.isProcessingAudio = false;
        this.playbackStarted = false;
        this.streamEnded = false;

        // 更新状态
        this.updateState({ isLoading: true, currentText: processedText });
        this.triggerEvent('start', { text: processedText });
      }

      // 发送流式文本到WebSocket
      console.log('发送流式文本到语音合成服务...');
      sendStreamTextToSpeech(processedText);
    } catch (error) {
      console.error('实时播放文本时出错:', error);
    }
  }

  // 结束实时播放
  public endRealtimePlay(): void {
    try {
      // 处理缓冲区中剩余的文本
      if (this.textBuffer && !this.inThinkTag && !this.inDetailsTag) {
        // 如果缓冲区中有文本，且不在标签内，发送它
        const processedText = this.processDetailsTags(this.processThinkTags());
        if (processedText) {
          sendStreamTextToSpeech(processedText);
        }
      }

      // 重置标签状态
      this.inThinkTag = false;
      this.inDetailsTag = false;
      this.textBuffer = '';

      // 发送流式文本结束信号
      console.log('发送流式文本结束信号...');
      sendStreamTextEnd();
    } catch (error) {
      console.error('结束实时播放时出错:', error);
    }
  }

  // 切换播放状态
  public togglePlayback(text: string): void {
    if (this.state.isPlaying || this.state.isBuffering) {
      this.stopPlayback();
    } else {
      this.startPlayback(text);
    }
  }

  public clearRevokeAudio() {
    this.beRevokeAudio.forEach(audioUrl => {
      URL.revokeObjectURL(audioUrl);
    });
    this.beRevokeAudio = [];
  }
}

// 创建单例实例
const howlerAudioPlayer = new HowlerAudioPlayer();

export default howlerAudioPlayer;

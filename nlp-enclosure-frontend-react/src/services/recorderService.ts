import Recorder from 'recorder-core';
import 'recorder-core/src/engine/pcm'; // 导入 PCM 格式支持
import http from '../utils/request';


// 定义录音配置类型
interface RecorderConfig {
  sampleRate?: number;
  bitRate?: number;
  type?: string;
  onProcess?: (
    buffers: Array<Array<number>>,
    powerLevel: number,
    bufferDuration: number,
    bufferSampleRate: number,
    newBufferIdx: number,
    asyncEnd: boolean
  ) => boolean | void;
}

// 定义服务器响应类型
interface STTResponse {
  status: string;
  text: string;
  message?: string;
}

class RecorderService {
  private recorder: any = null;
  private isRecording = false;
  private config: RecorderConfig = {
    sampleRate: 16000, // 默认采样率 16000Hz
    bitRate: 16,       // 默认位深度 16 位
    type: 'pcm',       // 默认格式 PCM
  };

  /**
   * 设置录音配置
   * @param config 录音配置
   */
  public setConfig(config: Partial<RecorderConfig>): void {
    this.config = { ...this.config, ...config };
    // 如果录音器已经初始化，需要重新初始化
    if (this.recorder) {
      this.close();
    }
  }

  /**
   * 初始化录音器
   * @returns Promise<boolean> 是否成功初始化
   */
  public async init(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        this.recorder = Recorder(this.config);

        this.recorder.open(() => {
          // 录音器已成功打开
          resolve(true);
        }, (msg: string, isUserNotAllow: boolean) => {
          // 录音器打开失败
          const errorMsg = isUserNotAllow ? '用户拒绝了录音权限' : `录音器初始化失败: ${msg}`;
          console.error(errorMsg);
          reject(new Error(errorMsg));
        });
      } catch (error) {
        console.error('初始化录音器时出错:', error);
        reject(error);
      }
    });
  }

  /**
   * 开始录音
   * @returns boolean 是否成功开始录音
   */
  public start(): boolean {
    if (!this.recorder) {
      console.error('录音器未初始化');
      return false;
    }

    try {
      this.recorder.start();
      this.isRecording = true;
      return true;
    } catch (error) {
      console.error('开始录音时出错:', error);
      return false;
    }
  }

  /**
   * 停止录音
   * @returns Promise<{ pcmBlob: Blob, duration: number }> PCM 格式的录音数据和时长
   */
  public async stop(): Promise<{ pcmBlob: Blob, duration: number }> {
    if (!this.recorder || !this.isRecording) {
      return Promise.reject(new Error('录音器未初始化或未在录音'));
    }

    return new Promise((resolve, reject) => {
      this.recorder.stop((blob: Blob, duration: number) => {
        this.isRecording = false;
        console.log(`录音结束，时长: ${duration}ms`);
        resolve({ pcmBlob: blob, duration });
      }, (msg: string) => {
        console.error('停止录音时出错:', msg);
        this.isRecording = false;
        reject(new Error(msg));
      });
    });
  }

  /**
   * 获取录音的 PCM 数据
   * @returns Int16Array PCM 数据
   */
  public getPCMData(): Int16Array | null {
    if (!this.recorder) {
      console.error('录音器未初始化');
      return null;
    }

    const buffers = this.recorder.buffers;
    if (!buffers || buffers.length === 0) {
      return null;
    }

    // 计算总长度
    let totalLength = 0;
    for (const buffer of buffers) {
      totalLength += buffer.length;
    }

    // 创建一个新的 Int16Array 来存储所有数据
    const result = new Int16Array(totalLength);
    let offset = 0;
    for (const buffer of buffers) {
      result.set(buffer, offset);
      offset += buffer.length;
    }

    return result;
  }

  /**
   * 提交 PCM 数据到服务器进行语音识别
   * @param pcmData PCM 格式的录音数据 (Blob 或 ArrayBuffer 或 Int16Array)
   * @param sampleRate 采样率
   * @returns Promise<string> 识别结果
   */
  public async submitToServer(
    pcmData: Blob | ArrayBuffer | Int16Array,
    sampleRate = 16000
  ): Promise<string> {
    // 使用 Java 后端接口，而不是直接调用 chatvoice 服务
    const apiUrl = `./enclosure/voice/stt/upload`;
    const formData = new FormData();

    // 创建 PCM 文件
    let pcmBlob: Blob;
    if (pcmData instanceof Blob) {
      pcmBlob = pcmData;
    } else if (pcmData instanceof Int16Array) {
      pcmBlob = new Blob([pcmData.buffer], { type: 'audio/pcm' });
    } else {
      pcmBlob = new Blob([pcmData], { type: 'audio/pcm' });
    }

    formData.append('audio_file', pcmBlob, 'audio.pcm');
    formData.append('sample_rate', sampleRate.toString());

    try {
      const response = await http.post<STTResponse>(apiUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.status === 'success') {
        // 去掉多余的内容
        const result = response.text.split('<|withitn|>')[1];
        return result;
      } else {
        throw new Error(response.message || '语音识别失败');
      }
    } catch (error) {
      console.error('提交语音数据时出错:', error);
      throw error;
    }
  }

  /**
   * 录音并识别（自动开始和停止录音）
   * @param recordDuration 录音时长（毫秒），默认 3000ms
   * @returns Promise<string> 识别结果
   */
  public async recordAndRecognize(recordDuration = 3000): Promise<string> {
    try {
      // 确保录音器已初始化
      if (!this.recorder) {
        await this.init();
      }

      // 开始录音
      const startSuccess = this.start();
      if (!startSuccess) {
        throw new Error('开始录音失败');
      }

      // 等待指定时长
      await new Promise(resolve => setTimeout(resolve, recordDuration));

      // 停止录音
      const { pcmBlob } = await this.stop();

      // 提交到服务器进行识别
      return await this.submitToServer(pcmBlob, this.config.sampleRate);
    } catch (error) {
      console.error('录音识别过程出错:', error);
      throw error;
    } finally {
      // 确保录音器被关闭
      if (this.isRecording) {
        try {
          await this.stop();
        } catch (e) {
          console.error('停止录音时出错:', e);
        }
      }
    }
  }

  /**
   * 关闭录音器，释放资源
   */
  public close(): void {
    if (this.recorder) {
      if (this.isRecording) {
        try {
          this.recorder.stop();
        } catch (e) {
          console.error('停止录音时出错:', e);
        }
      }
      this.recorder.close();
      this.recorder = null;
      this.isRecording = false;
    }
  }

  /**
   * 检查是否正在录音
   * @returns boolean 是否正在录音
   */
  public isCurrentlyRecording(): boolean {
    return this.isRecording;
  }
}

// 导出单例实例
export const recorderService = new RecorderService();
export default recorderService;

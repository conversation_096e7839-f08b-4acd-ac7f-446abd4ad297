import { getWWRegisterData } from './wwService';
import * as ww from '@wecom/jssdk';
import { message as antdMessage } from 'antd';
import recorderService from './recorderService';

// 用于存储当前录音状态
let isRecording = false;

// 用于存储麦克风权限状态
let hasPermission: boolean | null = null;

// 用于存储企业微信环境下的录音状态
let isWWRecording = false;

// 用于存储企业微信录音的localId
let wwRecordLocalId: string | null = null;

/**
 * 检查麦克风权限
 * @returns 返回权限状态：'granted', 'denied', 'prompt'
 */
export const checkMicrophonePermission = async (): Promise<string> => {
  if (typeof navigator === 'undefined' || !navigator.permissions) {
    return 'prompt';
  }

  try {
    const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName });
    return permissionStatus.state;
  } catch (error) {
    console.error('检查麦克风权限时出错:', error);
    return 'prompt';
  }
};

/**
 * 请求麦克风权限
 * @returns 是否获得权限
 */
export const requestMicrophonePermission = async (): Promise<boolean> => {
  try {
    await navigator.mediaDevices.getUserMedia({ audio: true });
    return true;
  } catch (error) {
    console.error('请求麦克风权限时出错:', error);
    return false;
  }
};

/**
 * 初始化麦克风服务
 */
export const initMicrophoneService = async (): Promise<void> => {
  // 检查是否在企业微信环境中
  const wwRegisterData = getWWRegisterData();
  const isInWWEnv = !!wwRegisterData;

  // 如果不在企业微信环境中，初始化 recorder
  if (!isInWWEnv) {
    try {
      await recorderService.init();
    } catch (error) {
      console.error('初始化录音器时出错:', error);
    }
  }
};

/**
 * 开始录音
 * @param startCallback
 * @param errorCallback 错误回调
 * @returns 是否成功开始录音
 */
export const startRecording = async (
  startCallback?: () => void,
  errorCallback?: (error: string) => void
): Promise<boolean> => {
  // 如果已经在录音，则不执行任何操作
  if (isRecording || isWWRecording) {
    console.log('已经在录音，不执行任何操作');
    return true;
  }

  // 检查是否在企业微信环境中
  const wwRegisterData = getWWRegisterData();
  const isInWWEnv = !!wwRegisterData;

  // 如果在企业微信环境中，使用企业微信的录音API
  if (isInWWEnv) {
    try {
      console.log('使用企业微信录音API');

      // 清除旧的localId
      wwRecordLocalId = null;

      // 注册录音结束事件监听
      ww.onVoiceRecordEnd((res: { localId: string }) => {
        console.log('企业微信录音自动结束', res);

        // 保存localId
        wwRecordLocalId = res.localId;

        // 更新状态
        isWWRecording = false;

        // 通知用户录音已结束
      })

      // 开始录音
      ww.startRecord({
        success: () => {
          console.log('企业微信开始录音成功');
          if (startCallback) {
            startCallback();
          }
          isWWRecording = true;
        },
        fail: (res) => {
          console.error('企业微信开始录音失败', res);
          wwRecordLocalId = null;
          if (errorCallback) {
            errorCallback(`企业微信开始录音失败: ${res.errMsg}`);
          }
        }
      });

      return true;
    } catch (error) {
      console.error('企业微信录音出错:', error);
      if (errorCallback) {
        errorCallback(`企业微信录音出错: ${error instanceof Error ? error.message : String(error)}`);
      }
      return false;
    }
  } else {
    // 非企业微信环境，使用 recorder-core 进行录音
    console.log('非企业微信环境，使用 recorder-core 进行录音');
    try {
      // 确保 recorder 已初始化
      if (!recorderService.isCurrentlyRecording()) {
        // 开始录音
        const startSuccess = recorderService.start();
        if (startSuccess) {
          isRecording = true;
          if (startCallback) {
            startCallback();
          }
          return true;
        } else {
          if (errorCallback) {
            errorCallback('开始录音失败');
          }
          return false;
        }
      } else {
        console.log('recorder 已经在录音中');
        return true;
      }
    } catch (error) {
      console.error('recorder 录音出错:', error);
      if (errorCallback) {
        errorCallback(`录音出错: ${error instanceof Error ? error.message : String(error)}`);
      }
      return false;
    }
  }
};

/**
 * 停止录音
 * @returns 是否成功停止录音
 */
export const stopRecording = (
  stopCallback?: (text: string) => void,
  errorCallback?: (error: string) => void
): boolean => {
  // 检查是否在企业微信环境中
  const wwRegisterData = getWWRegisterData();
  const isInWWEnv = !!wwRegisterData;

  // 如果在企业微信环境中且正在录音
  if (isInWWEnv && isWWRecording) {
    try {
      // 停止企业微信录音
      ww.stopRecord({
        success: (res: { localId: string }) => {
          console.log('企业微信停止录音成功', res);

          // 保存localId
          wwRecordLocalId = res.localId;

          // 更新状态
          isWWRecording = false;

          // 调用语音转文字API
          translateVoice(res.localId).then((text) => {
            console.log('语音转文字结果:', text);
            // 通知用户录音已结束
            if (stopCallback) {
              stopCallback(text);
            }
          }).catch((error) => {
            console.error('语音转文字失败:', error);
            antdMessage.error('语音转文字失败');
            if (errorCallback) {
              errorCallback(error.errMsg)
            }
          })
        },
        fail: (res: { errMsg: string }) => {
          console.error('企业微信停止录音失败', res);
          isWWRecording = false;
          wwRecordLocalId = null;
          if (errorCallback) {
            errorCallback(res.errMsg)
          }
        }
      });
      return true;
    } catch (error) {
      console.error('企业微信停止录音出错:', error);
      isWWRecording = false;
      return false;
    }
  }

  // 如果不在企业微信环境中且正在录音，使用 recorder-core 停止录音
  if (!isInWWEnv && isRecording) {
    try {
      // 停止录音
      recorderService.stop().then(async ({ pcmBlob, duration }) => {
        console.log(`recorder 停止录音成功，时长: ${duration}ms`);

        // 更新状态
        isRecording = false;

        try {
          // 提交到服务器进行语音识别
          const text = await recorderService.submitToServer(pcmBlob);
          console.log('语音转文字结果:', text);

          // 通知用户录音已结束
          if (stopCallback) {
            stopCallback(text);
          }
        } catch (error) {
          console.error('语音转文字失败:', error);
          antdMessage.error('语音转文字失败');
          if (errorCallback) {
            errorCallback(`语音转文字失败: ${error instanceof Error ? error.message : String(error)}`);
          }
        }
      }).catch((error) => {
        console.error('recorder 停止录音失败:', error);
        isRecording = false;
        if (errorCallback) {
          errorCallback(`停止录音失败: ${error instanceof Error ? error.message : String(error)}`);
        }
      });

      return true;
    } catch (error) {
      console.error('recorder 停止录音出错:', error);
      isRecording = false;
      return false;
    }
  }

  return false;
};


/**
 * 清理资源
 */
export const cleanup = (): void => {
  // 检查是否在企业微信环境中
  const wwRegisterData = getWWRegisterData();
  const isInWWEnv = !!wwRegisterData;

  // 如果在企业微信环境中且正在录音，停止企业微信录音
  if (isInWWEnv && isWWRecording) {
    try {
      ww.stopRecord({
        success: () => {
          console.log('企业微信录音已停止（清理）');
        },
        fail: () => {
          console.error('企业微信停止录音失败（清理）');
        }
      });
    } catch (error) {
      console.error('企业微信停止录音出错（清理）:', error);
    }
  }

  // 如果在非企业微信环境中且正在录音，停止 recorder 录音
  if (!isInWWEnv && isRecording) {
    try {
      recorderService.close();
      console.log('recorder 录音已停止（清理）');
    } catch (error) {
      console.error('recorder 停止录音出错（清理）:', error);
    }
  }

  // 重置所有状态
  isRecording = false;
  isWWRecording = false;
  wwRecordLocalId = null;
};

/**
 * 获取当前企业微信录音的localId
 * @returns 当前保存的localId，如果没有则返回null
 */
export const getWWRecordLocalId = (): string | null => {
  return wwRecordLocalId;
};

/**
 * 将语音转换为文字
 * @param localId 可选，如果不提供则使用最近一次录音的localId
 * @param showProgressTips 是否显示进度提示，默认为true
 * @returns 返回一个Promise，解析为转换后的文本
 */
export const translateVoice = async (
  localId?: string,
  showProgressTips = true
): Promise<string> => {
  // 检查是否在企业微信环境中
  const wwRegisterData = getWWRegisterData();
  const isInWWEnv = !!wwRegisterData;

  if (!isInWWEnv) {
    // 在非企业微信环境中，使用 recorder-core 进行语音识别
    console.log('非企业微信环境下，使用 recorder-core 进行语音识别');

    // 如果当前正在录音，先停止录音
    if (isRecording) {
      try {
        const { pcmBlob } = await recorderService.stop();
        isRecording = false;

        // 提交到服务器进行语音识别
        return await recorderService.submitToServer(pcmBlob);
      } catch (error) {
        console.error('recorder 语音识别出错:', error);
        throw new Error(`语音识别失败: ${error instanceof Error ? error.message : String(error)}`);
      }
    } else {
      // 如果没有正在进行的录音，返回空字符串
      console.warn('没有正在进行的录音');
      return '';
    }
  }

  // 如果没有提供localId，使用全局保存的localId
  const id = localId || wwRecordLocalId;

  // 如果没有可用的localId，抛出错误
  if (!id) {
    throw new Error('没有可用的录音文件ID');
  }

  // 调用企业微信的translateVoice API
  return new Promise<string>((resolve, reject) => {
    ww.translateVoice({
      localId: id,
      isShowProgressTips: showProgressTips,
      success: (res: { translateResult: string }) => {
        console.log('语音转文字成功', res);
        resolve(res.translateResult);
      },
      fail: (res: { errMsg: string }) => {
        console.error('语音转文字失败', res);
        reject(new Error(`语音转文字失败: ${res.errMsg}`));
      }
    });
  });
};

/**
 * 获取文件的base64编码内容
 * @param localId 可选，如果不提供则使用最近一次录音的localId
 */
export const getRecordFileData = async (localId?: string): Promise<string | null> => {
  // 如果没有提供localId，使用全局保存的localId
  const id = localId || wwRecordLocalId;

  // 如果没有可用的localId，返回null
  if (!id) {
    console.error('没有可用的录音文件ID');
    return null;
  }

  const localFileData = await ww.getLocalFileData({
    localId: id,
  });

  console.log(" localFileData: ", localFileData);

  if (localFileData.errMsg !== "getLocalFileData:ok") {
    console.error('获取录音文件数据失败', localFileData);
    return null;
  }

  return localFileData.localData; // 注意：这里应该是localData而不是data
}


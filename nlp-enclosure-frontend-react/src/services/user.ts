import http, { setAuthToken, clearAuthToken } from '../utils/request';
import {Account} from "../types/account.ts";

const baseUrl = import.meta.env.VITE_APP_API;

export const dingTalklogin = ()=> {
    http.get('./enclosure/idaas/login/dingtalk')
}

export const userNameLogin = (form: any) => {
    return http.post('./enclosure/user/login', null, {
        params: {
            username: form.username,
            password: form.password,
        }
    }).then(response => {
        // 登录成功后保存token
        if (response && response.token) {
            setAuthToken(response.token);
        }
        return response;
    })
}

export const getUserInfo = async ():Promise<Account> => {
    const userInfoFromLocalStorage = getUserInfoFromLocalStorage();
    if (userInfoFromLocalStorage) {
        return userInfoFromLocalStorage
    }

    const res = await http.get('./enclosure/dify/user/info')
    console.log(res)
    const userInfo = res.payload.principal.attributes;
    console.log(userInfo)
    if (userInfo) {
        setUserInfoToLocalStorage(userInfo)
    }
    return userInfo
}

export const setUserInfoToLocalStorage = (userInfo: Account) => {
    localStorage.setItem('userInfo', JSON.stringify(userInfo))
}

export const clearUserInfoFormLocalStorage = () => {
    localStorage.removeItem('userInfo')
}

export const getUserInfoFromLocalStorage = (): Account | null => {
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
        return JSON.parse(userInfo)
    }
    return null
}

export const logout = () => {
    clearUserInfoFormLocalStorage();
    clearAuthToken(); // 清除token
    http.post('./enclosure/logout').then(res => {
        if (res) {
            let pre = '';
            if (baseUrl.includes('localhost')) {
                pre = "/#/"
            } else {
                pre = "/index.html#/"
            }

            let path = ''
            if (res.integrate === 'username') {
                path = 'userLogin';
            } else if (res.integrate === 'gzyc') {
                path = 'idaasLogin';
            }

            window.location.href = pre + path;
        }
    })
}

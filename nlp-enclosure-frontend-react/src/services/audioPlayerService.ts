import {
  setTTSCallback,
  setTTSEndCallback,
  setErrorCallback,
  sendTextToSpeech,
  stopTextToSpeech,
  setTTSStoppedCallback,
  sendStreamTextToSpeech,
  sendStreamTextEnd
} from './websocketService';
import { processAIMessage } from '../utils/messageUtils';
import { message as antdMessage } from 'antd';


// 音频播放状态接口
export interface AudioPlayerState {
  isPlaying: boolean;
  isLoading: boolean;
  isBuffering: boolean;
  currentText: string;
}

// 音频播放事件类型
export type AudioPlayerEventType =
  | 'start'      // 开始播放
  | 'stop'       // 停止播放
  | 'buffer'     // 缓冲中
  | 'play'       // 播放中
  | 'end'        // 播放结束
  | 'error';     // 错误

// 音频播放事件回调接口
export interface AudioPlayerEventCallback {
  (event: AudioPlayerEventType, data?: any): void;
}

// 音频播放器类
class AudioPlayer {
  // 状态
  private state: AudioPlayerState = {
    isPlaying: false,
    isLoading: false,
    isBuffering: false,
    currentText: ''
  };

  // 状态变更回调
  private stateChangeCallback: ((state: AudioPlayerState) => void) | null = null;

  // 事件回调
  private eventCallbacks: AudioPlayerEventCallback[] = [];

  // 音频处理相关的引用
  private audioQueue: Blob[] = [];
  private audioBuffers: AudioBuffer[] = [];
  private audioContext: AudioContext | null = null;
  private currentSource: AudioBufferSourceNode | null = null;
  private isProcessingAudio: boolean = false;

  // 缓冲区配置
  private bufferThreshold: number = 1; // 开始播放前需要缓冲的音频块数量
  private streamEnded: boolean = false; // 标记流是否结束
  private playbackStarted: boolean = false; // 标记是否已开始播放
  private nextStartTime: number = 0; // 下一个音频块的开始时间

  // 消息API
  private messageApi = antdMessage;

  constructor() {
    // 初始化WebSocket连接和回调
    // this.setupCallbacks();
    // this.initAudioContextForMobile();
  }

  // 设置状态变更回调
  public setStateChangeCallback(callback: (state: AudioPlayerState) => void): void {
    this.stateChangeCallback = callback;
  }

  // 添加事件监听器
  public addEventListener(callback: AudioPlayerEventCallback): void {
    this.eventCallbacks.push(callback);
  }

  // 移除事件监听器
  public removeEventListener(callback: AudioPlayerEventCallback): void {
    const index = this.eventCallbacks.indexOf(callback);
    if (index !== -1) {
      this.eventCallbacks.splice(index, 1);
    }
  }

  // 触发事件
  private triggerEvent(event: AudioPlayerEventType, data?: any): void {
    this.eventCallbacks.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        console.error('事件回调执行错误:', error);
      }
    });
  }

  // 获取当前状态
  public getState(): AudioPlayerState {
    return { ...this.state };
  }

  // 更新状态并触发回调
  private updateState(newState: Partial<AudioPlayerState>): void {
    this.state = { ...this.state, ...newState };
    if (this.stateChangeCallback) {
      this.stateChangeCallback(this.state);
    }
  }

  // 设置WebSocket回调
  private setupCallbacks(): void {
    // 设置文字转语音回调
    setTTSCallback((audioChunk) => {
      // 将音频块添加到队列
      this.audioQueue.push(audioChunk);
      console.log('接收到音频块，大小:', audioChunk.size, '字节');

      // 如果正在加载中，表示这是第一批数据，可以取消加载状态
      if (this.state.isLoading) {
        this.updateState({ isLoading: false, isBuffering: true });
        this.triggerEvent('buffer');
      }

      // 如果当前没有在处理音频，开始处理
      if (!this.isProcessingAudio) {
        this.processAudioQueue();
      }
    });

    // 设置文字转语音结束回调
    setTTSEndCallback(() => {
      console.log('语音合成流结束');
      this.streamEnded = true;
      this.updateState({ isLoading: false });

      // 如果缓冲区中有足够的数据但还没开始播放，立即开始播放
      if (!this.playbackStarted && this.audioBuffers.length > 0) {
        this.startAudioPlayback();
      }
    });

    setTTSStoppedCallback((message) => {
      console.log('语音合成已停止:', message);
      this.streamEnded = true;
      this.updateState({ isPlaying: false, isLoading: false, isBuffering: false });
      this.triggerEvent('stop', { message });
    });

    // 设置错误回调
    setErrorCallback((error) => {
      this.messageApi.error(`语音合成错误: ${error}`);
      this.updateState({ isPlaying: false, isLoading: false, isBuffering: false });
      this.streamEnded = true;
      this.triggerEvent('error', { error });
    });
  }

  public initAudioContextForMobile(): void {
    // 用户交互时初始化AudioContext
    const initContext = () => {
      console.log(" 触发初始化 AudioContent")
        if (!this.audioContext) {
          console.log(" 执行初始化 AudioContent")
          this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

          if (this.audioContext.state === 'suspended') {
            this.audioContext.resume().then(() => {
              console.log('AudioContext已恢复');
            });
          }

          // 创建一个空的buffer并播放，解锁音频上下文
          const buffer = this.audioContext.createBuffer(1, 1, 22050);
          const source = this.audioContext.createBufferSource();
          source.buffer = buffer;
          source.connect(this.audioContext.destination);
          source.start(0);
          source.stop(0.001); // 确保非常短暂
          console.log('AudioContext已初始化并解锁');
        }

      // 移除事件监听器
      ['touchstart', 'touchend', 'click'].forEach(event => {
        document.removeEventListener(event, initContext);
      });
    };

    // 添加事件监听器
    ['touchstart', 'touchend', 'click'].forEach(event => {
      document.addEventListener(event, initContext, { once: true });
    });
  }

  // 处理音频队列 - 解码音频并添加到缓冲区
  private async processAudioQueue(): Promise<void> {
    // 如果队列为空或已经在处理，则返回
    if (this.audioQueue.length === 0 || this.isProcessingAudio) {
      return;
    }

    this.isProcessingAudio = true;

    try {
      // 初始化AudioContext（如果尚未初始化）
      if (!this.audioContext) {
        console.log(" 初始化123123AudioContext")
        this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        if (this.audioContext.state === 'suspended') {
          await this.audioContext.resume();
        }
      }

      // 获取队列中的下一个音频块
      const audioBlob = this.audioQueue.shift();
      if (!audioBlob) {
        this.isProcessingAudio = false;
        return;
      }

      // 将Blob转换为ArrayBuffer
      const arrayBuffer = await audioBlob.arrayBuffer();

      // 解码音频数据
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

      // 将解码后的音频添加到缓冲区
      this.audioBuffers.push(audioBuffer);
      console.log(`已缓冲 ${this.audioBuffers.length} 个音频块，当前阈值: ${this.bufferThreshold}`);

      // 检查播放状态
      console.log(" 检查播放状态", this.playbackStarted)
      if (!this.playbackStarted) {
        // 如果播放尚未开始，检查是否有足够的缓冲数据开始播放
        if (this.audioBuffers.length >= this.bufferThreshold ||
            (this.streamEnded && this.audioBuffers.length > 0)) {
          // 有足够的数据或流已结束，开始播放
          this.startAudioPlayback();
        }
      } else if (this.audioBuffers.length === 1 && !this.currentSource) {
        // 如果播放已经开始，但当前没有活跃的音频源，且缓冲区刚刚从空变为非空
        // 这种情况发生在缓冲区被消耗完后，新的音频块到达
        console.log('检测到缓冲区从空变为非空，继续播放');
        this.playNextBuffer(); // 继续播放
      }

      // 继续处理队列中的下一个音频块
      this.isProcessingAudio = false;
      if (this.audioQueue.length > 0) {
        this.processAudioQueue();
      }
    } catch (error) {
      console.error('处理音频时出错:', error);
      this.messageApi.error(`处理音频时出错: ${error instanceof Error ? error.message : String(error)}`);
      this.isProcessingAudio = false;
      this.updateState({ isPlaying: false, isBuffering: false });
    }
  }

  // 开始音频播放 - 从缓冲区播放音频
  private startAudioPlayback(): void {
    console.log("startAudioPlayback")
    if (!this.audioContext || this.audioBuffers.length === 0 /*|| this.playbackStarted*/) {
      return;
    }

    console.log('开始播放缓冲的音频');
    this.playbackStarted = true;
    this.updateState({ isPlaying: true, isBuffering: false });
    this.triggerEvent('play', { text: this.state.currentText });

    // 设置开始时间为当前时间
    this.nextStartTime = this.audioContext.currentTime;

    // 播放第一个缓冲区
    this.playNextBuffer();
  }

  // 播放下一个缓冲区
  private playNextBuffer(): void {
    console.log("playNextBuffer befor")
    if (!this.audioContext || !this.playbackStarted) {
      return;
    }
    console.log("playNextBuffer next")

    // 如果没有更多的缓冲区
    if (this.audioBuffers.length === 0) {
      if (this.streamEnded) {
        // 如果流已结束，则播放完成
        console.log('所有音频播放完成');
        this.playbackStarted = false;
        this.updateState({ isPlaying: false });
        this.triggerEvent('end');
      } else {
        // 如果流未结束，等待新的音频块
        console.log('缓冲区为空，等待更多音频数据...');
        // 不要重置playbackStarted，保持播放状态
        // 当新的音频块到达时，会通过processAudioQueue中的逻辑继续播放
      }
      return;
    }

    // 获取下一个音频缓冲区
    const nextBuffer = this.audioBuffers.shift();
    if (!nextBuffer) {
      console.log("playNextBuffer nextBuffer return")
      return; // 安全检查
    }

    // 创建音频源
    const source = this.audioContext.createBufferSource();
    source.buffer = nextBuffer;
    source.connect(this.audioContext.destination);

    // 存储当前音频源
    this.currentSource = source;
//     this.currentSource.playbackRate.value = 0.9;

    // 计算音频持续时间
    const duration = nextBuffer.duration;

    // 设置播放结束回调
    source.onended = () => {
      this.currentSource = null;
      // 播放下一个缓冲区
      this.playNextBuffer();
    };

    // 开始播放
    source.start(this.nextStartTime);

    // 更新下一个开始时间
    this.nextStartTime += duration;

    console.log(`正在播放音频块，持续时间: ${duration.toFixed(2)}秒，剩余缓冲区: ${this.audioBuffers.length}`);


  }

  // 停止播放
  public stopPlayback(): void {
    // 停止当前正在播放的音频
    if (this.currentSource) {
      try {
        this.currentSource.stop();
      } catch (error) {
        // 忽略已经停止的音频源错误
        console.error('停止音频源时出错:', error);
      }
      this.currentSource = null;
    }

    // 发送停止指令到服务器
    stopTextToSpeech();

    // 重置所有状态
    this.audioQueue = [];
    this.audioBuffers = [];
    this.isProcessingAudio = false;
    this.playbackStarted = false;
    this.streamEnded = true;
    this.nextStartTime = 0;

    // 更新UI状态
    this.updateState({ isPlaying: false, isBuffering: false });
    this.triggerEvent('stop');

    console.log('已停止播放并清空所有缓冲区');
  }

  // 开始播放
  public startPlayback(text: string): void {
    // 如果已经在播放，则停止
    if (this.state.isPlaying || this.state.isBuffering) {
      this.stopPlayback();
      return;
    }

    // 设置加载状态和当前文本
    this.updateState({ isLoading: true, currentText: text });
    this.triggerEvent('start', { text });

    try {
      // 重置所有状态
      this.audioQueue = [];
      this.audioBuffers = [];
      this.isProcessingAudio = false;
      this.playbackStarted = false;
      this.streamEnded = false;
      this.nextStartTime = 0;

      // 处理消息，移除<think>标签并提取纯文本
      const processedMessage = processAIMessage(text);

      console.log("处理后的消息:", processedMessage);
      if (!processedMessage) {
        this.messageApi.warning('没有可播放的内容');
        this.updateState({ isLoading: false });
        this.triggerEvent('error', { error: '没有可播放的内容' });
        return;
      }

      // 发送文本到WebSocket
      console.log('发送文本到语音合成服务...');
      sendTextToSpeech(processedMessage);
    } catch (error) {
      console.error('开始播放时出错:', error);
      this.messageApi.error(`开始播放时出错: ${error instanceof Error ? error.message : String(error)}`);
      this.updateState({ isLoading: false, isBuffering: false });
      this.triggerEvent('error', { error });
    }
  }

  // 用于跟踪标签状态的变量
  private inThinkTag: boolean = false;
  private inDetailsTag: boolean = false;
  private textBuffer: string = '';

  // 处理<think>标签
  private processThinkTags(): string {
    let processedText = this.textBuffer;
    let startIndex = 0;
    let continueProcessing = true;

    while (continueProcessing) {
      // 查找<think>标签
      const startTagIndex = processedText.indexOf('<think>', startIndex);

      if (startTagIndex !== -1) {
        // 找到开始标签，查找对应的结束标签
        const endTagIndex = processedText.indexOf('</think>', startTagIndex);

        if (endTagIndex !== -1) {
          // 找到完整的标签对，移除它们及其内容
          processedText = processedText.substring(0, startTagIndex) +
                         processedText.substring(endTagIndex + 8); // 8是</think>的长度
          // 继续从当前位置查找下一个标签
          startIndex = startTagIndex;
        } else {
          // 只找到开始标签，没有找到结束标签
          // 保留开始标签之前的内容，标记我们在标签内
          processedText = processedText.substring(0, startTagIndex);
          this.inThinkTag = true;
          continueProcessing = false;
        }
      } else if (this.inThinkTag) {
        // 我们在标签内，但没有找到新的开始标签
        // 查找结束标签
        const endTagIndex = processedText.indexOf('</think>');

        if (endTagIndex !== -1) {
          // 找到结束标签，保留结束标签之后的内容
          processedText = processedText.substring(endTagIndex + 8);
          this.inThinkTag = false;
        } else {
          // 没有找到结束标签，整个文本都在标签内
          processedText = '';
        }
        continueProcessing = false;
      } else {
        // 没有找到任何标签，结束处理
        continueProcessing = false;
      }
    }

    // 更新缓冲区
    this.textBuffer = '';

    return processedText;
  }

  // 处理<details>标签
  private processDetailsTags(text: string): string {
    if (!text) return '';

    let processedText = text;
    let startIndex = 0;
    let continueProcessing = true;

    while (continueProcessing) {
      // 查找<details>标签（考虑可能有属性）
      const startTagIndex = processedText.indexOf('<details', startIndex);

      if (startTagIndex !== -1) {
        // 找到开始标签，查找标签的结束>
        const tagEndIndex = processedText.indexOf('>', startTagIndex);

        if (tagEndIndex !== -1) {
          // 找到标签的结束，查找</details>结束标签
          const endTagIndex = processedText.indexOf('</details>', tagEndIndex);

          if (endTagIndex !== -1) {
            // 找到完整的标签对，移除它们及其内容
            processedText = processedText.substring(0, startTagIndex) +
                           processedText.substring(endTagIndex + 10); // 10是</details>的长度
            // 继续从当前位置查找下一个标签
            startIndex = startTagIndex;
          } else {
            // 只找到开始标签，没有找到结束标签
            // 保留开始标签之前的内容，标记我们在标签内
            processedText = processedText.substring(0, startTagIndex);
            this.inDetailsTag = true;
            continueProcessing = false;
          }
        } else {
          // 没有找到标签的结束>，可能是不完整的标签
          // 保留到目前为止的内容
          processedText = processedText.substring(0, startTagIndex);
          continueProcessing = false;
        }
      } else if (this.inDetailsTag) {
        // 我们在标签内，但没有找到新的开始标签
        // 查找结束标签
        const endTagIndex = processedText.indexOf('</details>');

        if (endTagIndex !== -1) {
          // 找到结束标签，保留结束标签之后的内容
          processedText = processedText.substring(endTagIndex + 10);
          this.inDetailsTag = false;
        } else {
          // 没有找到结束标签，整个文本都在标签内
          processedText = '';
        }
        continueProcessing = false;
      } else {
        // 没有找到任何标签，结束处理
        continueProcessing = false;
      }
    }

    return processedText;
  }

  // 实时播放文本（用于一边流式输出文本，一边进行文字转语音）
  public playRealtimeText(text: string): void {
    // 如果没有文本，则不处理
    if (!text || text.trim() === '') {
      return;
    }

    try {
      // 流式处理文本，过滤<think>和<details>标签
      // 将新文本添加到缓冲区
      this.textBuffer += text;

      // 处理<think>标签
      let processedText = this.processThinkTags();

      // 处理<details>标签
      processedText = this.processDetailsTags(processedText);
      console.log( '处理后的实时文本:', processedText);
      // 如果没有处理后的文本，直接返回
      if (!processedText) {
        return;
      }

      // 如果当前没有在播放，则初始化播放状态
      if (!this.state.isPlaying && !this.state.isBuffering && !this.state.isLoading) {
        // 重置所有状态
        this.audioQueue = [];
        this.audioBuffers = [];
        this.isProcessingAudio = false;
        this.playbackStarted = false;
        this.streamEnded = false;
        this.nextStartTime = 0;

        // 更新状态
        this.updateState({ isLoading: true, currentText: processedText });
        this.triggerEvent('start', { text: processedText });
      }

      // 发送流式文本到WebSocket
      console.log('发送流式文本到语音合成服务...');
      sendStreamTextToSpeech(processedText);
    } catch (error) {
      console.error('实时播放文本时出错:', error);
    }
  }

  // 结束实时播放
  public endRealtimePlay(): void {
    try {
      // 处理缓冲区中剩余的文本
      if (this.textBuffer && !this.inThinkTag && !this.inDetailsTag) {
        // 如果缓冲区中有文本，且不在标签内，发送它
        const processedText = this.processDetailsTags(this.processThinkTags());
        if (processedText) {
          sendStreamTextToSpeech(processedText);
        }
      }

      // 重置标签状态
      this.inThinkTag = false;
      this.inDetailsTag = false;
      this.textBuffer = '';

      // 发送流式文本结束信号
      console.log('发送流式文本结束信号...');
      sendStreamTextEnd();
    } catch (error) {
      console.error('结束实时播放时出错:', error);
    }
  }

  // 切换播放状态
  public togglePlayback(text: string): void {
    if (this.state.isPlaying || this.state.isBuffering) {
      this.stopPlayback();
    } else {
      this.startPlayback(text);
    }
  }
}

// 创建单例实例
const audioPlayer = new AudioPlayer();

export default audioPlayer;

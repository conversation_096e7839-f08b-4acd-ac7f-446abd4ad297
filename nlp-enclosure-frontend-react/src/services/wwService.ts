import http from '../utils/request.ts';
import { WWRegisterData } from '../types/wwConfigData.ts';
import * as ww from '@wecom/jssdk'

let wwRegisterData: WWRegisterData | null = null;

export const getWWRegisterData = (): WWRegisterData | null => {
  return wwRegisterData;
};

export const getCorpId = async (): Promise<string> => {
  try {
    const response = await http.get('./enclosure/ww/getCorpId');

    const corpId = response;
    console.log('corpId loaded:', corpId);

    return corpId;
  } catch (error) {
    console.error('Error fetching corpId:', error);
    return '';
  }
};

export const generateSignature = async (url: string): Promise<WWRegisterData | null> => {
  try {
    const response = await http.get('./enclosure/ww/generateSignature', {
      params: { url }
    });

    if (response.status.code !== '0') {
      return null;
    }

    wwRegisterData = response.payload;

    return response.payload;
  } catch (error) {
    console.error('Error fetching WWRegisterDat:', error);
    return null;
  }
};

export const tryRegisterWW = async () => {

  try {
    const corpId = await getCorpId();
    // await generateSignature('http://192.168.0.111:5173/');
    ww.register({
      corpId: corpId,
      jsApiList: ['startRecord', 'stopRecord', 'onVoiceRecordEnd', 'getLocalFileData', 'translateVoice'],
      getConfigSignature: async (url) => {
        const registerData = await generateSignature(url);
        if (registerData) {
          console.log('registerData', registerData);
          return {
            timestamp: registerData.timestamp,
            nonceStr: registerData.nonceStr,
            signature: registerData.signature,
          }
        }
        return null
      },
    })
  } catch (error) {
    console.error('Error registering WeWork:', error);
  }

}

import http, { getAuthToken } from '../utils/request';
import { AxiosResponse } from 'axios';

/**
 * 上传文件到Minio服务
 * 
 * @param file 要上传的文件
 * @returns 上传结果，包含文件URL等信息
 */
export const uploadToMinio = async (file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);
    
    // 获取认证令牌
    const token = getAuthToken();
    formData.append('token', token || '');

    try {
        const response: AxiosResponse<any> = await http.post('./enclosure/minio/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });

        // 检查响应状态
        if (response.status && response.status.code !== '0') {
            console.error('Minio upload failed:', response);
            return null;
        }

        return response.payload || response;
    } catch (error) {
        console.error('Minio upload error:', error);
        return null;
    }
};

/**
 * 获取Minio中的文件
 * 
 * @param objectName 文件对象名称
 * @returns 文件URL
 */
export const getMinioObjectUrl = async (objectName: string, expiry: number = 604800): Promise<string | null> => {
    try {
        const response: AxiosResponse<any> = await http.get(`./enclosure/minio/url/${objectName}`, {
            params: { expiry }
        });

        if (response && response.success) {
            return response.url;
        }
        
        return null;
    } catch (error) {
        console.error('Get Minio object URL error:', error);
        return null;
    }
};

/**
 * 删除Minio中的文件
 * 
 * @param objectName 文件对象名称
 * @returns 操作结果
 */
export const removeMinioObject = async (objectName: string): Promise<boolean> => {
    try {
        const response: AxiosResponse<any> = await http.delete(`./enclosure/minio/object/${objectName}`);
        
        return response && response.success;
    } catch (error) {
        console.error('Remove Minio object error:', error);
        return false;
    }
};

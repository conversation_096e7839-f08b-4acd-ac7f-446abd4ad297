import http from '../utils/request';
import { SuggestionSubmitRequest, SuggestionSubmitResponse } from '../types/suggestion';

/**
 * 提交优化建议
 * 
 * @param data 优化建议数据，包含标题和详细描述
 * @returns 提交结果
 */
export const submitSuggestion = async (data: SuggestionSubmitRequest): Promise<SuggestionSubmitResponse> => {
  try {
    const response = await http.post<SuggestionSubmitResponse>('./enclosure/suggestion/submit', data);
    return response;
  } catch (error) {
    console.error('提交优化建议时出错:', error);
    // 返回一个错误响应
    return {
      status: {
        code: '1', // 非0表示失败
        message: error instanceof Error ? error.message : '未知错误'
      }
    };
  }
};

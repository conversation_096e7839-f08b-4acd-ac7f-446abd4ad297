import http from "../utils/request.ts";
import { AgentParam } from '../types/agentDict.ts';

export const findAllAgent = (): Promise<any> => {
    return http.get('./enclosure/agentDict/findAll');
};

/**
 * 获取当前用户可见的所有AgentDict
 * @returns 当前用户可见的AgentDict列表
 */
export const findAllVisible = (): Promise<any> => {
    return http.get('./enclosure/agentDict/findAllVisible');
};

export const getAgentDictByIsDefaultTrue = (): Promise<any> => {
    return http.get('./enclosure/agentDict/getAgentDictByIsDefaultTrue');
};

export const findByIsDefaultFalse = (): Promise<any> => {
    return http.get('./enclosure/agentDict/findByIsDefaultFalse');
};

export const save = (agentDict: any): Promise<any> => {
    return http.post('./enclosure/agentDict/save', agentDict);
};

export const deleteAgentDict = (id: string): Promise<any> => {
    return http.get('./enclosure/agentDict/delete', { params: { id }});
};

/**
 * 更新AgentDict的可见性设置
 * @param id AgentDict ID
 * @param isPublic 是否公开可见
 * @param visibleUserIds 可见用户ID列表
 * @returns 更新结果
 */
export const updateVisibility = async (id: string, isPublic: boolean, visibleUserIds?: string[]): Promise<any> => {
  try {
    const response = await http.post('./enclosure/agentDict/updateVisibility',
      // 将visibleUserIds作为请求体传递
      visibleUserIds || [],
      {
        params: {
          id,
          isPublic
        }
      }
    );

    if (response.status.code !== '0') {
      return null;
    }

    return response.payload;
  } catch (error) {
    console.error('Error updating visibility:', error);
    return null;
  }
};

/**
 * 获取AgentDict的可见性设置
 * @param id AgentDict ID
 * @returns 可见性设置信息
 */
export const getVisibility = async (id: string): Promise<any> => {
    try {
      const response = await http.get('./enclosure/agentDict/getVisibility', {
        params: { id }
      });

      if (response.status.code !== '0') {
        return null;
      }

      return  response.payload;
    } catch (error) {
      console.error('Error fetching visibility:', error);
      return null;
    }
};

export const getWpsAgentParam = async (): Promise<AgentParam> => {
  try {
    const response = await http.get('./enclosure/agentDict/getWpsAgentParam');

    if (response.status.code !== '0') {
      return null;
    }

    return response.payload;
  } catch (error) {
    console.error('Get wps agent param error:', error);
  }
}

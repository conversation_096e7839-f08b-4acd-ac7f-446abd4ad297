import { getStoredChatVoiceUrl } from './configService';

// 存储活跃的WebSocket连接
let sttWebSocket: WebSocket | null = null;
let ttsWebSocket: WebSocket | null = null;

// 存储心跳定时器
let sttHeartbeatTimer: number | null = null;
let ttsHeartbeatTimer: number | null = null;

// 存储心跳超时定时器
let sttHeartbeatTimeoutTimer: number | null = null;
let ttsHeartbeatTimeoutTimer: number | null = null;

// 心跳间隔（毫秒）
// const HEARTBEAT_INTERVAL = 15000; // 15秒
const HEARTBEAT_TIMEOUT = 30000; // 30秒

// 存储回调函数
type STTCallback = (text: string) => void;
type TTSCallback = (audioChunk: Blob) => void;
type ErrorCallback = (error: string) => void;
type ConnectionCallback = () => void;
type TTSStoppedCallback = (message: string) => void;

let sttCallback: STTCallback | null = null;
let ttsCallback: TTSCallback | null = null;
let ttsEndCallback: (() => void) | null = null;
let ttsStoppedCallback: TTSStoppedCallback | null = null;
let errorCallback: ErrorCallback | null = null;
let sttConnectionCallback: ConnectionCallback | null = null;
let ttsConnectionCallback: ConnectionCallback | null = null;

/**
 * 初始化语音转文字WebSocket连接
 * @returns 是否成功初始化
 */
export const initSTTWebSocket = (): boolean => {
  try {
    // 如果已经存在连接，先关闭
    if (sttWebSocket) {
      closeSTTWebSocket();
    }

    // 获取ChatVoice服务URL
    // const chatVoiceUrl = getStoredChatVoiceUrl();
    // if (!chatVoiceUrl) {
    //   console.error('ChatVoice URL未配置');
    //   if (errorCallback) {
    //     errorCallback('ChatVoice URL未配置');
    //   }
    //   return false;
    // }

    // 确定WebSocket协议
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    // 从URL中提取主机部分
    // const urlObj = new URL(chatVoiceUrl);
    let host = getBaseUrlWithoutProtocol();
    // let host = '127.0.0.1:8000';
    console.log("host ---- ", host)
//     const host = '**************:32540';
    // 去掉最后的斜杠
    if (host.endsWith('/')) {
      host = host.substring(0, host.length - 1);
    }


    // 构建WebSocket URL
    const sttWsUrl = `${protocol}//${host}/ws/stt`;
    console.log(`正在连接STT WebSocket: ${sttWsUrl}`);

    // 创建WebSocket连接
    sttWebSocket = new WebSocket(sttWsUrl);

    // 设置事件处理程序
    sttWebSocket.onopen = () => {
      console.log('STT WebSocket连接已建立');
      // 启动心跳检测
      startSTTHeartbeat();
      if (sttConnectionCallback) {
        sttConnectionCallback();
      }
    };

    sttWebSocket.onmessage = (event) => {
      try {
        console.log('STT WebSocket收到消息:', event.data);
        const data = JSON.parse(event.data);

        // 处理心跳消息
        if (data.type === 'heartbeat') {
          console.debug('收到STT心跳消息:', data);
          // 发送心跳响应
          if (sttWebSocket) {
            sendHeartbeatPong(sttWebSocket);
          }
          // 重置心跳检测
          startSTTHeartbeat();
          return;
        }

        if (data.type === 'stt_result' && sttCallback) {
          sttCallback(data.text);
        } else if (data.type === 'error' && errorCallback) {
          errorCallback(data.message);
        }
      } catch (e) {
        console.error('处理STT WebSocket消息时出错:', e);
        if (errorCallback) {
          errorCallback('处理消息时出错');
        }
      }
    };

    sttWebSocket.onerror = (error) => {
      console.error('STT WebSocket错误:', error);
      if (errorCallback) {
        errorCallback('WebSocket连接错误');
      }
    };

    sttWebSocket.onclose = () => {
      console.log('STT WebSocket连接已关闭');
      sttWebSocket = null;

      // 清除心跳定时器
      if (sttHeartbeatTimer !== null) {
        window.clearTimeout(sttHeartbeatTimer);
        sttHeartbeatTimer = null;
      }

      if (sttHeartbeatTimeoutTimer !== null) {
        window.clearTimeout(sttHeartbeatTimeoutTimer);
        sttHeartbeatTimeoutTimer = null;
      }
    };

    return true;
  } catch (error) {
    console.error('初始化STT WebSocket时出错:', error);
    if (errorCallback) {
      errorCallback(`WebSocket初始化错误: ${error instanceof Error ? error.message : String(error)}`);
    }
    return false;
  }
};

/**
 * 初始化文字转语音WebSocket连接
 * @returns 是否成功初始化
 */
export const initTTSWebSocket = (): boolean => {
  try {
    // 如果已经存在连接，先关闭
    if (ttsWebSocket) {
      closeTTSWebSocket();
    }

    // 获取ChatVoice服务URL
    // const chatVoiceUrl = getStoredChatVoiceUrl();
    // if (!chatVoiceUrl) {
    //   console.error('ChatVoice URL未配置');
    //   if (errorCallback) {
    //     errorCallback('ChatVoice URL未配置');
    //   }
    //   return false;
    // }

    // 确定WebSocket协议
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    // 从URL中提取主机部分
    // const urlObj = new URL(chatVoiceUrl);
    // const host = urlObj.host;
//     const host = window.location.host;
    // TODO
//     const host = '*************:8000';
    let host = getBaseUrlWithoutProtocol();
    // let host = '127.0.0.1:8000';
    // 去掉最后的斜杠
    if (host.endsWith('/')) {
      host = host.substring(0, host.length - 1);
    }
//     const host = '**************:32540';


    // 构建WebSocket URL
    const ttsWsUrl = `${protocol}//${host}/ws/tts`;
    console.log(`正在连接TTS WebSocket: ${ttsWsUrl}`);

    // 创建WebSocket连接
    ttsWebSocket = new WebSocket(ttsWsUrl);

    // 设置事件处理程序
    ttsWebSocket.onopen = () => {
      console.log('TTS WebSocket连接已建立');
      // 启动心跳检测
      startTTSHeartbeat();
      if (ttsConnectionCallback) {
        ttsConnectionCallback();
      }
    };

    ttsWebSocket.onmessage = (event) => {
      try {
        console.log('TTS WebSocket收到消息:', event.data);
        // 检查是否是二进制数据
        if (event.data instanceof Blob) {
          console.log('TTS WebSocket收到音频数据');
          if (ttsCallback) {
            ttsCallback(event.data);
          }
        } else {
          // 尝试解析JSON消息
          const data = JSON.parse(event.data);
          console.log( 'TTS WebSocket收到JSON数据:', data);

          // 处理心跳消息
          if (data.type === 'heartbeat') {
            console.debug('收到TTS心跳消息:', data);
            // 发送心跳响应
            if (ttsWebSocket) {
              sendHeartbeatPong(ttsWebSocket);
            }
            // 重置心跳检测
            startTTSHeartbeat();
            return;
          }

          if (data.type === 'tts_end' && ttsEndCallback) {
            ttsEndCallback();
          } else if (data.type === 'tts_stopped' && ttsStoppedCallback) {
            ttsStoppedCallback(data.message);
          } else if (data.type === 'error' && errorCallback) {
            errorCallback(data.message);
          }
        }
      } catch (e) {
        console.error('处理TTS WebSocket消息时出错:', e);
        if (errorCallback) {
          errorCallback('处理消息时出错');
        }
      }
    };

    ttsWebSocket.onerror = (error) => {
      console.error('TTS WebSocket错误:', error);
      if (errorCallback) {
        errorCallback('WebSocket连接错误');
      }
    };

    ttsWebSocket.onclose = () => {
      console.log('TTS WebSocket连接已关闭');
      ttsWebSocket = null;

      // 清除心跳定时器
      if (ttsHeartbeatTimer !== null) {
        window.clearTimeout(ttsHeartbeatTimer);
        ttsHeartbeatTimer = null;
      }

      if (ttsHeartbeatTimeoutTimer !== null) {
        window.clearTimeout(ttsHeartbeatTimeoutTimer);
        ttsHeartbeatTimeoutTimer = null;
      }
    };

    return true;
  } catch (error) {
    console.error('初始化TTS WebSocket时出错:', error);
    if (errorCallback) {
      errorCallback(`WebSocket初始化错误: ${error instanceof Error ? error.message : String(error)}`);
    }
    return false;
  }
};

/**
 * 关闭语音转文字WebSocket连接
 */
export const closeSTTWebSocket = (): void => {
  // 清除心跳定时器
  if (sttHeartbeatTimer !== null) {
    window.clearTimeout(sttHeartbeatTimer);
    sttHeartbeatTimer = null;
  }

  if (sttHeartbeatTimeoutTimer !== null) {
    window.clearTimeout(sttHeartbeatTimeoutTimer);
    sttHeartbeatTimeoutTimer = null;
  }

  if (sttWebSocket) {
    sttWebSocket.close(1000, "强制关闭");
    sttWebSocket = null;
  }
};

/**
 * 关闭文字转语音WebSocket连接
 */
export const closeTTSWebSocket = (): void => {
  console.log(`关闭TTS WebSocket连接`);

  // 清除心跳定时器
  if (ttsHeartbeatTimer !== null) {
    window.clearTimeout(ttsHeartbeatTimer);
    ttsHeartbeatTimer = null;
  }

  if (ttsHeartbeatTimeoutTimer !== null) {
    window.clearTimeout(ttsHeartbeatTimeoutTimer);
    ttsHeartbeatTimeoutTimer = null;
  }

  if (ttsWebSocket) {
    ttsWebSocket.close(1000, "强制关闭");
    ttsWebSocket = null;
  }
};

/**
 * 发送音频数据到语音转文字WebSocket
 * @param audioData 音频数据
 * @returns 是否成功发送
 */
export const sendAudioData = (audioData: Blob | ArrayBuffer): boolean => {
  if (!sttWebSocket || sttWebSocket.readyState !== WebSocket.OPEN) {
    console.error('STT WebSocket未连接');
    if (errorCallback) {
      errorCallback('WebSocket未连接');
    }
    return false;
  }

  try {
    // 如果是Blob类型，记录类型信息
    if (audioData instanceof Blob) {
      console.log('发送音频数据，类型:', audioData.type, '大小:', audioData.size);
    } else {
      console.log('发送音频数据，大小:', audioData.byteLength);
    }

    sttWebSocket.send(audioData);
    return true;
  } catch (error) {
    console.error('发送音频数据时出错:', error);
    if (errorCallback) {
      errorCallback(`发送数据时出错: ${error instanceof Error ? error.message : String(error)}`);
    }
    return false;
  }
};

/**
 * 发送结束录音信号
 * @returns 是否成功发送
 */
export const sendEndRecording = (): boolean => {
  if (!sttWebSocket || sttWebSocket.readyState !== WebSocket.OPEN) {
    console.error('STT WebSocket未连接');
    if (errorCallback) {
      errorCallback('WebSocket未连接');
    }
    return false;
  }

  try {
    sttWebSocket.send('end_recording');
    return true;
  } catch (error) {
    console.error('发送结束录音信号时出错:', error);
    if (errorCallback) {
      errorCallback(`发送数据时出错: ${error instanceof Error ? error.message : String(error)}`);
    }
    return false;
  }
};

/**
 * 发送文本到文字转语音WebSocket
 * @param text 要转换为语音的文本
 * @returns 是否成功发送
 */
export const sendTextToSpeech = (text: string): boolean => {
  if (!ttsWebSocket || ttsWebSocket.readyState !== WebSocket.OPEN) {
    console.error('TTS WebSocket未连接');
    if (errorCallback) {
      errorCallback('WebSocket未连接');
    }
    return false;
  }

  try {
    // 确保以正确的格式发送文本
    const textCommand = JSON.stringify({ text });
    console.log('发送文本转语音请求:', textCommand);
    ttsWebSocket.send(textCommand);
    return true;
  } catch (error) {
    console.error('发送文本数据时出错:', error);
    if (errorCallback) {
      errorCallback(`发送数据时出错: ${error instanceof Error ? error.message : String(error)}`);
    }
    return false;
  }
};

/**
 * 发送停止语音生成指令
 * @returns 是否成功发送
 */
export const stopTextToSpeech = (): boolean => {
  if (!ttsWebSocket || ttsWebSocket.readyState !== WebSocket.OPEN) {
    console.error('TTS WebSocket未连接');
    if (errorCallback) {
      errorCallback('WebSocket未连接');
    }
    return false;
  }

  try {
    console.log('发送停止语音生成指令');
    // 确保以正确的格式发送停止指令
    const stopCommand = JSON.stringify({ type: 'stop' });
    console.log('停止指令内容:', stopCommand);
    ttsWebSocket.send(stopCommand);
    return true;
  } catch (error) {
    console.error('发送停止指令时出错:', error);
    if (errorCallback) {
      errorCallback(`发送停止指令时出错: ${error instanceof Error ? error.message : String(error)}`);
    }
    return false;
  }
};

/**
 * 发送流式文本到文字转语音WebSocket
 * @param textChunk 文本片段
 * @returns 是否成功发送
 */
export const sendStreamTextToSpeech = (textChunk: string): boolean => {
  if (!ttsWebSocket || ttsWebSocket.readyState !== WebSocket.OPEN) {
    console.error('TTS WebSocket未连接');
    if (errorCallback) {
      errorCallback('WebSocket未连接');
    }
    return false;
  }

  try {
    // 确保以正确的格式发送文本
    const streamTextCommand = JSON.stringify({ type: 'stream_text', text: textChunk });
    console.log('发送流式文本片段:', streamTextCommand);
    ttsWebSocket.send(streamTextCommand);
    return true;
  } catch (error) {
    console.error('发送流式文本数据时出错:', error);
    if (errorCallback) {
      errorCallback(`发送数据时出错: ${error instanceof Error ? error.message : String(error)}`);
    }
    return false;
  }
};

/**
 * 发送流式文本结束信号
 * @returns 是否成功发送
 */
export const sendStreamTextEnd = (): boolean => {
  if (!ttsWebSocket || ttsWebSocket.readyState !== WebSocket.OPEN) {
    console.error('TTS WebSocket未连接');
    if (errorCallback) {
      errorCallback('WebSocket未连接');
    }
    return false;
  }

  try {
    // 确保以正确的格式发送结束信号
    const streamEndCommand = JSON.stringify({ type: 'stream_end' });
    console.log('发送流式文本结束信号:', streamEndCommand);
    ttsWebSocket.send(streamEndCommand);
    return true;
  } catch (error) {
    console.error('发送流式文本结束信号时出错:', error);
    if (errorCallback) {
      errorCallback(`发送数据时出错: ${error instanceof Error ? error.message : String(error)}`);
    }
    return false;
  }
};

/**
 * 设置语音转文字回调函数
 * @param callback 回调函数
 */
export const setSTTCallback = (callback: STTCallback): void => {
  sttCallback = callback;
};

/**
 * 设置文字转语音回调函数
 * @param callback 回调函数
 */
export const setTTSCallback = (callback: TTSCallback): void => {
  ttsCallback = callback;
};

/**
 * 设置文字转语音结束回调函数
 * @param callback 回调函数
 */
export const setTTSEndCallback = (callback: () => void): void => {
  ttsEndCallback = callback;
};

/**
 * 设置文字转语音停止回调函数
 * @param callback 回调函数
 */
export const setTTSStoppedCallback = (callback: TTSStoppedCallback): void => {
  ttsStoppedCallback = callback;
};

/**
 * 设置错误回调函数
 * @param callback 回调函数
 */
export const setErrorCallback = (callback: ErrorCallback): void => {
  errorCallback = callback;
};

/**
 * 设置STT连接成功回调函数
 * @param callback 回调函数
 */
export const setSTTConnectionCallback = (callback: ConnectionCallback): void => {
  sttConnectionCallback = callback;
};

/**
 * 设置TTS连接成功回调函数
 * @param callback 回调函数
 */
export const setTTSConnectionCallback = (callback: ConnectionCallback): void => {
  ttsConnectionCallback = callback;
};

/**
 * 发送心跳响应
 * @param websocket WebSocket连接
 * @returns 是否成功发送
 */
const sendHeartbeatPong = (websocket: WebSocket): boolean => {
  if (!websocket || websocket.readyState !== WebSocket.OPEN) {
    console.error('WebSocket未连接，无法发送心跳响应');
    return false;
  }

  try {
    websocket.send('heartbeat_pong');
    console.debug('已发送心跳响应');
    return true;
  } catch (error) {
    console.error('发送心跳响应时出错:', error);
    return false;
  }
};

/**
 * 启动STT心跳检测
 */
const startSTTHeartbeat = (): void => {
  // 清除现有的定时器
  if (sttHeartbeatTimer !== null) {
    window.clearTimeout(sttHeartbeatTimer);
    sttHeartbeatTimer = null;
  }

  if (sttHeartbeatTimeoutTimer !== null) {
    window.clearTimeout(sttHeartbeatTimeoutTimer);
    sttHeartbeatTimeoutTimer = null;
  }

  // 设置心跳超时检测
  sttHeartbeatTimeoutTimer = window.setTimeout(() => {
    console.warn('STT WebSocket心跳超时，尝试重连');
    // 关闭现有连接
    closeSTTWebSocket();
    // 尝试重新连接
    initSTTWebSocket();
  }, HEARTBEAT_TIMEOUT);
};

/**
 * 启动TTS心跳检测
 */
const startTTSHeartbeat = (): void => {
  // 清除现有的定时器
  if (ttsHeartbeatTimer !== null) {
    window.clearTimeout(ttsHeartbeatTimer);
    ttsHeartbeatTimer = null;
  }

  if (ttsHeartbeatTimeoutTimer !== null) {
    window.clearTimeout(ttsHeartbeatTimeoutTimer);
    ttsHeartbeatTimeoutTimer = null;
  }

  // 设置心跳超时检测
  ttsHeartbeatTimeoutTimer = window.setTimeout(() => {
    console.warn('TTS WebSocket心跳超时，尝试重连');
    // 关闭现有连接
    closeTTSWebSocket();
    // 尝试重新连接
    initTTSWebSocket();
  }, HEARTBEAT_TIMEOUT);
};

/**
* 获取当前URL中index.html前的地址，并去除协议
* @returns 不含协议的基础URL
*/
export const getBaseUrlWithoutProtocol = (hasProtocol = false): string => {
    try {
        // 获取当前URL
        const currentUrl = window.location.href;

        // 创建URL对象
        const urlObj = new URL(currentUrl);

        // 获取主机名和端口
        const hostWithPort = urlObj.host; // 包含主机名和端口(如果有)

        // 获取路径名
        let pathname = urlObj.pathname;

        // 如果路径以index.html结尾，则去除index.html
        if (pathname.endsWith('/index.html')) {
            pathname = pathname.substring(0, pathname.length - 11); // 11是'/index.html'的长度
        } else if (pathname.includes('/index.html')) {
            // 如果index.html在路径中间
            pathname = pathname.split('/index.html')[0];
        }

        // 组合主机名、端口和路径，不包含协议
        if (hasProtocol) {
          return window.location.protocol + '//' + hostWithPort + pathname;
        } else {
          return hostWithPort + pathname;
        }
    } catch (error) {
        console.error('获取基础URL时出错:', error);
        return window.location.host; // 出错时返回当前主机名和端口
    }
};

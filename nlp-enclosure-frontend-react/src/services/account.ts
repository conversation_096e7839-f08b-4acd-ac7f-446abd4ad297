import http from "../utils/request.ts";
import { Account } from "../types/account.ts";

/**
 * 获取所有用户列表
 * @returns 用户列表
 */
export const getUserList = async (): Promise<Account[]> => {
    try {
      const response = await http.get('./enclosure/user/list');

      if (response.status.code !== '0') {
        return [];
      }

      return response.payload.data;
    } catch (error) {
      console.error('Error fetching user list:', error);
      return [];
    }
};

/**
 * 搜索用户
 * @param keyword 搜索关键词
 * @returns 匹配的用户列表
 */
export const searchUsers = async (keyword: string): Promise<Account[]> => {
  try {
    const response = await  http.get('./enclosure/user/search', {
      params: { keyword }
    });

    if (response.status.code !== '0') {
      return [];
    }

    return response.payload.data;
  } catch (error) {
    console.error('Error searching users:', error);
    return [];
  }
};

import { AgentDict } from '../types/agentDict';
import { AgentParam } from '../types/agentParam';
import { getUserInfoFromLocalStorage } from '../services/user';

// 只保留必要的类型定义
export interface SuperSonicConfig {
  agentId: string;
  serverUrl: string;
  apiKey: string;
}

// 创建默认的 SuperSonic 智能体配置
export const createDefaultSuperSonicAgent = (): AgentDict => {
  // 尝试使用当前系统用户信息
  const userInfo = getUserInfoFromLocalStorage();

  return {
    id: 'supersonic-default',
    name: 'SuperSonic 助手',
    description: '智能对话助手',
    type: 'supersonic',
    apiKey: 'your-default-api-key',
    superSonicServerHost: 'localhost:9080',
    superSonicAgentId: 'default-agent',
    iconColor: '#1677ff',
    fontColor: '#ffffff',
    isDefault: false,
    icon: '🚀', // 添加默认图标
    username: userInfo ? userInfo.workNo : 'admin', // 统一使用系统工号作为用户名
    password: userInfo ? userInfo.workNo : '123456' // 统一使用系统工号作为密码
  };
};

// 判断是否是 SuperSonic 类型的智能体
export const isSuperSonicAgent = (agent: AgentDict): boolean => {
  return agent.type === 'supersonic';
};

// 创建 SuperSonic 智能体的默认参数
export const createSuperSonicAgentParam = (): AgentParam => {
  return {
    opening_statement: '您好，我是 SuperSonic 智能助手，有什么可以帮您解答的问题吗？',
    suggested_questions: [
      '什么是 SuperSonic？',
      '如何使用 SuperSonic 创建知识库？',
      '如何连接自定义大语言模型？'
    ],
    suggested_questions_after_answer: {
      enabled: true
    },
    speech_to_text: {
      enabled: false
    },
    text_to_speech: {
      enabled: false,
      voice: 'default',
      language: 'zh-CN'
    },
    retriever_resource: {
      enabled: false
    },
    annotation_reply: {
      enabled: false
    },
    more_like_this: {
      enabled: false
    },
    user_input_form: [],
    sensitive_word_avoidance: {
      enabled: false,
      type: 'default',
      configs: []
    },
    file_upload: {
      image: {
        detail: 'default',
        enabled: false,
        number_limits: 5,
        transfer_methods: ['local_file']
      },
      enabled: false,
      allowed_file_types: ['image', 'document'],
      allowed_file_extensions: [],
      allowed_file_upload_methods: ['local_file'],
      number_limits: 5
    },
    system_parameters: {
      image_file_size_limit: 10,
      video_file_size_limit: 100,
      audio_file_size_limit: 10,
      file_size_limit: 50,
      workflow_file_upload_limit: 5
    }
  };
};

// SuperSonic 流式聊天函数 - 仅作为占位符，实际使用 Chat SDK 替代
export const superSonicStreamChat = (_query: string, _agentId: string, _conversationId?: string): ReadableStream<any> => {
  // 创建一个空的 ReadableStream 作为占位符
  // 实际上，我们将使用 Chat SDK 替代这个函数
  return new ReadableStream({
    start(controller) {
      controller.close();
    }
  });
};

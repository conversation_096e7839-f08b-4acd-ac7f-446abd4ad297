import http from '../utils/request';
import {UserAgentList} from "../types/userAgentList.ts";

// 1. 获取 userAgent 列表
export const getUserAgentList = async (): Promise<UserAgentList[]> => {
    try {
        const response = await http.get('./enclosure/userAgentList/list');
        console.log("getUserAgentList response", response)

        if (response.status.code !== '0') {
            return [];
        }

        return response.payload;
    } catch (error) {
        console.error('Error fetching user agent list:', error);
        throw error;
    }
};

// 2. 删除 userAgent
export const deleteUserAgent = async (id: string): Promise<void> => {
    try {
        await http.get('./enclosure/userAgentList/delete', {
            params: { id }
        });
    } catch (error) {
        console.error('Error deleting user agent:', error);
        throw error;
    }
};

// 3. 调整 userAgent 的排序
// export const updateUserAgentOrder = async (order: number, id: string): Promise<void> => {
//     try {
//         await http.get('./userAgentList/updateOrder', { params: { order, id } });
//     } catch (error) {
//         console.error('Error updating user agent order:', error);
//         throw error;
//     }
// };

export const setTopUserAgent = async (id: string, isTop: boolean): Promise<void> => {
    try {
        await http.get('./enclosure/userAgentList/top', { params: { isTop, id } });
    } catch (error) {
        console.error('Error setting top user agent:', error);
        throw error;
    }
};

// 4. 新增 userAgent
export const addUserAgent = async (userAgentList: any): Promise<void> => {
    try {
        await http.post('./enclosure/userAgentList/add', userAgentList);
    } catch (error) {
        console.error('Error adding user agent:', error);
        throw error;
    }
};

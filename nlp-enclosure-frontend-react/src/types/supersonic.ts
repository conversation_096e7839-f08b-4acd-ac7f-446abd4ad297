/**
 * SuperSonic 服务相关类型定义
 */

// SuperSonic 模型类型
export enum SuperSonicModelType {
    TEXT = 'text',
    CHAT = 'chat',
    EMBEDDING = 'embedding',
    IMAGE = 'image',
}

// SuperSonic 模型信息
export interface SuperSonicModel {
    id: string;
    name: string;
    type: SuperSonicModelType;
    description: string;
    capabilities: string[];
    contextWindow: number;
    maxTokens: number;
}

// SuperSonic 问答助理配置
export interface SuperSonicAssistant {
    id: string;
    name: string;
    description: string;
    model: string;
    instructions: string;
    tools: SuperSonicTool[];
}

// SuperSonic 工具类型
export interface SuperSonicTool {
    id: string;
    name: string;
    description: string;
    type: string;
}

// SuperSonic 问答请求
export interface SuperSonicChatRequest {
    model: string;
    messages: SuperSonicMessage[];
    temperature?: number;
    top_p?: number;
    max_tokens?: number;
    stream?: boolean;
}

// SuperSonic 消息
export interface SuperSonicMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

// SuperSonic 问答响应
export interface SuperSonicChatResponse {
    id: string;
    model: string;
    choices: {
        index: number;
        message: SuperSonicMessage;
        finish_reason: string;
    }[];
    usage: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}

// SuperSonic 内置 DEMO 数据
export interface SuperSonicDemoData {
    models: SuperSonicModel[];
    assistants: SuperSonicAssistant[];
    knowledgeBase: {
        id: string;
        name: string;
        description: string;
        documents: number;
    }[];
}

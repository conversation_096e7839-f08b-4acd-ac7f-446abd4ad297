import {AgentDict} from "./agentDict.ts";

export interface ChatHistory {
    /**
     * 主键ID（UUID格式）
     */
    id: string;

    /**
     * 智能体的ID（关联智能体字典表）
     */
    agentDictId: string;

    /**
     * Dify系统的聊天记录唯一标识
     */
    conversationId: string;

    /**
     * 对话名称（用户自定义的对话标题）
     */
    name: string;

    /**
     * 是否置顶标记（true表示置顶显示）
     */
    isTop: boolean;

    /**
     * 逻辑删除标记（true表示已删除）
     */
    isDeleted: boolean;

    /**
     * 创建人标识（UUID格式，通常为用户ID）
     */
    createBy: string;

    /**
     * 创建时间戳
     */
    createAt: Date;

    agentDict?: AgentDict;
}

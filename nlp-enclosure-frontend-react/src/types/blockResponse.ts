export interface BlockResponse {
    /**
     * 不同模式下的事件类型.
     */
    event: string;

    /**
     * 消息唯一 ID.
     */
    message_id: string;

    /**
     * 任务ID.
     */
    task_id: string;

    /**
     * agent_thought id.
     */
    id: string;

    /**
     * 会话 ID.
     */
    conversation_id: string;

    /**
     * App 模式，固定为 chat.
     */
    mode: string;

    /**
     * 完整回复内容.
     */
    answer: string;

    /**
     * 元数据.
     */
    metadata: Record<string, Record<string, string>>;

    /**
     * 创建时间戳.
     */
    created_at: number;
}

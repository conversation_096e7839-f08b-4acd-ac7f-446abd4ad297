import {AgentDict} from "./agentDict.ts";

export interface UserAgentList {
    /**
     * 主键
     */
    id?: string;

    /**
     * 智能体的ID
     */
    agentDictId?: string;

    /**
     * 排序
     */
    // sortOrder?: number;

    /**
     * 是否置顶
     */
    isTop?: boolean;

    /**
     * 用户ID
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createAt?: Date;

    agentDict?: AgentDict;
}

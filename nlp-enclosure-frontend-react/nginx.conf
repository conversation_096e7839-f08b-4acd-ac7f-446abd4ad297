server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;
    gzip_proxied any;

    # 缓存配置
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 7d;
        add_header Cache-Control "public, no-transform";
    }

    # 处理React路由
    location / {
        try_files $uri $uri/ /index.html;
    }


    location /api/ {
        client_max_body_size 100M;
        if ($request_uri ~* /api/(.*)$) {
            set $new $1;
            proxy_pass http://*************:9080/api/$new;
        }
    }


    location /enclosure-icons/ {
        client_max_body_size 100M;
        if ($request_uri ~* /enclosure-icons/(.*)$) {
            set $new $1;
            proxy_pass http://**************:31829/enclosure-icons/$new;
        }
    }

    location ~* ^/ws/(tts|stt)$ {
        client_max_body_size 100M;
        proxy_pass http://*************:8000$request_uri;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
    }
}

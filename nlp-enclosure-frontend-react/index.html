<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>广州烟草AI门户</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
<!--<script src="https://cdn.bootcss.com/vConsole/3.3.4/vconsole.min.js"></script>-->
<!--<script>-->
<!--  new VConsole();-->
<!--</script>-->
<script>
  (function() {
    // 处理微信浏览器的字体大小设置
    if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
      handleFontSize();
    } else {
      document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
    }
    function handleFontSize() {
      // 设置网页字体为默认大小
      WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize' : 0 });
      // 重写设置网页字体大小的事件
      WeixinJSBridge.on('menu:setfont', function() {
        WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize' : 0 });
        resetAllElements();
      });
    }

    // 处理系统字体大小变化
    function resetFontSize() {
      // 强制设置body的字体大小为16px
      document.documentElement.style.fontSize = '16px';
      document.body.style.fontSize = '16px';

      // 防止iOS设备上的字体大小自动调整
      document.body.style.webkitTextSizeAdjust = 'none';
      document.body.style.textSizeAdjust = 'none';
      document.body.style.msTextSizeAdjust = 'none';
      document.body.style.mozTextSizeAdjust = 'none';

    }

    // 重置所有元素的字体大小
    function resetAllElements() {
      // 处理所有元素
      var elements = document.querySelectorAll('*');
      for (var i = 0; i < elements.length; i++) {
        resetElementStyle(elements[i]);
      }

      // 特别处理Portal元素
      handlePortalElements();
    }

    // 重置单个元素的样式
    function resetElementStyle(el) {
      if (!el || !(el instanceof HTMLElement)) return;

      var tagName = el.tagName.toLowerCase();

      // 设置默认字体大小
      if (tagName === 'html' || tagName === 'body') {
        el.style.fontSize = '16px';
      } else if (tagName === 'h1') {
        el.style.fontSize = '24px';
      } else if (tagName === 'h2') {
        el.style.fontSize = '22px';
      } else if (tagName === 'h3') {
        el.style.fontSize = '20px';
      } else if (tagName === 'h4') {
        el.style.fontSize = '18px';
      } else if (tagName === 'h5') {
        el.style.fontSize = '16px';
      } else if (tagName === 'h6') {
        el.style.fontSize = '14px';
      } else {
        el.style.fontSize = '16px';
      }

      // 禁用文本大小调整
      el.style.webkitTextSizeAdjust = 'none';
      el.style.textSizeAdjust = 'none';
      el.style.msTextSizeAdjust = 'none';
      el.style.mozTextSizeAdjust = 'none';

      // 添加额外的防护措施
      el.style.maxHeight = '100000px';
      el.style.transform = 'none';
    }

    // 处理Portal元素
    function handlePortalElements() {
      // 查找所有可能的Portal容器
      var portalSelectors = [
        'body > div.ant-dropdown',
        'body > div.ant-select-dropdown',
        'body > div.ant-popover',
        'body > div[class*="ant-"]',
        '.ant-dropdown-wrap',
        '.ant-dropdown-container',
        '.ant-select-dropdown-container'
      ];

      // 处理每个选择器
      for (var i = 0; i < portalSelectors.length; i++) {
        var containers = document.querySelectorAll(portalSelectors[i]);

        // 处理每个容器
        for (var j = 0; j < containers.length; j++) {
          var container = containers[j];

          // 设置容器本身
          resetElementStyle(container);

          // 设置容器内的所有元素
          var allElements = container.querySelectorAll('*');
          for (var k = 0; k < allElements.length; k++) {
            resetElementStyle(allElements[k]);
          }
        }
      }
    }

    // 页面加载时设置
    resetFontSize();

    // 监听窗口大小变化，重新设置字体大小
    window.addEventListener('resize', resetFontSize);

    // 对于iOS设备，监听orientationchange事件
    window.addEventListener('orientationchange', resetFontSize);
  })();
</script>

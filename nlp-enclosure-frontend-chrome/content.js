// 监听来自popup或background的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  // 处理捕获内容的请求
  if (message.action === 'captureContent') {
    console.log('收到捕获内容请求');
    capturePageContent()
      .then(content => {
        console.log('捕获内容成功，长度:', content.length);
        sendResponse({ success: true, content });
      })
      .catch(error => {
        console.error('捕获内容失败:', error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // 表示将异步发送响应
  }

  // 处理将内容复制到剪贴板的请求
  if (message.action === 'copyToClipboard') {
    console.log('收到复制到剪贴板请求');
    copyToClipboard(message.content)
      .then(() => {
        console.log('内容已复制到剪贴板');
        sendResponse({ success: true });
      })
      .catch(error => {
        console.error('复制到剪贴板失败:', error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // 表示将异步发送响应
  }

  // 处理解析页面内容的请求
  if (message.action === 'parsePageContent') {
    console.log('收到解析页面内容请求');
    parsePageContent()
      .then(content => {
        console.log('解析页面内容成功，长度:', content.length);
        sendResponse({ success: true, content });
      })
      .catch(error => {
        console.error('解析页面内容失败:', error);
        sendResponse({ success: false, error: error.message });
      });
    return true; // 表示将异步发送响应
  }

  // 处理来自background.js的消息，这些消息可能是给AI门户页面的
  if (message.type === 'pageContent' || message.type === 'activeTabInfo' || message.type === 'capturedContent') {
    console.log('收到转发给AI门户的消息:', message.type);
    // 如果当前页面是AI门户页面，则将消息转发给页面
    window.postMessage(message, '*');
    sendResponse({ success: true });
    return true;
  }
});

// 捕获页面内容并转换为Markdown
async function capturePageContent() {
  try {
    // 获取页面HTML
    //const html = document.documentElement.outerHTML;
    let html = document.documentElement.outerHTML;
    
    // 获取所有iframe的内容
    const iframes = document.querySelectorAll('iframe');
    for (const iframe of iframes) {
      try {
        // 检查是否可以访问iframe内容（同源策略）
        if (iframe.contentDocument) {
          const iframeContent = iframe.contentDocument.documentElement.outerHTML;
          // 替换iframe标签为实际内容
          html = html.replace(iframe.outerHTML, iframeContent);
        }
      } catch (e) {
        console.log('无法访问iframe内容（可能是跨域限制）:', e);
      }
    }
    console.log('获取页面HTML成功，长度:', html);
    console.log('捕获页面内容成功，长度:', html.length);

    // 将HTML转换为Markdown
    const markdown = await htmlToMarkdown(html);
    console.log('转换为Markdown成功，内容:', markdown);

    return markdown;
  } catch (error) {
    console.error('捕获页面内容失败:', error);
    throw error;
  }
}

// 解析页面内容
async function parsePageContent() {
  try {
    // 获取页面的主要内容
    const mainContent = extractMainContent();

    // 将主要内容转换为Markdown
    const markdown = await htmlToMarkdown(mainContent);

    return markdown;
  } catch (error) {
    console.error('解析页面内容失败:', error);
    throw error;
  }
}

// 提取页面的主要内容
function extractMainContent() {
  // 创建一个新的HTML元素来存放主要内容
  const mainContentElement = document.createElement('div');

  // 尝试不同的选择器来找到主要内容
  const selectors = [
    'article',
    'main',
    '.article',
    '.post',
    '.content',
    '.main-content',
    '#content',
    '#main',
    '.article-content',
    '.post-content'
  ];

  // 尝试每个选择器
  for (const selector of selectors) {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
      // 找到了匹配的元素，复制其内容
      elements.forEach(element => {
        mainContentElement.appendChild(element.cloneNode(true));
      });
      return mainContentElement.innerHTML;
    }
  }

  // 如果没有找到主要内容，尝试使用启发式算法
  const paragraphs = document.querySelectorAll('p');
  if (paragraphs.length > 0) {
    // 找到段落最多的容器
    const containers = {};
    paragraphs.forEach(p => {
      let parent = p.parentElement;
      while (parent && parent !== document.body) {
        containers[parent] = (containers[parent] || 0) + 1;
        parent = parent.parentElement;
      }
    });

    // 找到包含段落最多的容器
    let bestContainer = null;
    let maxParagraphs = 0;
    for (const container in containers) {
      if (containers[container] > maxParagraphs) {
        maxParagraphs = containers[container];
        bestContainer = container;
      }
    }

    if (bestContainer) {
      return bestContainer.innerHTML;
    }
  }

  // 如果上述方法都失败，返回整个正文
  return document.body.innerHTML;
}

// 将内容复制到剪贴板
async function copyToClipboard(text) {
  try {
    // 使用 Clipboard API 复制内容
    await navigator.clipboard.writeText(text);

    // 显示复制成功的通知
    showNotification('内容已复制到剪贴板');

    return true;
  } catch (error) {
    console.error('复制到剪贴板失败:', error);

    // 尝试使用传统方法
    try {
      // 创建一个临时文本区域
      const textArea = document.createElement('textarea');
      textArea.value = text;

      // 将文本区域添加到文档中
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);

      // 选中文本并复制
      textArea.focus();
      textArea.select();
      document.execCommand('copy');

      // 移除文本区域
      document.body.removeChild(textArea);

      // 显示复制成功的通知
      showNotification('内容已复制到剪贴板');

      return true;
    } catch (fallbackError) {
      console.error('使用备用方法复制失败:', fallbackError);
      throw new Error('复制到剪贴板失败');
    }
  }
}

// 显示通知
function showNotification(message) {
  // 创建通知元素
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.position = 'fixed';
  notification.style.top = '20px';
  notification.style.right = '20px';
  notification.style.backgroundColor = '#4CAF50';
  notification.style.color = 'white';
  notification.style.padding = '10px 20px';
  notification.style.borderRadius = '4px';
  notification.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
  notification.style.zIndex = '10000';
  notification.style.transition = 'opacity 0.5s';

  // 添加到文档
  document.body.appendChild(notification);

  // 3秒后消失
  setTimeout(() => {
    notification.style.opacity = '0';
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 500);
  }, 3000);
}

// 将HTML转换为Markdown
async function htmlToMarkdown(html) {
  try {
    // 创建一个新的DOMParser
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // 移除脚本和样式标签
    const scripts = doc.querySelectorAll('script, style, noscript');
    scripts.forEach(script => script.remove());

    // 获取标题
    const title = doc.title;
    let markdown = `# ${title}\n\n`;

    // 获取主要内容
    const mainContent = doc.body;

    // 处理标题
    const headings = mainContent.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
      const level = parseInt(heading.tagName.substring(1));
      const text = heading.textContent.trim();
      if (text) {
        heading.outerHTML = `\n${'#'.repeat(level)} ${text}\n\n`;
      }
    });

    // 处理段落
    const paragraphs = mainContent.querySelectorAll('p');
    paragraphs.forEach(paragraph => {
      const text = paragraph.textContent.trim();
      if (text) {
        paragraph.outerHTML = `\n${text}\n\n`;
      }
    });

    // 处理链接
    const links = mainContent.querySelectorAll('a');
    links.forEach(link => {
      const text = link.textContent.trim();
      const href = link.getAttribute('href');
      if (text && href) {
        link.outerHTML = `[${text}](${href})`;
      }
    });

    // 处理图片，这里根据市局实际情况还是直接扔掉图片不做处理
    const images = mainContent.querySelectorAll('img');
    images.forEach(image => {
      const src = image.getAttribute('src');
      const alt = image.getAttribute('alt') || '';
      if (src) {
        image.outerHTML =``;// `![${alt}](${src})`;
      }
    });

    // 处理列表
    const lists = mainContent.querySelectorAll('ul, ol');
    lists.forEach(list => {
      const isOrdered = list.tagName.toLowerCase() === 'ol';
      const items = list.querySelectorAll('li');
      let listMarkdown = '\n';

      items.forEach((item, index) => {
        const text = item.textContent.trim();
        if (text) {
          listMarkdown += isOrdered ? `${index + 1}. ${text}\n` : `- ${text}\n`;
        }
      });

      list.outerHTML = `${listMarkdown}\n`;
    });

    // 处理表格
    const tables = mainContent.querySelectorAll('table');
    tables.forEach(table => {
      let tableMarkdown = '\n';

      // 处理表头
      const headerRows = table.querySelectorAll('thead tr');
      if (headerRows.length > 0) {
        const headerCells = headerRows[0].querySelectorAll('th');
        if (headerCells.length > 0) {
          tableMarkdown += '| ';
          headerCells.forEach(cell => {
            tableMarkdown += `${cell.textContent.trim()} | `;
          });
          tableMarkdown += '\n| ';
          headerCells.forEach(() => {
            tableMarkdown += '--- | ';
          });
          tableMarkdown += '\n';
        }
      }

      // 处理表格内容
      const rows = table.querySelectorAll('tbody tr');
      rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 0) {
          tableMarkdown += '| ';
          cells.forEach(cell => {
            tableMarkdown += `${cell.textContent.trim()} | `;
          });
          tableMarkdown += '\n';
        }
      });

      table.outerHTML = `${tableMarkdown}\n`;
    });

    // 处理代码块
    const codeBlocks = mainContent.querySelectorAll('pre, code');
    codeBlocks.forEach(codeBlock => {
      const text = codeBlock.textContent.trim();
      if (text) {
        codeBlock.outerHTML = codeBlock.tagName.toLowerCase() === 'pre' ?
          `\n\`\`\`\n${text}\n\`\`\`\n\n` :
          `\`${text}\``;
      }
    });

    // 处理引用
    const blockquotes = mainContent.querySelectorAll('blockquote');
    blockquotes.forEach(blockquote => {
      const text = blockquote.textContent.trim();
      if (text) {
        // 将文本分成多行，每行前面添加 >
        const lines = text.split('\n');
        let quoteMarkdown = '\n';
        lines.forEach(line => {
          quoteMarkdown += `> ${line}\n`;
        });
        blockquote.outerHTML = `${quoteMarkdown}\n`;
      }
    });

    // 获取处理后的内容
    let processedContent = mainContent.textContent
      .replace(/\n{3,}/g, '\n\n') // 删除多余的空行
      .trim();

    // 处理每一行，删除只包含空格的行和行首行尾的空格
    processedContent = processedContent
      .split('\n')
      .map(line => line.trim()) // 去除行首和行尾的空格
      .filter(line => line.length > 0) // 过滤掉空行和只包含空格的行
      .join('\n');

    // 删除连续的空行，只保留一个空行
    processedContent = processedContent.replace(/\n{3,}/g, '\n\n');

    // 确保段落之间有适当的空行
    processedContent = processedContent
      .replace(/([.!?])\s*\n([A-Z])/g, '$1\n\n$2') // 在句子结束符号后面和大写字母之间添加空行
      .replace(/(\w)\n(\w)/g, '$1 $2'); // 将同一段落内的换行替换为空格

    markdown += processedContent;

    return markdown;
  } catch (error) {
    console.error('HTML转Markdown失败:', error);
    // 如果转换失败，返回原始HTML
    return `转换失败，原始HTML内容：\n\n${html}`;
  }
}

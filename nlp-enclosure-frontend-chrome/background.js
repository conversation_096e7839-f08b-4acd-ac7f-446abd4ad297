// 导入配置
import { DEFAULT_AI_PORTAL_URL, addTimestampToUrl } from './src/config.js';

// 监听扩展安装或更新事件
chrome.runtime.onInstalled.addListener(async () => {
  // 设置侧边栏行为，点击扩展图标时打开侧边栏
  await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });

  // 创建右键菜单
  createContextMenus();
});

// 监听标签页更新事件
chrome.tabs.onUpdated.addListener(async (tabId, info, tab) => {
  if (!tab.url) return;

  // 为每个标签页启用侧边栏
  await chrome.sidePanel.setOptions({
    tabId,
    path: 'side_panel.html',
    width:400,//默认宽度
    enabled: true
  });
});

// 创建右键菜单
function createContextMenus() {
  // 创建主菜单
  chrome.contextMenus.create({
    id: "mainMenu",
    title: "AI门户",
    contexts: ["all"]
  });

  // 创建子菜单项
  chrome.contextMenus.create({
    id: "getPageContent",
    title: "获取页面内容",
    contexts: ["all"],
    parentId: "mainMenu"
  });

  chrome.contextMenus.create({
    id: "parsePageContent",
    title: "解析页面内容",
    contexts: ["all"],
    parentId: "mainMenu"
  });
}

// 监听右键菜单点击事件
chrome.contextMenus.onClicked.addListener((info, _tab) => {
  // 确保使用当前活动的标签页
  chrome.tabs.query({ active: true, currentWindow: true }, async (tabs) => {
    if (tabs.length === 0) {
      console.error('无法获取当前标签页');
      return;
    }

    const activeTab = tabs[0];

    if (info.menuItemId === "getPageContent") {
      // 向content script发送消息，请求获取页面内容
      console.log("开始获取页面内容，当前标签页:", activeTab.title, activeTab.url);
      chrome.tabs.sendMessage(activeTab.id, { action: 'captureContent' }, async (response) => {
        if (chrome.runtime.lastError) {
          console.error('发送消息错误:', chrome.runtime.lastError);
          return;
        }
        console.log("获取页面内容成功", response);

        if (response && response.success) {
          // 将内容写入剪贴板
          chrome.tabs.sendMessage(activeTab.id, {
            action: 'copyToClipboard',
            content: response.content
          });

          // 打开侧边栏
          await chrome.sidePanel.open({ tabId: activeTab.id });

          // 向侧边栏发送内容
          setTimeout(() => {
            chrome.runtime.sendMessage({
              action: 'sendContentToSidePanel',
              pageUrl: activeTab.url,
              pageTitle: activeTab.title,
              content: response.content
            });
          }, 500); // 给侧边栏一些时间加载
        }
      });
    } else if (info.menuItemId === "parsePageContent") {
      // 向content script发送消息，请求解析页面内容
      console.log("开始解析页面内容，当前标签页:", activeTab.title, activeTab.url);
      chrome.tabs.sendMessage(activeTab.id, { action: 'parsePageContent' }, async (response) => {
        if (chrome.runtime.lastError) {
          console.error('发送消息错误:', chrome.runtime.lastError);
          return;
        }

        if (response && response.success) {
          // 将内容写入剪贴板
          chrome.tabs.sendMessage(activeTab.id, {
            action: 'copyToClipboard',
            content: response.content
          });

          // 打开侧边栏
          await chrome.sidePanel.open({ tabId: activeTab.id });

          // 向侧边栏发送内容
          setTimeout(() => {
            chrome.runtime.sendMessage({
              action: 'sendContentToSidePanel',
              pageUrl: activeTab.url,
              pageTitle: activeTab.title,
              content: response.content
            });
          }, 500); // 给侧边栏一些时间加载
        }
      });
    }
  });
});

// 监听来自popup或content script的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  // 处理获取当前标签页信息的请求
  if (message.action === 'getActiveTabInfo') {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs.length > 0) {
        const activeTab = tabs[0];
        sendResponse({
          success: true,
          tabId: activeTab.id,
          url: activeTab.url,
          title: activeTab.title
        });
      } else {
        sendResponse({ success: false, error: '无法获取当前标签页' });
      }
    });
    return true; // 表示将异步发送响应
  }

  // 处理获取页面内容的请求
  if (message.action === 'captureContent') {
    // 始终使用当前活动标签页，而不是使用之前的tabId
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs.length === 0) {
        sendResponse({ success: false, error: '无法获取当前标签页' });
        return;
      }

      const activeTab = tabs[0];
      const tabId = activeTab.id;

      chrome.tabs.sendMessage(tabId, { action: 'captureContent' }, (response) => {
        if (chrome.runtime.lastError) {
          console.error('发送消息错误:', chrome.runtime.lastError);
          sendResponse({ success: false, error: chrome.runtime.lastError.message });
          return;
        }

        if (response && response.success) {
          sendResponse({
            success: true,
            content: response.content
          });
        } else {
          sendResponse({ success: false, error: '获取内容失败' });
        }
      });
    });

    return true; // 表示将异步发送响应
  }

  // 处理打开侧边栏的请求
  if (message.action === 'openSidePanel') {
    chrome.tabs.query({ active: true, currentWindow: true }, async (tabs) => {
      if (tabs.length > 0) {
        await chrome.sidePanel.open({ tabId: tabs[0].id });
        sendResponse({ success: true });
      } else {
        sendResponse({ success: false, error: '无法获取当前标签页' });
      }
    });
    return true; // 表示将异步发送响应
  }

  // 处理发送内容到侧边栏的请求
  if (message.action === 'sendContentToSidePanel') {
    // 这个消息会被侧边栏接收并处理
    sendResponse({ success: true });
    return true;
  }
});

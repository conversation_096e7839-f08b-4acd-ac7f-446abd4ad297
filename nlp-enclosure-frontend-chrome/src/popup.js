// 不再需要导入配置，因为使用侧边栏

// 获取DOM元素
const openAIPortalButton = document.getElementById('openAIPortal');
const captureContentButton = document.getElementById('captureContent');
const statusDiv = document.getElementById('status');

// 获取AI门户URL - 现在使用侧边栏，不再需要这个函数

// 打开AI门户
openAIPortalButton.addEventListener('click', async () => {
  try {
    showStatus('正在打开AI门户...', 'info');

    // 向background.js发送消息，请求打开侧边栏
    chrome.runtime.sendMessage({ action: 'openSidePanel' }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('发送消息错误:', chrome.runtime.lastError);
        showStatus(`错误: ${chrome.runtime.lastError.message}`, 'error');
        return;
      }

      if (response && response.success) {
        showStatus('AI门户已打开', 'success');
      } else {
        showStatus('打开AI门户失败', 'error');
      }
    });
  } catch (error) {
    console.error('打开AI门户错误:', error);
    showStatus(`错误: ${error.message}`, 'error');
  }
});

// 获取当前页面内容
captureContentButton.addEventListener('click', async () => {
  try {
    showStatus('正在获取内容...', 'info');
    console.log('点击获取内容按钮');

    // 获取当前活动标签页
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs.length === 0) {
        showStatus('无法获取当前标签页', 'error');
        return;
      }

      const activeTab = tabs[0];
      console.log('当前标签页:', activeTab.url);

      // 向content script发送消息，请求获取页面内容
      chrome.tabs.sendMessage(activeTab.id, { action: 'captureContent' }, async (response) => {
        if (chrome.runtime.lastError) {
          console.error('发送消息错误:', chrome.runtime.lastError);
          showStatus(`错误: ${chrome.runtime.lastError.message}`, 'error');
          return;
        }

        if (response && response.success) {
          console.log('获取内容成功，长度:', response.content.length);
          showStatus('内容已获取', 'success');

          // 先打开侧边栏
          chrome.runtime.sendMessage({ action: 'openSidePanel' }, async (_openResponse) => {
            if (chrome.runtime.lastError) {
              console.error('打开侧边栏错误:', chrome.runtime.lastError);
              showStatus(`错误: ${chrome.runtime.lastError.message}`, 'error');
              return;
            }

            // 等待侧边栏打开
            setTimeout(() => {
              // 向侧边栏发送内容
              chrome.runtime.sendMessage({
                action: 'sendContentToSidePanel',
                pageUrl: activeTab.url,
                pageTitle: activeTab.title,
                content: response.content
              }, (sendResponse) => {
                if (chrome.runtime.lastError) {
                  console.error('发送内容错误:', chrome.runtime.lastError);
                  showStatus(`错误: ${chrome.runtime.lastError.message}`, 'error');
                } else if (sendResponse && sendResponse.success) {
                  console.log('内容已发送到AI门户');
                }
              });
            }, 500); // 给侧边栏一些时间加载
          });
        } else {
          console.error('获取内容失败:', response ? response.error : '无响应');
          showStatus('获取内容失败', 'error');
        }
      });
    });
  } catch (error) {
    console.error('捕获内容错误:', error);
    showStatus(`错误: ${error.message}`, 'error');
  }
});

// 显示状态信息
function showStatus(message, type = '') {
  statusDiv.textContent = message;
  statusDiv.className = type;

  // 3秒后清除状态
  setTimeout(() => {
    statusDiv.textContent = '';
    statusDiv.className = '';
  }, 3000);
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  // 检查是否已配置AI门户URL
  chrome.storage.sync.get('aiPortalUrl', (data) => {
    if (!data.aiPortalUrl) {
      showStatus('请在选项页面配置AI门户URL', 'error');
    }
  });
});

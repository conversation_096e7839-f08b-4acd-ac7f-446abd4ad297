/**
 * 插件配置文件
 * 集中管理所有配置项
 */

// 默认AI门户URL
const DEFAULT_AI_PORTAL_URL = 'https://ai.gz-tobacco.net/index.html#/chromeApp';
// const DEFAULT_AI_PORTAL_URL = 'http://localhost:5173/index.html#/chromeApp';

/**
 * 生成带有随机时间戳参数的URL，用于强制刷新内容
 * @param {string} url - 原始URL
 * @returns {string} - 带有时间戳参数的URL
 */
function addTimestampToUrl(url) {
  // 生成随机数和时间戳的组合，确保每次都不同
  const timestamp = new Date().getTime();
  const random = Math.floor(Math.random() * 1000000);
  const cacheBuster = `_t=${timestamp}_${random}`;

  // 判断原始URL是否已包含参数
  if (url.includes('?')) {
    // 如果已有参数，添加新参数
    return `${url}&${cacheBuster}`;
  } else if (url.includes('#')) {
    // 如果有锚点但没有参数，在锚点前添加参数
    const [baseUrl, hash] = url.split('#');
    return `${baseUrl}?${cacheBuster}#${hash}`;
  } else {
    // 如果没有参数也没有锚点，直接添加参数
    return `${url}?${cacheBuster}`;
  }
}

// 导出配置和工具函数
export { DEFAULT_AI_PORTAL_URL, addTimestampToUrl };

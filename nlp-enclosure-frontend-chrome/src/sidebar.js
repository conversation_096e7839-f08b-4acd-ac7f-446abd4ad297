// 侧边栏控制脚本
import { DEFAULT_AI_PORTAL_URL, addTimestampToUrl } from './config.js';

// 侧边栏状态
let sidebarVisible = false;
let sidebarIframe = null;
let sidebarContainer = null;
let toolbarContainer = null;

// 创建侧边栏
function createSidebar(url) {
  // 如果已经存在，则先移除
  removeSidebar();

  // 创建容器
  sidebarContainer = document.createElement('div');
  sidebarContainer.id = 'ai-portal-sidebar-container';

  // 创建工具栏
  toolbarContainer = document.createElement('div');
  toolbarContainer.id = 'ai-portal-toolbar';

  // 创建标题
  const title = document.createElement('div');
  title.textContent = 'AI门户';
  title.style.fontWeight = 'bold';
  title.style.fontSize = '14px';

  // 创建按钮容器
  const buttonsContainer = document.createElement('div');
  buttonsContainer.style.display = 'flex';
  buttonsContainer.style.gap = '8px';

  // 创建配置按钮
  const configButton = document.createElement('button');
  configButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>';
  configButton.title = '配置';
  configButton.addEventListener('click', openConfigPanel);

  // 创建关闭按钮
  const closeButton = document.createElement('button');
  closeButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';
  closeButton.title = '关闭';
  closeButton.addEventListener('click', toggleSidebar);

  // 添加按钮到按钮容器
  buttonsContainer.appendChild(configButton);
  buttonsContainer.appendChild(closeButton);

  // 添加标题和按钮到工具栏
  toolbarContainer.appendChild(title);
  toolbarContainer.appendChild(buttonsContainer);

  // 创建iframe
  sidebarIframe = document.createElement('iframe');
  sidebarIframe.id = 'ai-portal-iframe';
  // 添加随机时间戳参数避免缓存
  sidebarIframe.src = addTimestampToUrl(url);
  console.log('加载侧边栏URL:', sidebarIframe.src);
  sidebarIframe.style.cssText = `
    flex: 1;
    border: none;
    width: 100%;
    height: 100%;
  `;

  // 添加工具栏和iframe到容器
  sidebarContainer.appendChild(toolbarContainer);
  sidebarContainer.appendChild(sidebarIframe);

  // 添加到页面
  document.body.appendChild(sidebarContainer);

  // 显示侧边栏
  setTimeout(() => {
    sidebarContainer.style.transform = 'translateX(0)';
    sidebarVisible = true;
  }, 50);

  // 添加消息监听
  window.addEventListener('message', handleIframeMessage);
}

// 移除侧边栏
function removeSidebar() {
  if (sidebarContainer) {
    // 移除消息监听
    window.removeEventListener('message', handleIframeMessage);

    // 移除DOM元素
    document.body.removeChild(sidebarContainer);
    sidebarContainer = null;
    sidebarIframe = null;
    toolbarContainer = null;
    sidebarVisible = false;
  }
}

// 切换侧边栏显示/隐藏
function toggleSidebar() {
  if (sidebarVisible && sidebarContainer) {
    sidebarContainer.style.transform = 'translateX(100%)';
    setTimeout(() => {
      removeSidebar();
    }, 300); // 等待动画完成
  } else {
    chrome.storage.sync.get('aiPortalUrl', (data) => {
      const url = data.aiPortalUrl || DEFAULT_AI_PORTAL_URL;
      console.log('切换侧边栏，加载URL:', url);
      createSidebar(url);
    });
  }
}

// 打开配置面板
function openConfigPanel() {
  // 创建配置面板容器
  const configPanelContainer = document.createElement('div');
  configPanelContainer.id = 'ai-portal-config-panel';
  configPanelContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    justify-content: center;
    align-items: center;
  `;

  // 创建配置面板
  const configPanel = document.createElement('div');
  configPanel.style.cssText = `
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 500px;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    padding: 20px;
  `;

  // 创建配置面板标题
  const configTitle = document.createElement('h2');
  configTitle.textContent = 'AI门户设置';
  configTitle.style.cssText = `
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 18px;
    color: #333;
  `;

  // 创建URL设置区域
  const urlSettingContainer = document.createElement('div');
  urlSettingContainer.style.cssText = `
    margin-bottom: 20px;
  `;

  const urlLabel = document.createElement('label');
  urlLabel.textContent = 'AI门户URL:';
  urlLabel.style.cssText = `
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
  `;

  const urlInput = document.createElement('input');
  urlInput.type = 'url';
  urlInput.id = 'config-ai-portal-url';
  urlInput.style.cssText = `
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    margin-bottom: 8px;
  `;

  const urlDescription = document.createElement('p');
  urlDescription.textContent = '输入AI门户的完整URL地址';
  urlDescription.style.cssText = `
    margin: 0;
    font-size: 12px;
    color: #777;
  `;

  // 添加URL设置元素
  urlSettingContainer.appendChild(urlLabel);
  urlSettingContainer.appendChild(urlInput);
  urlSettingContainer.appendChild(urlDescription);

  // 创建按钮区域
  const buttonsContainer = document.createElement('div');
  buttonsContainer.style.cssText = `
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  `;

  // 创建保存按钮
  const saveButton = document.createElement('button');
  saveButton.textContent = '保存设置';
  saveButton.style.cssText = `
    padding: 8px 16px;
    background-color: #4285f4;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  `;

  // 创建取消按钮
  const cancelButton = document.createElement('button');
  cancelButton.textContent = '取消';
  cancelButton.style.cssText = `
    padding: 8px 16px;
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  `;

  // 添加按钮到按钮区域
  buttonsContainer.appendChild(cancelButton);
  buttonsContainer.appendChild(saveButton);

  // 添加所有元素到配置面板
  configPanel.appendChild(configTitle);
  configPanel.appendChild(urlSettingContainer);
  configPanel.appendChild(buttonsContainer);

  // 添加配置面板到容器
  configPanelContainer.appendChild(configPanel);

  // 添加到页面
  document.body.appendChild(configPanelContainer);

  // 加载当前设置
  chrome.storage.sync.get('aiPortalUrl', (data) => {
    urlInput.value = data.aiPortalUrl || DEFAULT_AI_PORTAL_URL;
  });

  // 添加事件监听
  saveButton.addEventListener('click', () => {
    const newUrl = urlInput.value.trim();
    if (isValidUrl(newUrl)) {
      chrome.storage.sync.set({ aiPortalUrl: newUrl }, () => {
        // 关闭配置面板
        document.body.removeChild(configPanelContainer);

        // 如果侧边栏已打开，则重新加载
        if (sidebarVisible && sidebarIframe) {
          // 添加随机时间戳参数避免缓存
          sidebarIframe.src = addTimestampToUrl(newUrl);
          console.log('配置更新后重新加载URL:', sidebarIframe.src);
        }
      });
    } else {
      alert('请输入有效的URL地址');
    }
  });

  cancelButton.addEventListener('click', () => {
    document.body.removeChild(configPanelContainer);
  });

  // 点击背景关闭配置面板
  configPanelContainer.addEventListener('click', (event) => {
    if (event.target === configPanelContainer) {
      document.body.removeChild(configPanelContainer);
    }
  });
}

// 验证URL格式
function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
}

// 处理来自iframe的消息
function handleIframeMessage(event) {
  // 验证消息来源
  if (sidebarIframe && event.source === sidebarIframe.contentWindow) {
    // 处理消息
    const data = event.data;

    if (data.type === 'getActiveTabInfo') {
      // 获取当前标签页信息
      chrome.runtime.sendMessage({ action: 'getActiveTabInfo' }, (response) => {
        if (response && response.success) {
          // 向iframe发送标签页信息
          sidebarIframe.contentWindow.postMessage({
            type: 'activeTabInfo',
            tabId: response.tabId,
            url: response.url,
            title: response.title
          }, '*');
        }
      });
    }

    if (data.type === 'captureContent') {
      // 获取页面内容
      chrome.runtime.sendMessage({
        action: 'captureContent',
        tabId: data.tabId || null
      }, (response) => {
        if (response && response.success) {
          // 向iframe发送内容
          sidebarIframe.contentWindow.postMessage({
            type: 'capturedContent',
            requestId: data.requestId,
            content: response.content
          }, '*');
        }
      });
    }

    if (data.type === 'closeSidebar') {
      // 关闭侧边栏
      toggleSidebar();
    }
  }
}

// 监听来自background的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  if (message.action === 'toggleSidebar') {
    toggleSidebar();
    sendResponse({ success: true });
    return true;
  }

  if (message.action === 'sendContentToSidebar') {
    if (sidebarIframe && sidebarVisible) {
      sidebarIframe.contentWindow.postMessage({
        type: 'pageContent',
        pageUrl: message.pageUrl,
        pageTitle: message.pageTitle,
        content: message.content
      }, '*');
      sendResponse({ success: true });
    } else {
      // 如果侧边栏未打开，则先打开
      toggleSidebar();

      // 等待侧边栏加载完成
      setTimeout(() => {
        if (sidebarIframe) {
          sidebarIframe.contentWindow.postMessage({
            type: 'pageContent',
            pageUrl: message.pageUrl,
            pageTitle: message.pageTitle,
            content: message.content
          }, '*');
        }
        sendResponse({ success: true });
      }, 1000);
    }
    return true;
  }
});

// 导出函数
export { toggleSidebar };

// 导入配置
import { DEFAULT_AI_PORTAL_URL } from './config.js';

// 获取DOM元素
const aiPortalUrlInput = document.getElementById('aiPortalUrl');
const saveButton = document.getElementById('saveButton');
const resetButton = document.getElementById('resetButton');
const statusDiv = document.getElementById('status');

// 默认设置
const defaultSettings = {
  aiPortalUrl: DEFAULT_AI_PORTAL_URL
};

// 加载保存的设置
function loadSettings() {
  chrome.storage.sync.get('aiPortalUrl', (data) => {
    aiPortalUrlInput.value = data.aiPortalUrl || defaultSettings.aiPortalUrl;
  });
}

// 保存设置
function saveSettings() {
  const aiPortalUrl = aiPortalUrlInput.value.trim();

  // 验证URL格式
  if (!isValidUrl(aiPortalUrl)) {
    showStatus('请输入有效的URL地址', 'error');
    return;
  }

  // 保存到Chrome存储
  chrome.storage.sync.set({ aiPortalUrl }, () => {
    showStatus('设置已保存', 'success');
  });
}

// 重置为默认设置
function resetSettings() {
  aiPortalUrlInput.value = defaultSettings.aiPortalUrl;

  // 保存到Chrome存储
  chrome.storage.sync.set(defaultSettings, () => {
    showStatus('设置已重置为默认值', 'success');
  });
}

// 验证URL格式
function isValidUrl(url) {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
}

// 显示状态信息
function showStatus(message, type = '') {
  statusDiv.textContent = message;
  statusDiv.className = type;

  // 3秒后清除状态
  setTimeout(() => {
    statusDiv.textContent = '';
    statusDiv.className = '';
  }, 3000);
}

// 添加事件监听器
saveButton.addEventListener('click', saveSettings);
resetButton.addEventListener('click', resetSettings);

// 初始化
document.addEventListener('DOMContentLoaded', loadSettings);

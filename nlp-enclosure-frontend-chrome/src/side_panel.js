// 导入配置
import { DEFAULT_AI_PORTAL_URL, addTimestampToUrl } from './config.js';

// 获取DOM元素
const configButton = document.getElementById('configButton');
const refreshButton = document.getElementById('refreshButton');
const contentIframe = document.getElementById('content');

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
  // 加载AI门户URL
  const aiPortalUrl = await getAIPortalUrl();

  // 添加iframe加载事件监听
  contentIframe.addEventListener('load', () => {
    console.log('AI门户iframe加载完成');
  });

  // 添加iframe错误事件监听
  contentIframe.addEventListener('error', (e) => {
    console.error('AI门户iframe加载失败:', e);
  });

  // 设置iframe的src，添加随机时间戳参数避免缓存
  contentIframe.src = addTimestampToUrl(aiPortalUrl);
  console.log('初始加载AI门户URL:', contentIframe.src);

  // 添加按钮事件监听
  configButton.addEventListener('click', openConfigPanel);
  refreshButton.addEventListener('click', () => {
    console.log('点击刷新按钮');
    refreshContent();
  });

  // 监听来自iframe的消息
  window.addEventListener('message', handleIframeMessage);

  // 监听标签页切换事件
  chrome.tabs.onActivated.addListener(handleTabChange);
});

// 获取AI门户URL
async function getAIPortalUrl() {
  return new Promise((resolve) => {
    chrome.storage.sync.get('aiPortalUrl', (data) => {
      resolve(data.aiPortalUrl || DEFAULT_AI_PORTAL_URL);
    });
  });
}

// 刷新内容
async function refreshContent() {
  const aiPortalUrl = await getAIPortalUrl();

  // 使用addTimestampToUrl函数添加随机时间戳参数强制刷新
  const refreshUrl = addTimestampToUrl(aiPortalUrl);

  // 先清空iframe内容，再设置新的URL
  contentIframe.src = 'about:blank';

  // 使用setTimeout确保about:blank加载完成后再设置新URL
  setTimeout(() => {
    contentIframe.src = refreshUrl;
    console.log('刷新AI门户页面:', refreshUrl);
  }, 50);
}

// 打开配置面板
function openConfigPanel() {
  chrome.runtime.openOptionsPage();
}

// 处理来自iframe的消息
function handleIframeMessage(event) {
  // 验证消息来源
  if (event.source === contentIframe.contentWindow) {
    const data = event.data;

    // 处理不同类型的消息
    if (data.type === 'getActiveTabInfo') {
      getActiveTabInfo();
    }

    if (data.type === 'captureContent') {
      captureContent(data.tabId, data.requestId);
    }
  }
}

// 获取当前标签页信息
async function getActiveTabInfo() {
  try {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tabs.length > 0) {
      const activeTab = tabs[0];

      // 向iframe发送标签页信息
      contentIframe.contentWindow.postMessage({
        type: 'activeTabInfo',
        tabId: activeTab.id,
        url: activeTab.url,
        title: activeTab.title
      }, '*');
    }
  } catch (error) {
    console.error('获取标签页信息失败:', error);
  }
}

// 捕获页面内容
async function captureContent(tabId, requestId) {
  try {
    // 如果没有指定tabId，则使用当前活动标签页
    if (!tabId) {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        tabId = tabs[0].id;
      } else {
        throw new Error('无法获取当前标签页');
      }
    }

    // 向content script发送消息，请求获取页面内容
    chrome.tabs.sendMessage(tabId, { action: 'captureContent' }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('发送消息错误:', chrome.runtime.lastError);
        return;
      }

      if (response && response.success) {
        // 向iframe发送内容
        contentIframe.contentWindow.postMessage({
          type: 'capturedContent',
          requestId: requestId,
          content: response.content
        }, '*');
      }
    });
  } catch (error) {
    console.error('捕获内容失败:', error);
  }
}

// 处理标签页切换事件
async function handleTabChange(activeInfo) {
  try {
    // 直接使用activeInfo中的tabId获取标签页信息
    const tab = await chrome.tabs.get(activeInfo.tabId);

    // 当标签页切换时，重置侧边栏内容
    // 先刷新AI门户页面，清除之前的内容
    await refreshContent();

    // 等待一小段时间确保页面已加载，再发送消息
    setTimeout(() => {
      try {
        // 向iframe发送标签页切换消息
        contentIframe.contentWindow.postMessage({
          type: 'tabChanged',
          tabId: tab.id,
          url: tab.url,
          title: tab.title
        }, '*');
        console.log('发送标签页切换消息到AI门户');
      } catch (err) {
        console.error('发送标签页切换消息失败:', err);
      }
    }, 300);

    console.log('标签页切换到:', tab.title, tab.url);
  } catch (error) {
    console.error('处理标签页切换事件失败:', error);
  }
}

// 监听来自background的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  if (message.action === 'sendContentToSidePanel') {
    try {
      // 确保在发送消息前先检查iframe是否已加载
      if (contentIframe.contentWindow) {
        // 向iframe发送内容
        contentIframe.contentWindow.postMessage({
          type: 'pageContent',
          pageUrl: message.pageUrl,
          pageTitle: message.pageTitle,
          content: message.content
        }, '*');
        console.log('已将页面内容发送到AI门户');
        sendResponse({ success: true });
      } else {
        // 如果iframe还没准备好，等待一会再试
        setTimeout(() => {
          try {
            contentIframe.contentWindow.postMessage({
              type: 'pageContent',
              pageUrl: message.pageUrl,
              pageTitle: message.pageTitle,
              content: message.content
            }, '*');
            console.log('延迟发送页面内容到AI门户');
          } catch (err) {
            console.error('发送页面内容失败:', err);
          }
        }, 500);
        sendResponse({ success: true, delayed: true });
      }
    } catch (error) {
      console.error('处理sendContentToSidePanel消息失败:', error);
      sendResponse({ success: false, error: error.message });
    }
    return true;
  }
});

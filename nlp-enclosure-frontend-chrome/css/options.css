body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f5f5;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h1 {
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.option-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

input[type="url"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.description {
  font-size: 12px;
  color: #777;
  margin-top: 5px;
}

.buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

button {
  padding: 8px 16px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  flex: 1;
}

button:hover {
  background-color: #3367d6;
}

#resetButton {
  background-color: #f44336;
}

#resetButton:hover {
  background-color: #d32f2f;
}

#status {
  margin-top: 20px;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

#status.success {
  background-color: #e8f5e9;
  color: #2e7d32;
}

#status.error {
  background-color: #ffebee;
  color: #c62828;
}

# Chrome AI插件

这是一个Chrome浏览器插件，用于连接AI门户页面，实现网页内容获取和交互功能。

## 功能介绍

1. **打开AI门户**：点击插件图标，可以在浏览器右侧展示一个外部的AI门户页面。
2. **获取网页内容**：可以获取当前浏览器访问的网页地址及内容，并将HTML转换为Markdown格式。获取的内容会自动复制到剪贴板中。
3. **双向通信**：AI门户页面可以触发插件方法，插件也可以调用AI门户页面方法，实现双向交互。

## 技术栈

- **前端**：HTML, CSS, JavaScript (原生)
- **Chrome扩展API**：
  - `chrome.storage` - 用于存储配置信息
  - `chrome.tabs` - 用于获取和操作标签页
  - `chrome.runtime` - 用于消息通信
  - `chrome.scripting` - 用于注入和执行脚本
  - `chrome.contextMenus` - 用于创建右键菜单
  - `chrome.sidePanel` - 用于在浏览器右侧显示侧边栏
  - `chrome.clipboardWrite` - 用于写入剪贴板
- **通信机制**：
  - 使用`postMessage` API实现插件与AI门户页面的通信
  - 使用Chrome扩展的消息传递API实现插件内部组件间的通信

## 项目结构

```
nlp-enclosure-chrome-extension/
│
├── manifest.json           # 插件配置文件
├── popup.html              # 点击插件图标显示的弹出页面
├── background.js           # 后台脚本，处理插件生命周期和通信
├── content.js              # 内容脚本，注入到网页中获取内容
├── options.html            # 插件选项页面
├── side_panel.html         # 侧边栏页面
│
├── css/                    # 样式文件
│   ├── popup.css           # 弹出页面样式
│   └── options.css         # 选项页面样式
│
├── src/                    # JavaScript源文件
│   ├── popup.js            # 弹出页面脚本
│   ├── options.js          # 选项页面脚本
│   ├── side_panel.js       # 侧边栏脚本
│   └── config.js           # 全局配置文件
│
└── images/                 # 图标和图片资源
    ├── icon16.png          # 16x16 图标
    ├── icon48.png          # 48x48 图标
    └── icon128.png         # 128x128 图标
```

## 安装方法

1. 打开Chrome浏览器，进入扩展程序页面 (`chrome://extensions/`)
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择本项目文件夹

## 使用方法

1. **配置AI门户URL**：
   - 右键点击插件图标，选择"选项"
   - 在选项页面中输入AI门户的URL地址
   - 点击"保存设置"

2. **打开AI门户**：
   - 点击插件图标
   - 在弹出的窗口中点击"打开AI门户"按钮

3. **获取网页内容**：
   - 方式一：点击插件图标，在弹出的窗口中点击"获取当前页面内容"按钮
   - 方式二：右键点击页面，选择"AI门户" > "获取页面内容"
   - 获取的内容会自动复制到剪贴板中，并在右侧侧边栏中显示

## 通信协议

### 插件向AI门户发送的消息格式

```javascript
{
  type: 'pageContent',      // 消息类型
  pageUrl: 'https://...',   // 页面URL
  pageTitle: '页面标题',     // 页面标题
  content: '页面内容...'     // Markdown格式的页面内容
}
```

### AI门户向插件发送的消息格式

```javascript
{
  type: 'getActiveTabInfo',  // 获取当前标签页信息
  // 或
  type: 'captureContent',    // 获取页面内容
  tabId: 123,                // 标签页ID
  requestId: 'uuid'          // 请求ID，用于匹配响应
}
```

## 开发说明

- 插件使用Manifest V3规范开发
- 使用原生JavaScript，无需额外的框架或库
- 通过Chrome扩展API和postMessage实现通信
- 使用DOMParser进行HTML到Markdown的转换
